import os
import sqlite3
import pandas as pd
from datetime import date
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter
import tkinter as tk
from tkinter import filedialog, messagebox

# 添加选择数据库函数
def select_database():
    """让用户选择数据库文件"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    file_path = filedialog.askopenfilename(
        title="选择数据库文件",
        filetypes=[("数据库文件", "*.db"), ("SQLite文件", "*.sqlite"), ("所有文件", "*.*")],
        initialdir=r"C:\Users\<USER>\Desktop\Day Report\database"
    )
    
    if not file_path:
        print("未选择数据库文件，程序将退出。")
        return None
    
    return file_path

def export_all_tables_to_one_excel(db_path, output_path):
    """将数据库中所有表导出到一个Excel文件，每个表作为一个工作表"""
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f"数据库中的表: {tables}")
        
        if not tables:
            print("数据库中没有表！")
            return False
        
        # 创建Excel写入器
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            for table in tables:
                print(f"导出表: {table}")
                
                # 读取表数据
                df = pd.read_sql_query(f"SELECT * FROM {table}", conn)
                
                # 写入到Excel的工作表
                df.to_excel(writer, sheet_name=table[:31], index=False)  # Excel工作表名最长31个字符
                
                print(f"  已添加工作表: {table}")
        
        conn.close()
        print(f"\n所有表已导出到: {output_path}")
        return True
    
    except Exception as e:
        print(f"导出表时出错: {str(e)}")
        return False

def export_table_with_formatting(db_path, table_name, output_path):
    """将指定表导出到Excel并添加格式化"""
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        
        # 读取表数据
        df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
        conn.close()
        
        if df.empty:
            print(f"表 {table_name} 中没有数据")
            return False
        
        # 创建Excel工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = table_name[:31]  # Excel工作表名最长31个字符
        
        # 添加标题
        ws.merge_cells(f'A1:{get_column_letter(len(df.columns))}1')
        title_cell = ws['A1']
        title_cell.value = f"{table_name} 数据表"
        title_cell.font = Font(size=16, bold=True)
        title_cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # 添加表头
        headers = df.columns.tolist()
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=2, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
        
        # 添加数据
        for r_idx, row in enumerate(df.itertuples(index=False), 3):
            for c_idx, value in enumerate(row, 1):
                cell = ws.cell(row=r_idx, column=c_idx)
                cell.value = value
                
                # 设置数字列的对齐方式
                if isinstance(value, (int, float)):
                    cell.alignment = Alignment(horizontal='right')
                else:
                    cell.alignment = Alignment(horizontal='left')
        
        # 调整列宽
        for col in range(1, len(headers) + 1):
            ws.column_dimensions[get_column_letter(col)].width = 15
        
        # 保存文件
        wb.save(output_path)
        print(f"表 {table_name} 已导出到: {output_path}")
        return True
    
    except Exception as e:
        print(f"导出表时出错: {str(e)}")
        return False

def analyze_database(db_path):
    """分析数据库结构并显示信息"""
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f"\n===== 数据库分析 =====")
        print(f"数据库路径: {db_path}")
        print(f"表的数量: {len(tables)}")
        print(f"表名列表: {', '.join(tables)}")
        
        for table in tables:
            print(f"\n----- 表: {table} -----")
            
            # 查询表结构
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            print(f"列数: {len(columns)}")
            print("列结构:")
            for col in columns:
                print(f"  {col[1]} ({col[2]})")
            
            # 查询记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            row_count = cursor.fetchone()[0]
            print(f"记录数: {row_count}")
            
            # 如果有数据，显示一些样例
            if row_count > 0:
                cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                sample_data = cursor.fetchall()
                print("数据样例:")
                column_names = [col[1] for col in columns]
                for i, row in enumerate(sample_data):
                    print(f"  记录 {i+1}:")
                    for j, value in enumerate(row):
                        print(f"    {column_names[j]}: {value}")
        
        conn.close()
        return True
    
    except Exception as e:
        print(f"分析数据库时出错: {str(e)}")
        return False

# 在主程序中修改
if __name__ == "__main__":
    # 让用户选择数据库文件
    db_path = select_database()
    
    if db_path:
        print(f"选择的数据库: {db_path}")
        
        # 检查数据库是否存在
        if not os.path.exists(db_path):
            print(f"警告: 数据库文件 {db_path} 不存在。")
        else:
            # 分析数据库结构
            analyze_database(db_path)
            
            # 导出所有表到一个Excel文件
            output_dir = r"C:\Users\<USER>\Desktop\Day Report\数据库导出"
            os.makedirs(output_dir, exist_ok=True)
            
            # 获取数据库文件名（不含路径和扩展名）
            db_name = os.path.splitext(os.path.basename(db_path))[0]
            output_path = os.path.join(output_dir, f"{db_name}_数据库内容.xlsx")
            
            # 导出数据
            if export_all_tables_to_one_excel(db_path, output_path):
                # 自动打开Excel文件
                os.startfile(output_path)