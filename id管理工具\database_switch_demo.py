# -*- coding: utf-8 -*-
"""
数据库切换功能演示
展示如何使用数据库切换功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import tempfile
import sqlite3

def create_demo_database(path, name):
    """创建演示数据库"""
    conn = sqlite3.connect(path)
    cursor = conn.cursor()
    
    # 创建表结构
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS Equipment_ID (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            STATE TEXT,
            Location TEXT,
            Chair_Serial_No TEXT UNIQUE,
            Company TEXT,
            Import_Date TEXT
        )
    """)
    
    # 插入示例数据
    sample_data = [
        (f"{name}_001", f"{name}位置1", f"{name}公司", "2024-01-01"),
        (f"{name}_002", f"{name}位置2", f"{name}公司", "2024-01-02"),
        (f"{name}_003", f"{name}位置3", f"{name}公司", "2024-01-03"),
    ]
    
    for i, (serial, location, company, date) in enumerate(sample_data, 1):
        cursor.execute("""
            INSERT OR REPLACE INTO Equipment_ID 
            (STATE, Location, Chair_Serial_No, Company, Import_Date)
            VALUES (?, ?, ?, ?, ?)
        """, ("正常", location, serial, company, date))
    
    conn.commit()
    conn.close()
    print(f"✅ 创建演示数据库: {path}")

def demo_database_switch():
    """演示数据库切换功能"""
    print("🎯 数据库切换功能演示")
    print("=" * 50)
    
    # 创建演示窗口
    root = tk.Tk()
    root.title("数据库切换功能演示")
    root.geometry("800x600")
    
    # 主框架
    main_frame = ttk.Frame(root, padding=20)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="🗃️ 数据库切换功能演示", 
                           font=("Segoe UI", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 说明文本
    info_text = tk.Text(main_frame, height=8, width=80, wrap=tk.WORD)
    info_text.pack(pady=(0, 20))
    
    info_content = """
📋 功能说明：
• 数据库切换功能允许您在不同的数据库文件之间快速切换
• 程序会自动记住您最近使用的数据库路径
• 支持创建新数据库和浏览现有数据库
• 切换后会自动刷新界面数据

🎯 演示步骤：
1. 点击"创建演示数据库"按钮创建几个测试数据库
2. 点击"打开数据库管理器"体验切换功能
3. 在数据库管理器中尝试不同的操作

⚠️ 注意：这是演示版本，实际使用时请在主程序中操作
    """
    
    info_text.insert(tk.END, info_content)
    info_text.config(state=tk.DISABLED)
    
    # 当前数据库信息
    current_frame = ttk.LabelFrame(main_frame, text="当前数据库信息", padding=10)
    current_frame.pack(fill=tk.X, pady=(0, 20))
    
    current_db_var = tk.StringVar(value="未选择数据库")
    current_label = ttk.Label(current_frame, textvariable=current_db_var)
    current_label.pack()
    
    # 演示数据库列表
    demo_frame = ttk.LabelFrame(main_frame, text="演示数据库", padding=10)
    demo_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    # 创建列表框
    demo_listbox = tk.Listbox(demo_frame, height=6)
    demo_listbox.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    
    # 演示数据库路径
    temp_dir = tempfile.gettempdir()
    demo_databases = {
        "项目A数据库": os.path.join(temp_dir, "demo_project_a.db"),
        "项目B数据库": os.path.join(temp_dir, "demo_project_b.db"),
        "测试数据库": os.path.join(temp_dir, "demo_test.db"),
    }
    
    def create_demo_databases():
        """创建演示数据库"""
        try:
            for name, path in demo_databases.items():
                create_demo_database(path, name.replace("数据库", ""))
                demo_listbox.insert(tk.END, f"✅ {name}: {path}")
            
            messagebox.showinfo("成功", "演示数据库创建完成！")
            
        except Exception as e:
            messagebox.showerror("错误", f"创建演示数据库失败:\n{e}")
    
    def open_database_manager():
        """打开数据库管理器"""
        try:
            # 检查优化模块是否可用
            try:
                from database_switcher import DatabaseSwitcher
                
                def refresh_callback():
                    # 更新当前数据库显示
                    try:
                        from config_manager import user_preferences
                        current_path = user_preferences.get_current_database()
                        current_db_var.set(f"当前: {os.path.basename(current_path)}")
                    except:
                        current_db_var.set("获取当前数据库失败")
                
                # 创建数据库切换器
                switcher = DatabaseSwitcher(root, refresh_callback)
                switcher.show_database_manager()
                
            except ImportError:
                messagebox.showerror("错误", "数据库切换模块未找到，请确保所有优化模块文件存在")
                
        except Exception as e:
            messagebox.showerror("错误", f"打开数据库管理器失败:\n{e}")
    
    def cleanup_demo_databases():
        """清理演示数据库"""
        try:
            cleaned = 0
            for name, path in demo_databases.items():
                if os.path.exists(path):
                    os.remove(path)
                    cleaned += 1
            
            demo_listbox.delete(0, tk.END)
            current_db_var.set("未选择数据库")
            
            if cleaned > 0:
                messagebox.showinfo("完成", f"已清理 {cleaned} 个演示数据库")
            else:
                messagebox.showinfo("提示", "没有找到需要清理的演示数据库")
                
        except Exception as e:
            messagebox.showerror("错误", f"清理演示数据库失败:\n{e}")
    
    def show_database_info():
        """显示数据库信息"""
        selection = demo_listbox.curselection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一个数据库")
            return
        
        selected_text = demo_listbox.get(selection[0])
        db_name = selected_text.split(":")[0].replace("✅ ", "")
        db_path = demo_databases.get(db_name)
        
        if db_path and os.path.exists(db_path):
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM Equipment_ID")
                count = cursor.fetchone()[0]
                cursor.execute("SELECT Chair_Serial_No FROM Equipment_ID LIMIT 3")
                samples = cursor.fetchall()
                conn.close()
                
                info = f"数据库: {db_name}\n"
                info += f"路径: {db_path}\n"
                info += f"记录数: {count}\n"
                info += f"示例数据: {', '.join([s[0] for s in samples])}"
                
                messagebox.showinfo("数据库信息", info)
                
            except Exception as e:
                messagebox.showerror("错误", f"读取数据库信息失败:\n{e}")
        else:
            messagebox.showerror("错误", "数据库文件不存在")
    
    # 操作按钮
    btn_frame = ttk.Frame(main_frame)
    btn_frame.pack(fill=tk.X)
    
    ttk.Button(btn_frame, text="🆕 创建演示数据库", 
              command=create_demo_databases).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(btn_frame, text="🗃️ 打开数据库管理器", 
              command=open_database_manager).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(btn_frame, text="ℹ️ 查看数据库信息", 
              command=show_database_info).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(btn_frame, text="🗑️ 清理演示数据库", 
              command=cleanup_demo_databases).pack(side=tk.LEFT, padx=(0, 10))
    
    # 关闭按钮
    ttk.Button(btn_frame, text="关闭", 
              command=root.destroy).pack(side=tk.RIGHT)
    
    # 居中显示窗口
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    print("✅ 演示窗口已创建")
    print("📋 请在窗口中体验数据库切换功能")
    
    root.mainloop()

def main():
    """主函数"""
    print("🎯 数据库切换功能演示启动")
    
    try:
        demo_database_switch()
        print("✅ 演示完成")
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
