import sqlite3
import shutil
import os

DB_PATH = r"C:\\Users\\<USER>\\Desktop\\Day Report\\database\\sales_reports.db"
BACKUP_PATH = DB_PATH + ".bak"

# 备份数据库文件
if not os.path.exists(BACKUP_PATH):
    shutil.copyfile(DB_PATH, BACKUP_PATH)
    print(f"已备份数据库到: {BACKUP_PATH}")
else:
    print(f"备份文件已存在: {BACKUP_PATH}")

conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

# 1. 收集所有视图定义
cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='view';")
all_views = cursor.fetchall()
# 2. 删除所有视图
for vname, _ in all_views:
    cursor.execute(f'DROP VIEW IF EXISTS "{vname}";')

# 查找所有包含 Rental 字段的表（排除 *_bak、*_bak_bak 等备份表）
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';")
tables = [row[0] for row in cursor.fetchall() if not row[0].endswith('_bak') and not row[0].endswith('_bak_bak') and not row[0].endswith('_bak_bak_bak')]
rental_tables = []
for table in tables:
    cursor.execute(f"PRAGMA table_info('{table}')")
    columns = cursor.fetchall()
    for col in columns:
        if col[1].lower() == 'rental':
            rental_tables.append((table, columns))
            break

# 先批量备份所有目标表
for table, _ in rental_tables:
    backup_table = table + '_bak'
    cursor.execute(f'CREATE TABLE IF NOT EXISTS "{backup_table}" AS SELECT * FROM "{table}";')

# 再循环迁移表结构
for table, columns in rental_tables:
    print(f"\n处理表: {table}")
    pk_cols = [col for col in columns if col[-1]]
    has_pk = len(pk_cols) > 0
    id_names = [col[1].upper() for col in columns]
    has_id_col = 'ID' in id_names
    new_cols = []
    for col in columns:
        name, ctype, notnull, dflt, pk = col[1], col[2], col[3], col[4], col[5]
        if name.lower() == 'rental':
            ctype = 'TEXT'
        if not has_pk and name.upper() == 'ID':
            pk = 1
        new_cols.append((name, ctype, notnull, dflt, pk))
    if not has_pk and not has_id_col:
        # 仅当原表没有任何形式的 ID 字段时才添加自增大写 ID
        new_cols = [('ID', 'INTEGER', 0, None, 1)] + [(c[0], c[1], c[2], c[3], 0) for c in new_cols]
    col_defs = []
    for name, ctype, notnull, dflt, pk in new_cols:
        line = f'"{name}" {ctype}'
        if pk:
            line += ' PRIMARY KEY'
            if ctype.upper() == 'INTEGER':
                line += ' AUTOINCREMENT'
        if notnull:
            line += ' NOT NULL'
        if dflt is not None:
            line += f' DEFAULT {dflt}'
        col_defs.append(line)
    new_table = table + '_new'
    cursor.execute(f'DROP TABLE IF EXISTS "{new_table}";')
    cursor.execute(f'CREATE TABLE "{new_table}" ({', '.join(col_defs)});')
    old_col_names = [col[1] for col in columns]
    new_col_names = [c[0] for c in new_cols]
    # 只迁移新表和旧表都存在的字段
    common_cols = [c for c in old_col_names if c in new_col_names]
    insert_cols = ','.join([f'"{c}"' for c in common_cols])
    select_cols = ','.join([f'CAST("{c}" AS TEXT) AS "{c}"' if c.lower() == 'rental' else f'"{c}"' for c in common_cols])
    if not has_pk:
        insert_cols = ','.join([f'"{c}"' for c in common_cols if c.upper() != 'ID'])
        select_cols = ','.join([f'CAST("{c}" AS TEXT) AS "{c}"' if c.lower() == 'rental' else f'"{c}"' for c in common_cols if c.upper() != 'ID'])
        sql = f'INSERT INTO "{new_table}" ({insert_cols}) SELECT {select_cols} FROM "{table}";'
    else:
        sql = f'INSERT INTO "{new_table}" ({insert_cols}) SELECT {select_cols} FROM "{table}";'
    cursor.execute(sql)
    cursor.execute(f'DROP TABLE "{table}";')
    cursor.execute(f'ALTER TABLE "{new_table}" RENAME TO "{table}";')
    print(f"表 {table} 已完成 Rental 字段类型更改和主键补充，原表已备份为 {table}_bak")

for vname, vsql in all_views:
    if vsql:
        try:
            cursor.execute(vsql)
        except Exception as e:
            print(f"恢复视图 {vname} 失败: {e}")

conn.commit()
conn.close()
print("\n所有包含 Rental 字段的表已处理完毕。请检查数据和表结构。")