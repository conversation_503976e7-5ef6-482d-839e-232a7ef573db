Transaction Date,Order ID,Refund,报错原因
2025-05-18,603011216,10,处理失败
===== 2025-05-27 16:28:51.674290 =====
temp_refund_20250527162733.xlsx 第1行 处理异常: 'RefundProcessor' object has no attribute 'log_message'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\Day report 3\Refund_process_修复版.py", line 311, in process_refund_file
    dt_obj = datetime.datetime.strptime(transaction_date_for_query, "%Y-%m-%d %H:%M:%S")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\_strptime.py", line 674, in _strptime_datetime
    tt, fraction, gmtoff_fraction = _strptime(data_string, format)
                                    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\_strptime.py", line 453, in _strptime
    raise ValueError("time data %r does not match format %r" %
                     (data_string, format))
ValueError: time data '2025-05-18' does not match format '%Y-%m-%d %H:%M:%S'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\Day report 3\Refund_process_修复版.py", line 319, in process_refund_file
    self.log_message(f"信息: Equipment_ID {order_id} 使用 Transaction Date '{refund_date}' (仅日期) 进行当天匹配。", level='info')
    ^^^^^^^^^^^^^^^^
AttributeError: 'RefundProcessor' object has no attribute 'log_message'

--------------------------------------------------
2025-05-18,603011216,10,处理失败
===== 2025-05-27 17:27:31.124396 =====
temp_refund_20250527172614.xlsx 第1行 处理异常: 'RefundProcessor' object has no attribute 'log_message'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\Day report 3\Refund_process_修复版.py", line 311, in process_refund_file
    dt_obj = datetime.datetime.strptime(transaction_date_for_query, "%Y-%m-%d %H:%M:%S")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\_strptime.py", line 674, in _strptime_datetime
    tt, fraction, gmtoff_fraction = _strptime(data_string, format)
                                    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\_strptime.py", line 453, in _strptime
    raise ValueError("time data %r does not match format %r" %
                     (data_string, format))
ValueError: time data '2025-05-18' does not match format '%Y-%m-%d %H:%M:%S'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\Day report 3\Refund_process_修复版.py", line 319, in process_refund_file
    self.log_message(f"信息: Equipment_ID {order_id} 使用 Transaction Date '{refund_date}' (仅日期) 进行当天匹配。", level='info')
    ^^^^^^^^^^^^^^^^
AttributeError: 'RefundProcessor' object has no attribute 'log_message'

--------------------------------------------------
2024-01-15,TEST001,25,处理失败
===== 2025-06-11 11:13:25.454445 =====
测试退款文件.xlsx 第1行 未找到匹配数据
查询条件:
  Transaction Date: 2024-01-15
  Order ID: TEST001
  Refund Amount: 25.0
原始行数据:
  Transaction Date: 2024-01-15
  Settlement Date: 2024-01-15
  Refund Date: 2024-01-15
  Merchant Ref ID: IOT_TEST
  Transaction ID: TEST_TXN_001
  Channel: Online
  Order ID: TEST001
  Currency: MYR
  Billing: 50
  Actual: 50
  Refund: 25
  MDR: 1.5
  GST: 3
  Status: Completed
  Refund Fee: 0.5
  Quantity: 1
  Reference1: TEST_REF1
  Reference2: TEST_REF2
  PROCESS: 

--------------------------------------------------
2025-06-11,TEST001,25,处理失败
===== 2025-06-11 11:14:17.080049 =====
测试退款文件.xlsx 第1行 未找到匹配数据
查询条件:
  Transaction Date: 2025-06-11
  Order ID: TEST001
  Refund Amount: 25.0
原始行数据:
  Transaction Date: 2025-06-11
  Settlement Date: 2025-06-11
  Refund Date: 2025-06-11
  Merchant Ref ID: IOT_TEST
  Transaction ID: TEST_TXN_001
  Channel: Online
  Order ID: TEST001
  Currency: MYR
  Billing: 50
  Actual: 50
  Refund: 25
  MDR: 1.5
  GST: 3
  Status: Completed
  Refund Fee: 0.5
  Quantity: 1
  Reference1: TEST_REF1
  Reference2: TEST_REF2
  PROCESS: 

--------------------------------------------------
2025-06-11,TEST001,25,处理失败
===== 2025-06-11 11:14:28.809004 =====
测试退款文件.xlsx 第1行 未找到匹配数据
查询条件:
  Transaction Date: 2025-06-11
  Order ID: TEST001
  Refund Amount: 25.0
原始行数据:
  Transaction Date: 2025-06-11
  Settlement Date: 2025-06-11
  Refund Date: 2025-06-11
  Merchant Ref ID: IOT_TEST
  Transaction ID: TEST_TXN_001
  Channel: Online
  Order ID: TEST001
  Currency: MYR
  Billing: 50
  Actual: 50
  Refund: 25
  MDR: 1.5
  GST: 3
  Status: Completed
  Refund Fee: 0.5
  Quantity: 1
  Reference1: TEST_REF1
  Reference2: TEST_REF2
  PROCESS: 

--------------------------------------------------
2025-06-11,TEST001,25,处理失败
===== 2025-06-11 11:18:08.848308 =====
测试退款文件.xlsx 第1行 未找到匹配数据
查询条件:
  Transaction Date: 2025-06-11
  Order ID: TEST001
  Refund Amount: 25.0
原始行数据:
  Transaction Date: 2025-06-11
  Settlement Date: 2025-06-11
  Refund Date: 2025-06-11
  Merchant Ref ID: IOT_TEST
  Transaction ID: TEST_TXN_001
  Channel: Online
  Order ID: TEST001
  Currency: MYR
  Billing: 50
  Actual: 50
  Refund: 25
  MDR: 1.5
  GST: 3
  Status: Completed
  Refund Fee: 0.5
  Quantity: 1
  Reference1: TEST_REF1
  Reference2: TEST_REF2
  PROCESS: 

--------------------------------------------------
2025-06-11,TEST001,25,处理失败
===== 2025-06-11 11:20:27.101490 =====
测试退款文件.xlsx 第1行 未找到匹配数据
查询条件:
  Transaction Date: 2025-06-11
  Order ID: TEST001
  Refund Amount: 25.0
原始行数据:
  Transaction Date: 2025-06-11
  Settlement Date: 2025-06-11
  Refund Date: 2025-06-11
  Merchant Ref ID: IOT_TEST
  Transaction ID: TEST_TXN_001
  Channel: Online
  Order ID: TEST001
  Currency: MYR
  Billing: 50
  Actual: 50
  Refund: 25
  MDR: 1.5
  GST: 3
  Status: Completed
  Refund Fee: 0.5
  Quantity: 1
  Reference1: TEST_REF1
  Reference2: TEST_REF2
  PROCESS: 

--------------------------------------------------
2025-06-11,TEST001,25,处理失败
===== 2025-06-11 11:21:07.836990 =====
测试退款文件.xlsx 第1行 未找到匹配数据
查询条件:
  Transaction Date: 2025-06-11
  Order ID: TEST001
  Refund Amount: 25.0
原始行数据:
  Transaction Date: 2025-06-11
  Settlement Date: 2025-06-11
  Refund Date: 2025-06-11
  Merchant Ref ID: IOT_TEST
  Transaction ID: TEST_TXN_001
  Channel: Online
  Order ID: TEST001
  Currency: MYR
  Billing: 50
  Actual: 50
  Refund: 25
  MDR: 1.5
  GST: 3
  Status: Completed
  Refund Fee: 0.5
  Quantity: 1
  Reference1: TEST_REF1
  Reference2: TEST_REF2
  PROCESS: 

--------------------------------------------------
2025-06-11,TEST001,25,处理失败
===== 2025-06-11 11:21:59.030054 =====
测试退款文件.xlsx 第1行 未找到匹配数据
查询条件:
  Transaction Date: 2025-06-11
  Order ID: TEST001
  Refund Amount: 25.0
原始行数据:
  Transaction Date: 2025-06-11
  Settlement Date: 2025-06-11
  Refund Date: 2025-06-11
  Merchant Ref ID: IOT_TEST
  Transaction ID: TEST_TXN_001
  Channel: Online
  Order ID: TEST001
  Currency: MYR
  Billing: 50
  Actual: 50
  Refund: 25
  MDR: 1.5
  GST: 3
  Status: Completed
  Refund Fee: 0.5
  Quantity: 1
  Reference1: TEST_REF1
  Reference2: TEST_REF2
  PROCESS: 

--------------------------------------------------
2025-06-11,TEST001,25,处理失败
===== 2025-06-11 11:27:39.269882 =====
测试退款文件.xlsx 第1行 处理异常: cannot access local variable 'is_equipment_id_search' where it is not associated with a value
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Day report 3\Refund_process_修复版.py", line 426, in process_refund_file
    if is_equipment_id_search:
       ^^^^^^^^^^^^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'is_equipment_id_search' where it is not associated with a value

--------------------------------------------------
2025-06-11,TEST001,25,处理失败
===== 2025-06-11 11:29:17.863773 =====
测试退款文件.xlsx 第1行 处理异常: cannot access local variable 'exact_query_condition' where it is not associated with a value
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Day report 3\Refund_process_修复版.py", line 465, in process_refund_file
    if exact_query_condition:
       ^^^^^^^^^^^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'exact_query_condition' where it is not associated with a value

--------------------------------------------------
2025-06-11,TEST002,25,处理失败
===== 2025-06-11 11:34:46.123506 =====
测试退款文件_无TransactionID.xlsx 第1行 未找到匹配数据
查询条件:
  Transaction Date: 2025-06-11
  Order ID: TEST002
  Refund Amount: 25.0
原始行数据:
  Transaction Date: 2025-06-11
  Settlement Date: 2025-06-11
  Refund Date: 2025-06-11
  Merchant Ref ID: IOT_TEST_NO_TXN
  Transaction ID: nan
  Channel: Online
  Order ID: TEST002
  Currency: MYR
  Billing: 50
  Actual: 50
  Refund: 25
  MDR: 1.5
  GST: 3
  Status: Completed
  Refund Fee: 0.5
  Quantity: 1
  Reference1: TEST_REF1
  Reference2: TEST_REF2
  PROCESS: 

--------------------------------------------------
2025-06-11,TEST_TRAD,25,处理失败
===== 2025-06-11 11:38:48.253544 =====
测试_传统匹配.xlsx 第1行 未找到匹配数据
查询条件:
  Transaction Date: 2025-06-11
  Order ID: TEST_TRAD
  Refund Amount: 25.0
原始行数据:
  Transaction Date: 2025-06-11
  Settlement Date: 2025-06-11
  Refund Date: 2025-06-11
  Merchant Ref ID: IOT_TRADITIONAL_TEST
  Transaction ID: nan
  Channel: Online
  Order ID: TEST_TRAD
  Currency: MYR
  Billing: 50
  Actual: 50
  Refund: 25
  MDR: 1.5
  GST: 3
  Status: Completed
  Refund Fee: 0.5
  Quantity: 1
  Reference1: TEST_REF1
  Reference2: TEST_REF2
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:19:10.813923 =====
SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第1行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:19:10.840916 =====
SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第2行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:19:10.866168 =====
SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第3行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:19:10.886723 =====
SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第4行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:19:10.911519 =====
SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第5行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:19:10.939591 =====
SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第6行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:19:10.971047 =====
SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第7行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:26:09.345118 =====
SETTLEMENT_REPORT_09062025_IOT.xlsx 第1行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:26:09.430491 =====
SETTLEMENT_REPORT_09062025_IOT.xlsx 第2行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:26:09.499505 =====
SETTLEMENT_REPORT_09062025_IOT.xlsx 第3行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:26:09.567261 =====
SETTLEMENT_REPORT_09062025_IOT.xlsx 第4行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:26:09.636932 =====
SETTLEMENT_REPORT_09062025_IOT.xlsx 第5行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:26:09.705300 =====
SETTLEMENT_REPORT_09062025_IOT.xlsx 第6行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
,,0.0,处理失败
===== 2025-06-12 15:26:09.776248 =====
SETTLEMENT_REPORT_09062025_IOT.xlsx 第7行 未找到匹配数据
查询条件:
  Transaction Date: None
  Order ID: 
  Refund Amount: 0.0
原始行数据:
  Transaction Date: 
  Settlement Date: 
  Refund Date: 
  Merchant Ref ID: 
  Transaction ID: 
  Channel: 
  Order ID: 
  Currency: 
  Billing: 0.0
  Actual: 0.0
  Refund: 0.0
  MDR: 0.0
  GST: 0.0
  Status: 
  Refund Fee: 0.0
  Quantity: 
  Reference1: 
  Reference2: 
  PROCESS: 

--------------------------------------------------
2025-06-08,603011106,5,处理失败
===== 2025-06-12 15:38:30.036840 =====
SETTLEMENT_REPORT_09062025_IOT.xlsx 第1行 未找到匹配数据
查询条件:
  Transaction Date: 2025-06-08
  Order ID: 603011106
  Refund Amount: 5.0
原始行数据:
  Transaction Date: 2025-06-08
  Settlement Date: 2025-06-10
  Refund Date: 2025-06-10
  Merchant Ref ID: zeroiot1
  Transaction ID: 2920303242
  Channel: RPP_DuitNowQR-Offline_MP
  Order ID: 603011106
  Currency: MYR
  Billing: 5
  Actual: 4.78
  Refund: 5
  MDR: 0
  GST: 0
  Status: cancelled
  Refund Fee: 0
  Quantity: 0
  Reference1: 1
  Reference2: 
  PROCESS: 

--------------------------------------------------
2025-06-11,2025061114101932681813364924416,5,处理失败
===== 2025-06-17 16:14:58.844062 =====
SETTLEMENT_REPORT_12062025_IOT.xlsx 第3行 未找到匹配数据
查询条件:
  Transaction Date: 2025-06-11
  Order ID: 2025061114101932681813364924416
  Refund Amount: 5.0
原始行数据:
  Transaction Date: 2025-06-11
  Settlement Date: 2025-06-13
  Refund Date: 2025-06-13
  Merchant Ref ID: zeroiot1
  Transaction ID: 2925766936
  Channel: FPX_MB2U
  Order ID: 2025061114101932681813364924416
  Currency: MYR
  Billing: 5
  Actual: 4.2
  Refund: 5
  MDR: 0
  GST: 0
  Status: cancelled
  Refund Fee: 0
  Quantity: 0
  Reference1: 1
  Reference2: 
  PROCESS: 

--------------------------------------------------
2025-06-11,2025061114081932681293904568320,5,处理失败
===== 2025-06-17 16:14:58.975858 =====
SETTLEMENT_REPORT_12062025_IOT.xlsx 第4行 未找到匹配数据
查询条件:
  Transaction Date: 2025-06-11
  Order ID: 2025061114081932681293904568320
  Refund Amount: 5.0
原始行数据:
  Transaction Date: 2025-06-11
  Settlement Date: 2025-06-13
  Refund Date: 2025-06-13
  Merchant Ref ID: zeroiot1
  Transaction ID: 2925763165
  Channel: FPX_MB2U
  Order ID: 2025061114081932681293904568320
  Currency: MYR
  Billing: 5
  Actual: 4.2
  Refund: 5
  MDR: 0
  GST: 0
  Status: cancelled
  Refund Fee: 0
  Quantity: 0
  Reference1: 1
  Reference2: 
  PROCESS: 

--------------------------------------------------
