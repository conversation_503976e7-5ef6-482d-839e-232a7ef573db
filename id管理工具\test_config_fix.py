# -*- coding: utf-8 -*-
"""
测试Config属性修复
验证所有Config相关的属性访问是否正确
"""

def test_config_imports():
    """测试配置导入"""
    print("🧪 测试配置导入...")
    
    try:
        from constants import Config
        print("  ✅ Config 导入成功")
        
        # 测试各个子类
        print(f"  📁 DB路径: {Config.DB.DEFAULT_DB_PATH}")
        print(f"  🖥️ 主窗口尺寸: {Config.UI.MAIN_WINDOW_SIZE}")
        print(f"  📄 默认页面大小: {Config.UI.DEFAULT_PAGE_SIZE}")
        print(f"  📊 历史记录限制: {Config.UI.OPERATION_HISTORY_LIMIT}")
        
        # 测试方法
        window_size = Config.get_window_size("main")
        print(f"  🎯 窗口尺寸方法: {window_size}")
        
        # 测试列宽
        if hasattr(Config.UI, 'COLUMN_WIDTHS'):
            print(f"  📏 列宽配置: {len(Config.UI.COLUMN_WIDTHS)} 列")
        else:
            print("  ❌ 缺少 COLUMN_WIDTHS")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置导入失败: {e}")
        return False

def test_main_program_config():
    """测试主程序中的配置使用"""
    print("\n🧪 测试主程序配置使用...")
    
    try:
        # 模拟主程序中的配置检查
        from constants import Config
        
        # 测试优化模块启用状态
        OPTIMIZATION_ENABLED = True
        
        # 测试历史记录限制访问
        if OPTIMIZATION_ENABLED and hasattr(Config, 'UI') and hasattr(Config.UI, 'OPERATION_HISTORY_LIMIT'):
            history_limit = Config.UI.OPERATION_HISTORY_LIMIT
            print(f"  ✅ 历史记录限制: {history_limit}")
        else:
            print("  ❌ 无法访问历史记录限制")
            return False
        
        # 测试列宽访问
        if OPTIMIZATION_ENABLED and hasattr(Config, 'UI') and hasattr(Config.UI, 'COLUMN_WIDTHS'):
            column_widths = Config.UI.COLUMN_WIDTHS
            print(f"  ✅ 列宽配置: {len(column_widths)} 列")
        else:
            print("  ❌ 无法访问列宽配置")
            return False
        
        # 测试窗口尺寸访问
        window_size = Config.get_window_size("main")
        print(f"  ✅ 窗口尺寸: {window_size}")
        
        # 测试页面大小访问
        page_size = Config.UI.DEFAULT_PAGE_SIZE
        print(f"  ✅ 默认页面大小: {page_size}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 主程序配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_scenarios():
    """测试错误场景"""
    print("\n🧪 测试错误场景...")
    
    try:
        from constants import Config
        
        # 测试不存在的属性
        try:
            # 这应该不会出错，因为我们已经修复了
            if hasattr(Config, 'database'):
                print("  ❌ Config.database 仍然存在（应该已被移除）")
                return False
            else:
                print("  ✅ Config.database 不存在（正确）")
        except:
            print("  ❌ Config.database 访问出错")
            return False
        
        # 测试正确的属性访问
        try:
            db_constants = Config.DB
            ui_constants = Config.UI
            file_constants = Config.FILE
            print("  ✅ 所有子常量类都可访问")
        except Exception as e:
            print(f"  ❌ 子常量类访问失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 错误场景测试失败: {e}")
        return False

def test_dummy_config():
    """测试兼容性配置"""
    print("\n🧪 测试兼容性配置...")
    
    try:
        # 模拟优化模块禁用的情况
        class DummyConfig:
            def __init__(self):
                self.database = type('obj', (object,), {'DEFAULT_PAGE_SIZE': 50, 'OPERATION_HISTORY_LIMIT': 50})()
                self.columns = type('obj', (object,), {'COLUMN_WIDTHS': {}})()
            def get_window_size(self, window_type="main"):
                return "1400x800"
        
        dummy_config = DummyConfig()
        
        # 测试兼容性访问
        window_size = dummy_config.get_window_size("main")
        print(f"  ✅ 兼容性窗口尺寸: {window_size}")
        
        # 注意：这里我们不测试 dummy_config.database，因为这是旧的错误用法
        
        return True
        
    except Exception as e:
        print(f"  ❌ 兼容性配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔬 Config属性修复验证")
    print("=" * 50)
    
    tests = [
        ("配置导入", test_config_imports),
        ("主程序配置使用", test_main_program_config),
        ("错误场景", test_error_scenarios),
        ("兼容性配置", test_dummy_config),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                failed += 1
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            failed += 1
            print(f"💥 {test_name}测试异常: {e}")
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"  ✅ 通过: {passed}")
    print(f"  ❌ 失败: {failed}")
    print(f"  📈 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有测试通过！")
        print("✅ Config属性访问已修复")
        print("✅ 不再出现 'Config' has no attribute 'database' 错误")
        print("✅ 所有配置访问都使用正确的路径")
        
        print("\n📋 修复总结:")
        print("  • Config.database → Config.UI (历史记录限制)")
        print("  • opt_config.columns → Config.UI (列宽配置)")
        print("  • 保持 Config.get_window_size() 方法不变")
        print("  • 保持 Config.UI.DEFAULT_PAGE_SIZE 不变")
        
    else:
        print("\n⚠️ 部分测试失败，请检查相关问题。")
    
    return failed == 0

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ Config修复验证完成，程序应该不再出现属性错误！")
        else:
            print("\n❌ Config修复验证发现问题，请检查。")
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    finally:
        input("\n按回车键退出...")
