2025-05-09 17:16:25.121572 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\IOT\IOT REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\IOT\已处理\IOT REFUNDING LIST.xlsx
2025-05-09 17:16:36.497188 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\ZERO REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\已处理\ZERO REFUNDING LIST.xlsx
2025-05-15 12:16:01.195582 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\ZERO REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\已处理\ZERO REFUNDING LIST.xlsx
2025-05-16 12:06:26.738124 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\IOT\IOT REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\IOT\已处理\IOT REFUNDING LIST.xlsx
2025-05-16 12:06:41.788674 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\ZERO REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\已处理\ZERO REFUNDING LIST.xlsx
2025-05-19 14:09:30.419416 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\ZERO REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\已处理\ZERO REFUNDING LIST.xlsx
2025-05-20 12:54:01.184721 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\ZERO REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\已处理\ZERO REFUNDING LIST.xlsx
2025-05-22 14:36:25.587546 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\IOT\IOT REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\IOT\已处理\IOT REFUNDING LIST.xlsx
2025-05-22 14:36:39.688181 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\ZERO REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\已处理\ZERO REFUNDING LIST.xlsx
2025-05-23 14:37:15.356786 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\IOT\IOT REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\IOT\已处理\IOT REFUNDING LIST.xlsx
2025-05-23 14:37:27.615689 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\ZERO REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\已处理\ZERO REFUNDING LIST.xlsx
2025-05-26 14:44:11.914668 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\IOT\IOT REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\IOT\已处理\IOT REFUNDING LIST.xlsx
2025-05-26 14:44:22.574092 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\ZERO REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\已处理\ZERO REFUNDING LIST.xlsx
2025-05-29 11:55:20.549339 IOT REFUNDING LIST.xlsx 第1行: 准备操作表 'IOT_Sales', rowid: None, 匹配详情: (None, 'ZERO2', '2025052114341925077758643859456', 'Offline order', 'Finish', 5.0, 5.0, '2025-05-21', '603011134', 'MYTOWN CHERAS(3B)', 'KUALA LUMPUR.', '2025-05-21', None, '14:34:39', None, '2025-05-21 14:34:39', None, None), 订单ID: 603011134, 原始金额: 5.0, 退款金额: 5.0, 计算后新金额: 0.0
2025-05-29 11:55:20.558443 IOT REFUNDING LIST.xlsx 第1行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 11:55:20.665449 IOT REFUNDING LIST.xlsx 第2行: 准备操作表 'IOT_Sales', rowid: None, 匹配详情: (None, 'ZERO2', '2025052114341925077696350056448', 'Offline order', 'Finish', 5.0, 5.0, '2025-05-21', '603011197', 'MYTOWN CHERAS(3B)', 'KUALA LUMPUR.', '2025-05-21', None, '14:34:24', None, '2025-05-21 14:34:24', None, None), 订单ID: 603011197, 原始金额: 5.0, 退款金额: 5.0, 计算后新金额: 0.0
2025-05-29 11:55:20.665753 IOT REFUNDING LIST.xlsx 第2行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 11:55:20.775863 IOT REFUNDING LIST.xlsx 第3行: 准备操作表 'IOT_Sales', rowid: None, 匹配详情: (None, 'ZERO2', '2025052514511926531654344044544', 'Offline order', 'Finish', 5.0, 5.0, '2025-05-25', '603010440', 'BOULEVARD KUCHING-SWK', 'SARAWAK.', '2025-05-25', None, '14:51:54', None, '2025-05-25 14:51:54', None, None), 订单ID: 603010440, 原始金额: 5.0, 退款金额: 5.0, 计算后新金额: 0.0
2025-05-29 11:55:20.777253 IOT REFUNDING LIST.xlsx 第3行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 11:55:20.891441 IOT REFUNDING LIST.xlsx 第4行: 准备操作表 'IOT_Sales', rowid: None, 匹配详情: (None, 'ZERO2', '2025052515071926535579059154944', 'Offline order', 'Finish', 10.0, 10.0, '2025-05-25', '603010375', 'CITYMALL KK(1)', 'SABAH.', '2025-05-25', None, '15:07:29', None, '2025-05-25 15:07:30', None, None), 订单ID: 603010375, 原始金额: 10.0, 退款金额: 10.0, 计算后新金额: 0.0
2025-05-29 11:55:20.891791 IOT REFUNDING LIST.xlsx 第4行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 11:55:21.037530 IOT REFUNDING LIST.xlsx 第5行: 准备操作表 'IOT_Sales', rowid: None, 匹配详情: (None, 'ZERO2', '2025052513301926511085087158272', 'Offline order', 'Finish', 10.0, 10.0, '2025-05-25', '603011176', 'MYTOWN CHERAS(5)', 'KUALA LUMPUR.', '2025-05-25', None, '13:30:10', None, '2025-05-25 13:30:10', None, None), 订单ID: 603011176, 原始金额: 10.0, 退款金额: 10.0, 计算后新金额: 0.0
2025-05-29 11:55:21.037899 IOT REFUNDING LIST.xlsx 第5行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 11:55:21.158764 IOT REFUNDING LIST.xlsx 第6行: 准备操作表 'IOT_Sales', rowid: None, 匹配详情: (None, 'ZERO2', '2025052513301926511085087158272', 'Offline order', 'Finish', 10.0, 10.0, '2025-05-25', '603011176', 'MYTOWN CHERAS(5)', 'KUALA LUMPUR.', '2025-05-25', None, '13:30:10', None, '2025-05-25 13:30:10', None, None), 订单ID: 603011176, 原始金额: 10.0, 退款金额: 10.0, 计算后新金额: 0.0
2025-05-29 11:55:21.159071 IOT REFUNDING LIST.xlsx 第6行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 11:55:34.349287 ZERO REFUNDING LIST.xlsx 第1行: 准备操作表 'ZERO_Sales', rowid: None, 匹配详情: (None, 'ZERO', '2025052521151926628260561088512', 'Offline order', 'Finish', 10.0, 10.0, '2025-05-25', '603010822', 'IMPERIAL MALL MIRI-SWK', 'SARAWAK.', '2025-05-25', None, '21:15:46', None, '2025-05-25 21:15:47', None), 订单ID: 603010822, 原始金额: 10.0, 退款金额: 10.0, 计算后新金额: 0.0
2025-05-29 11:55:34.349702 ZERO REFUNDING LIST.xlsx 第1行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 11:55:34.365410 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\ZERO REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\已处理\ZERO REFUNDING LIST.xlsx
2025-05-29 14:05:17.981556 IOT REFUNDING LIST.xlsx 第1行: 准备操作表 'IOT_Sales', rowid: None, 匹配详情: (None, 'ZERO2', '2025052114341925077758643859456', 'Offline order', 'Finish', 5.0, 5.0, '2025-05-21', '603011134', 'MYTOWN CHERAS(3B)', 'KUALA LUMPUR.', '2025-05-21', None, '14:34:39', None, '2025-05-21 14:34:39', None, None), 订单ID: 603011134, 原始金额: 5.0, 退款金额: 5.0, 计算后新金额: 0.0
2025-05-29 14:05:17.981895 IOT REFUNDING LIST.xlsx 第1行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 14:05:17.987172 操作异常: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\Desktop\\Day Report\\Refunding\\Refunding backup\\refund_fail.csv'
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\Day Report\Refund_process 脚本.py", line 313, in process_refund_file
    self.log_mgr.log_fail(row, f"{filename} 第{idx+1}行: {process_status} 操作未影响任何行. 可能rowid不匹配或记录已被修改/删除.")
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\Day Report\Refund_process 脚本.py", line 80, in log_fail
    with open(self.fail_log, "a", encoding="utf-8") as f:
         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\Desktop\\Day Report\\Refunding\\Refunding backup\\refund_fail.csv'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\Day Report\Refund_process 脚本.py", line 325, in process_refund_file
    self.log_mgr.log_fail(row, f"{filename} 第{idx+1}行 退款操作一般错误: {e}\n{traceback.format_exc()}")
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\Day Report\Refund_process 脚本.py", line 80, in log_fail
    with open(self.fail_log, "a", encoding="utf-8") as f:
         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\Desktop\\Day Report\\Refunding\\Refunding backup\\refund_fail.csv'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\Day Report\Refund_process 脚本.py", line 654, in main
    ok = processor.process_refund_file(plat, fpath)
  File "c:\Users\<USER>\Desktop\Day Report\Refund_process 脚本.py", line 337, in process_refund_file
    self.log_mgr.log_fail(row, f"{filename} 第{idx+1}行 处理异常: {e}\n{traceback.format_exc()}")
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\Day Report\Refund_process 脚本.py", line 80, in log_fail
    with open(self.fail_log, "a", encoding="utf-8") as f:
         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\Desktop\\Day Report\\Refunding\\Refunding backup\\refund_fail.csv'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\Day Report\Refund_process 脚本.py", line 663, in main
    log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, f"{fname} 主流程异常: {e}\n{traceback.format_exc()}")
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\Day Report\Refund_process 脚本.py", line 80, in log_fail
    with open(self.fail_log, "a", encoding="utf-8") as f:
         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
PermissionError: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\Desktop\\Day Report\\Refunding\\Refunding backup\\refund_fail.csv'

2025-05-29 14:20:08.213287 IOT REFUNDING LIST.xlsx 第1行: 准备操作表 'IOT_Sales', rowid: None, 匹配详情: (None, 'ZERO2', '2025052114341925077758643859456', 'Offline order', 'Finish', 5.0, 5.0, '2025-05-21', '603011134', 'MYTOWN CHERAS(3B)', 'KUALA LUMPUR.', '2025-05-21', None, '14:34:39', None, '2025-05-21 14:34:39', None, None), 订单ID: 603011134, 原始金额: 5.0, 退款金额: 5.0, 计算后新金额: 0.0
2025-05-29 14:20:08.213689 IOT REFUNDING LIST.xlsx 第1行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 14:20:08.328395 IOT REFUNDING LIST.xlsx 第2行: 准备操作表 'IOT_Sales', rowid: None, 匹配详情: (None, 'ZERO2', '2025052114341925077696350056448', 'Offline order', 'Finish', 5.0, 5.0, '2025-05-21', '603011197', 'MYTOWN CHERAS(3B)', 'KUALA LUMPUR.', '2025-05-21', None, '14:34:24', None, '2025-05-21 14:34:24', None, None), 订单ID: 603011197, 原始金额: 5.0, 退款金额: 5.0, 计算后新金额: 0.0
2025-05-29 14:20:08.328774 IOT REFUNDING LIST.xlsx 第2行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 14:20:08.425941 IOT REFUNDING LIST.xlsx 第3行: 准备操作表 'IOT_Sales', rowid: None, 匹配详情: (None, 'ZERO2', '2025052514511926531654344044544', 'Offline order', 'Finish', 5.0, 5.0, '2025-05-25', '603010440', 'BOULEVARD KUCHING-SWK', 'SARAWAK.', '2025-05-25', None, '14:51:54', None, '2025-05-25 14:51:54', None, None), 订单ID: 603010440, 原始金额: 5.0, 退款金额: 5.0, 计算后新金额: 0.0
2025-05-29 14:20:08.426291 IOT REFUNDING LIST.xlsx 第3行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 14:20:08.525139 IOT REFUNDING LIST.xlsx 第4行: 准备操作表 'IOT_Sales', rowid: None, 匹配详情: (None, 'ZERO2', '2025052515071926535579059154944', 'Offline order', 'Finish', 10.0, 10.0, '2025-05-25', '603010375', 'CITYMALL KK(1)', 'SABAH.', '2025-05-25', None, '15:07:29', None, '2025-05-25 15:07:30', None, None), 订单ID: 603010375, 原始金额: 10.0, 退款金额: 10.0, 计算后新金额: 0.0
2025-05-29 14:20:08.525434 IOT REFUNDING LIST.xlsx 第4行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 14:20:08.649017 IOT REFUNDING LIST.xlsx 第5行: 准备操作表 'IOT_Sales', rowid: None, 匹配详情: (None, 'ZERO2', '2025052513301926511085087158272', 'Offline order', 'Finish', 10.0, 10.0, '2025-05-25', '603011176', 'MYTOWN CHERAS(5)', 'KUALA LUMPUR.', '2025-05-25', None, '13:30:10', None, '2025-05-25 13:30:10', None, None), 订单ID: 603011176, 原始金额: 10.0, 退款金额: 10.0, 计算后新金额: 0.0
2025-05-29 14:20:08.649485 IOT REFUNDING LIST.xlsx 第5行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 14:20:08.762545 IOT REFUNDING LIST.xlsx 第6行: 准备操作表 'IOT_Sales', rowid: None, 匹配详情: (None, 'ZERO2', '2025052513301926511085087158272', 'Offline order', 'Finish', 10.0, 10.0, '2025-05-25', '603011176', 'MYTOWN CHERAS(5)', 'KUALA LUMPUR.', '2025-05-25', None, '13:30:10', None, '2025-05-25 13:30:10', None, None), 订单ID: 603011176, 原始金额: 10.0, 退款金额: 10.0, 计算后新金额: 0.0
2025-05-29 14:20:08.762931 IOT REFUNDING LIST.xlsx 第6行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 14:20:22.138392 ZERO REFUNDING LIST.xlsx 第1行: 准备操作表 'ZERO_Sales', rowid: None, 匹配详情: (None, 'ZERO', '2025052521151926628260561088512', 'Offline order', 'Finish', 10.0, 10.0, '2025-05-25', '603010822', 'IMPERIAL MALL MIRI-SWK', 'SARAWAK.', '2025-05-25', None, '21:15:46', None, '2025-05-25 21:15:47', None), 订单ID: 603010822, 原始金额: 10.0, 退款金额: 10.0, 计算后新金额: 0.0
2025-05-29 14:20:22.138855 ZERO REFUNDING LIST.xlsx 第1行: 已删除 操作执行完毕，影响行数: 0
2025-05-29 14:20:22.163523 警告：无法删除源文件 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\ZERO REFUNDING LIST.xlsx，但已成功复制到 C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO\已处理\ZERO REFUNDING LIST.xlsx
