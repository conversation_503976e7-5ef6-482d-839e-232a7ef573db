#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建简单的测试Excel文件
"""

import pandas as pd
import os

# 创建测试数据 - 根据您的实际数据格式
data = {
    'STATE': ['PENANG'],
    'Location': ['PRANGIN MALL'],
    'Quantity': [1],  # 给一个默认值而不是空
    'Chair_Serial_No': ['*********'],
    'Sim_Card_Model': ['Celcom'],
    'SIMCARDID': ['896019210381540476'],
    'Sim_Card_No': ['015-92270267'],
    'Layer': ['2'],
    'Company': ['Test Company'],  # 给一个默认值
    'DATE': ['30/05/2024   01/08/2024 13/06/2030'],
    'Rental': ['280.00']
}

df = pd.DataFrame(data)

# 保存路径
excel_path = "C:/Users/<USER>/Desktop/Day Report/test_data.xlsx"

try:
    df.to_excel(excel_path, index=False)
    print(f"✅ 测试Excel文件已创建: {excel_path}")
    print(f"📊 数据内容:")
    print(df.to_string())
    
    # 验证文件
    test_df = pd.read_excel(excel_path)
    print(f"\n✅ 验证成功，包含 {len(test_df)} 行数据")
    print(f"📋 列名: {list(test_df.columns)}")
    
    # 显示每个字段的值和类型
    print(f"\n📝 详细字段信息:")
    for col in test_df.columns:
        val = test_df[col].iloc[0]
        print(f"  {col}: '{val}' (类型: {type(val).__name__})")
        
except Exception as e:
    print(f"❌ 创建文件失败: {e}")
    
print(f"\n🎯 使用说明:")
print(f"1. 打开ID管理工具")
print(f"2. 选择 文件 -> 导入Excel")
print(f"3. 选择文件: {excel_path}")
print(f"4. 查看导入结果和错误信息")
