# -*- coding: utf-8 -*-
"""
ID管理工具 - 英文文件名版本
解决批处理脚本中文文件名编码问题
"""

# 直接导入并运行原始的ID管理工具
if __name__ == "__main__":
    try:
        # 导入原始模块
        import importlib.util
        import sys
        import os
        
        # 获取原始文件路径
        original_file = os.path.join(os.path.dirname(__file__), "ID管理工具.py")
        
        # 加载原始模块
        spec = importlib.util.spec_from_file_location("id_manager", original_file)
        id_manager = importlib.util.module_from_spec(spec)
        
        # 执行原始模块
        spec.loader.exec_module(id_manager)
        
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
