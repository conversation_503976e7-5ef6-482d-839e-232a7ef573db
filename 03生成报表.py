﻿# -*- coding: utf-8 -*-
import win32com.client as win32
import os
import time

def kill_excel():
    """智能终止Excel进程"""
    os.system('taskkill /IM EXCEL.EXE /F /FI "STATUS eq RUNNING" > NUL 2>&1')
    time.sleep(1)
    excel_running = os.system('tasklist /NH /FI "IMAGENAME eq EXCEL.EXE" 2>NUL | find /I "EXCEL.EXE"') == 0
    if excel_running:
        print("警告：检测到残留Excel进程，请手动关闭！")

def generate_pivot_report(file_path):
    xl = None
    wb = None
    try:
        file_path = os.path.abspath(file_path)
        print(f"🔍 正在处理文件：{file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在：{file_path}")

        print("🛑 正在清理Excel进程...")
        for _ in range(3):
            kill_excel()
            if not os.system('tasklist | find "EXCEL.EXE"'):
                break
            time.sleep(1)

        xl = win32.Dispatch('Excel.Application')
        xl.Visible = False
        xl.DisplayAlerts = False
        xl.AutomationSecurity = 1

        # 打开工作簿（添加连接超时处理）
        for retry in range(3):
            try:
                wb = xl.Workbooks.Open(file_path, ReadOnly=False, IgnoreReadOnlyRecommended=True)
                break
            except Exception as e:
                if retry == 2: raise
                print(f"文件打开失败，重试中... ({retry+1}/3)")
                time.sleep(2)

        # VBA模块管理
        deleted_count = 0
        for i in range(wb.VBProject.VBComponents.Count, 0, -1):
            comp = wb.VBProject.VBComponents.Item(i)
            if comp.Type == 1 and comp.Name.startswith("PivotMacros"):
                wb.VBProject.VBComponents.Remove(comp)
                deleted_count += 1
        print(f"♻️ 已清理 {deleted_count} 个旧模块")

        # 新版VBA代码（关键修改点▼）
        vba_code = u'''
Sub CreateMainPivotTable()
    On Error GoTo ErrorHandler
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Sheets("销售分析")
    Dim pvtCache As PivotCache
    Dim pvtTable As PivotTable
    
    ' 清理现有透视表
    For Each pvtTable In ws.PivotTables
        pvtTable.TableRange2.Clear
    Next pvtTable
    
    ' 创建新透视表
    Set pvtCache = ThisWorkbook.PivotCaches.Create( _
        SourceType:=xlExternal, _
        SourceData:=ThisWorkbook.Model.ModelTables("设备表") _
    )
    Set pvtTable = pvtCache.CreatePivotTable( _
        TableDestination:=ws.Range("A3"), _
        TableName:="主销售分析" _
    )
    
    ' 配置行字段
    With pvtTable
        .CubeFields("[设备表].[省份]").Orientation = xlRowField
        .CubeFields("[设备表].[城市]").Orientation = xlRowField
        
        ' 配置计算字段
        .AddDataField .CubeFields("[指标].[总销售额]"), "总销售额"
        .AddDataField .CubeFields("[指标].[利润]"), "利润"
        .AddDataField .CubeFields("[指标].[单件利润]"), "单件利润"
        
        ' 条件格式
        ApplyProfitConditionalFormat .DataBodyRange
    End With
    
    ws.Columns.AutoFit
    Exit Sub
    
ErrorHandler:
    MsgBox "主分析表生成失败：" & Err.Description, vbCritical
End Sub

Private Sub ApplyProfitConditionalFormat(rng As Range)
    On Error Resume Next
    With rng.FormatConditions
        .Delete
        ' 红色：亏损
        .Add(Type:=xlCellValue, Operator:=xlLess, Formula1:="0").Interior.Color = RGB(255, 199, 206)
        ' 黄色：0-500
        .Add(Type:=xlCellValue, Operator:=xlBetween, Formula1:="0", Formula2:="500").Interior.Color = RGB(255, 235, 156)
        ' 橙色：501-1000
        .Add(Type:=xlCellValue, Operator:=xlBetween, Formula1:="501", Formula2:="1000").Interior.Color = RGB(255, 192, 0)
        ' 绿色：>1000
        .Add(Type:=xlCellValue, Operator:=xlGreater, Formula1:="1000").Interior.Color = RGB(198, 239, 206)
    End With
End Sub

'▼▼▼ 数据模型初始化 ▼▼▼
Private Sub Workbook_Open()
    If Not InitializeDataModel Then
        MsgBox "数据模型初始化失败，请检查数据连接！", vbCritical
    End If
    RefreshAllData
End Sub

Private Function InitializeDataModel() As Boolean
    On Error GoTo ErrorHandler
    Application.EnableEvents = False
    
    ' 验证数据模型
    If ThisWorkbook.Model.ModelTables.Count = 0 Then
        MsgBox "未检测到数据模型，请先建立数据连接！", vbExclamation
        Exit Function
    End If
    
    ' 核心DAX计算字段（关键修改点▼）
    With ThisWorkbook.Model.ModelMeasures
        .Add "总销售额", "=SUMX(销售表, 销售表[数量] * RELATED(设备表[单价]))"
        .Add "总成本", "=SUMX(销售表, 销售表[数量] * RELATED(设备表[成本价]))"
        .Add "利润", "=[总销售额] - [总成本]"
        .Add "单件利润", "=DIVIDE([利润], SUM(销售表[数量]), 0)"  ' 添加零值保护
    End With
    
    ' 创建层次结构
    If Not CreateHierarchy("地区层级", "设备表", Array("省份", "城市")) Then
        Exit Function
    End If
    
    InitializeDataModel = True
    Exit Function
    
ErrorHandler:
    MsgBox "初始化错误：" & Err.Description, vbCritical
    InitializeDataModel = False
End Function
'''

        # 添加VBA模块（关键修改点▼）
        vba_module = wb.VBProject.VBComponents.Add(1)
        vba_module.Name = "PivotMacros"
        vba_module.CodeModule.AddFromString(vba_code)  # 移除了.encode转换

        # 增强编译逻辑
        try:
            xl.VBE.Windows("Project").Visible = True
            xl.VBE.ActiveVBProject.VBComponents("PivotMacros").Activate
            xl.VBE.CommandBars.FindControl(ID:=578).Execute
            print("✅ VBA代码编译成功")
        except Exception as e:
            print(f"⚠️ 编译警告：{str(e)}")

        # 稳健化保存流程
        for i in range(3):
            try:
                time.sleep(2)
                wb.SaveAs(Filename=file_path,
                         FileFormat=52,
                         AccessMode=1,
                         ConflictResolution=2)
                print(f"💾 文件保存{'成功' if i==0 else '重试成功'}（第{i+1}次）")
                break
            except Exception as e:
                if i == 2: raise
                print(f"保存失败，重试中... ({str(e)})")
                time.sleep(2)

        # 执行报表生成
        print("⏳ 正在初始化数据模型...")
        xl.Application.Run(f"'{os.path.basename(file_path)}'!PivotMacros.InitializeDataModel")
        
        print("⏳ 正在生成主分析表...")
        xl.Application.Run(f"'{os.path.basename(file_path)}'!PivotMacros.CreateMainPivotTable")
        
        wb.Save()
        return True

    except Exception as e:
        error_msg = f"""
❌ 错误详情：
- 类型：{type(e).__name__}
- 信息：{str(e)}"""
        if "Permission" in str(e):
            error_msg += "\n🔧 建议：右键文件 → 属性 → 解除锁定"
        print(error_msg)
        return False

    finally:
        try:
            if wb: wb.Close(SaveChanges=False)
            if xl: xl.Quit()
        except: pass
        kill_excel()

if __name__ == "__main__":
    target_file = r"C:\Users\<USER>\Desktop\Day Report\Sales_Report_PowerBI.xlsm"
    
    print("="*50)
    print("📊 智能报表生成器 v4.3")
    print("="*50)
    print("执行前请确保：")
    print("1. 使用记事本另存本文件为UTF-8编码")
    print("2. Excel文件已解除锁定（右键文件 → 属性 → 解除锁定）\n")
    
    for attempt in range(3):
        print(f"\n🔄 尝试第 {attempt+1} 次生成...")
        if generate_pivot_report(target_file):
            print("\n✅ 生成成功！验证步骤：")
            print("1. 打开文件时启用宏")
            print("2. 查看 [销售分析] 工作表的透视表")
            print("3. Alt+F11查看中文VBA代码")
            break
        elif attempt == 2:
            print("\n❌ 多次尝试失败，请检查：")
            print("1. 文件编码是否为UTF-8 with BOM")
            print("2. Power Query中的表名是否匹配")