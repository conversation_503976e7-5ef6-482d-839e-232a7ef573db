# -*- coding: utf-8 -*-
"""
数据库切换功能测试脚本
验证数据库切换功能的逻辑是否正确
"""

import os
import sys
import tempfile
import sqlite3

def test_config_manager():
    """测试配置管理器"""
    print("🧪 测试配置管理器...")
    try:
        from config_manager import config_manager, user_preferences
        
        # 测试获取默认数据库路径
        default_path = user_preferences.get_current_database()
        print(f"  默认数据库路径: {default_path}")
        
        # 测试设置新路径
        test_path = r"C:\temp\test.db"
        user_preferences.set_current_database(test_path)
        
        # 验证路径是否更新
        new_path = user_preferences.get_current_database()
        print(f"  设置后的路径: {new_path}")
        
        if new_path == test_path:
            print("  ✅ 配置管理器测试通过")
            return True
        else:
            print("  ❌ 配置管理器测试失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 配置管理器测试异常: {e}")
        return False

def test_database_switcher():
    """测试数据库切换器"""
    print("🧪 测试数据库切换器...")
    try:
        from database_switcher import DatabaseSwitcher
        
        # 创建临时数据库文件
        temp_dir = tempfile.gettempdir()
        test_db = os.path.join(temp_dir, "test_switch.db")
        
        # 创建测试数据库
        conn = sqlite3.connect(test_db)
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS Equipment_ID (
                ID INTEGER PRIMARY KEY,
                Chair_Serial_No TEXT
            )
        """)
        cursor.execute("INSERT INTO Equipment_ID (Chair_Serial_No) VALUES ('TEST001')")
        conn.commit()
        conn.close()
        
        print(f"  创建测试数据库: {test_db}")
        
        # 测试数据库切换器初始化
        class MockWindow:
            pass
        
        mock_window = MockWindow()
        switcher = DatabaseSwitcher(mock_window)
        
        print("  ✅ 数据库切换器初始化成功")
        
        # 清理测试文件
        if os.path.exists(test_db):
            os.remove(test_db)
            print("  🗑️ 清理测试文件")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库切换器测试异常: {e}")
        return False

def test_database_creation():
    """测试数据库创建功能"""
    print("🧪 测试数据库创建功能...")
    try:
        from database_switcher import DatabaseSwitcher
        
        # 创建临时目录
        temp_dir = tempfile.gettempdir()
        test_db = os.path.join(temp_dir, "test_create.db")
        
        # 确保文件不存在
        if os.path.exists(test_db):
            os.remove(test_db)
        
        # 创建数据库切换器实例
        class MockWindow:
            pass
        
        switcher = DatabaseSwitcher(MockWindow())
        
        # 测试数据库结构创建
        switcher._create_database_structure(test_db)
        
        # 验证数据库是否创建成功
        if os.path.exists(test_db):
            conn = sqlite3.connect(test_db)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            conn.close()
            
            if any('Equipment_ID' in table[0] for table in tables):
                print(f"  ✅ 数据库创建成功，包含 {len(tables)} 个表")
                
                # 清理测试文件
                os.remove(test_db)
                return True
            else:
                print("  ❌ 数据库创建失败，缺少必要表")
                return False
        else:
            print("  ❌ 数据库文件创建失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 数据库创建测试异常: {e}")
        return False

def test_recent_databases():
    """测试最近使用数据库功能"""
    print("🧪 测试最近使用数据库功能...")
    try:
        from config_manager import user_preferences
        
        # 清空最近使用列表
        user_preferences.config_manager.set(user_preferences.preferences_section, 
                                           'recent_databases', '[]')
        
        # 添加测试数据库
        test_dbs = [
            r"C:\test1.db",
            r"C:\test2.db", 
            r"C:\test3.db"
        ]
        
        for db in test_dbs:
            user_preferences.add_recent_database(db)
        
        # 验证最近使用列表
        recent = user_preferences.get_recent_databases()
        
        if len(recent) == 3 and recent[0] == test_dbs[-1]:
            print(f"  ✅ 最近使用数据库功能正常，共 {len(recent)} 项")
            return True
        else:
            print(f"  ❌ 最近使用数据库功能异常，期望3项，实际{len(recent)}项")
            return False
            
    except Exception as e:
        print(f"  ❌ 最近使用数据库测试异常: {e}")
        return False

def test_integration():
    """测试集成功能"""
    print("🧪 测试集成功能...")
    try:
        # 测试主程序导入
        print("  测试主程序导入...")
        import ID管理工具
        
        # 检查是否有数据库管理方法
        if hasattr(ID管理工具.EquipmentManager, 'show_database_manager'):
            print("  ✅ 主程序包含数据库管理方法")
        else:
            print("  ❌ 主程序缺少数据库管理方法")
            return False
        
        if hasattr(ID管理工具.EquipmentManager, 'refresh_after_db_switch'):
            print("  ✅ 主程序包含数据库切换刷新方法")
        else:
            print("  ❌ 主程序缺少数据库切换刷新方法")
            return False
        
        print("  ✅ 集成功能测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 集成功能测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🔬 数据库切换功能测试")
    print("=" * 50)
    
    tests = [
        ("配置管理器", test_config_manager),
        ("数据库切换器", test_database_switcher),
        ("数据库创建", test_database_creation),
        ("最近使用数据库", test_recent_databases),
        ("集成功能", test_integration)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                failed += 1
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            failed += 1
            print(f"💥 {test_name}测试异常: {e}")
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"  ✅ 通过: {passed}")
    print(f"  ❌ 失败: {failed}")
    print(f"  📈 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("🎉 所有测试通过！数据库切换功能正常。")
        print("\n📋 功能验证:")
        print("  ✅ 配置管理器可以保存和读取数据库路径")
        print("  ✅ 数据库切换器可以正常初始化")
        print("  ✅ 可以创建新的数据库文件")
        print("  ✅ 最近使用数据库列表功能正常")
        print("  ✅ 主程序集成功能完整")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    return failed == 0

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 数据库切换功能可以正常使用！")
        else:
            print("\n❌ 数据库切换功能存在问题，请检查。")
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    finally:
        input("\n按回车键退出...")
