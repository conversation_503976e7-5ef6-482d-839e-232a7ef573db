# -*- coding: utf-8 -*-
"""
Excel导入管理器
将原来的超长import_from_excel函数拆分为多个职责单一的函数
"""

import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from typing import Optional, Dict, List, Any, Tuple
from constants import Config
from error_handler import handle_error, ValidationError
from database_connection_manager import get_db_manager


class ExcelImportManager:
    """Excel导入管理器"""
    
    def __init__(self, parent_window, refresh_callback=None):
        self.parent_window = parent_window
        self.refresh_callback = refresh_callback
        self.db_manager = get_db_manager()
        
    def start_import(self):
        """开始导入流程"""
        try:
            # 1. 选择文件
            file_path = self._select_excel_file()
            if not file_path:
                return
            
            # 2. 验证文件
            df = self._validate_excel_file(file_path)
            if df is None:
                return
            
            # 3. 处理导入
            self._process_excel_import(df, file_path)
            
        except Exception as e:
            handle_error(e, "Excel导入")
    
    def _select_excel_file(self) -> Optional[str]:
        """选择Excel文件"""
        return filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=Config.FILE.EXCEL_FILE_TYPES
        )
    
    def _validate_excel_file(self, file_path: str) -> Optional[pd.DataFrame]:
        """验证Excel文件"""
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            # 检查必要字段
            missing_cols = Config.FILE.REQUIRED_EXCEL_COLUMNS - set(df.columns)
            if missing_cols:
                self._show_missing_columns_error(missing_cols, df.columns)
                return None
            
            return df
            
        except Exception as e:
            handle_error(e, "Excel文件验证")
            return None
    
    def _show_missing_columns_error(self, missing_cols: set, existing_cols: List[str]):
        """显示缺少列的错误信息"""
        missing_list = ', '.join(missing_cols)
        error_msg = f"❌ Excel文件缺少必要字段: {missing_list}\n\n"
        error_msg += f"📋 当前Excel文件包含的字段: {', '.join(existing_cols)}\n\n"
        error_msg += f"✅ 必须包含的字段: {', '.join(Config.FILE.REQUIRED_EXCEL_COLUMNS)}\n\n"
        error_msg += f"💡 建议: 请确保Excel文件包含 '{missing_list}' 字段"
        messagebox.showerror("文件验证失败", error_msg)
    
    def _process_excel_import(self, df: pd.DataFrame, file_path: str):
        """处理Excel导入"""
        # 创建导入进度窗口
        import_window = self._create_import_window()
        progress_updater = self._create_progress_updater(import_window)
        
        try:
            # 显示预览信息
            self._show_import_preview(df, file_path, progress_updater)
            
            # 执行导入
            result = self._execute_import(df, progress_updater)
            
            # 显示结果
            self._show_import_result(result, progress_updater)
            
            # 刷新界面
            if self.refresh_callback:
                self.refresh_callback()
                
        except Exception as e:
            handle_error(e, "Excel导入处理")
        finally:
            # 添加完成按钮
            self._add_completion_button(import_window)
    
    def _create_import_window(self) -> tk.Toplevel:
        """创建导入进度窗口"""
        import_win = tk.Toplevel(self.parent_window)
        import_win.title("导入Excel数据")
        import_win.geometry(Config.get_window_size("import"))
        import_win.transient(self.parent_window)
        import_win.grab_set()
        
        main_frame = ttk.Frame(import_win, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(main_frame, text="正在导入Excel数据...", 
                 font=("Arial", 12, "bold")).pack(pady=(0, 10))
        
        # 创建进度显示区域
        progress_frame = ttk.LabelFrame(main_frame, text="导入进度", padding=10)
        progress_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本框显示进度
        progress_text = tk.Text(progress_frame, height=15, width=70)
        progress_text.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(progress_frame, command=progress_text.yview)
        progress_text.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 保存引用
        import_win.progress_text = progress_text
        import_win.main_frame = main_frame
        
        return import_win
    
    def _create_progress_updater(self, import_window: tk.Toplevel):
        """创建进度更新器"""
        def update_progress(message: str):
            import_window.progress_text.insert(tk.END, message + "\n")
            import_window.progress_text.see(tk.END)
            import_window.update()
        
        return update_progress
    
    def _show_import_preview(self, df: pd.DataFrame, file_path: str, progress_updater):
        """显示导入预览"""
        progress_updater(f"✅ Excel文件验证通过")
        progress_updater(f"📋 Excel文件列 ({len(df.columns)}个): {', '.join(df.columns)}")
        progress_updater(f"📊 数据预览 (前3行):")
        
        for i, (_, row) in enumerate(df.head(3).iterrows()):
            row_preview = {k: str(v)[:30] + '...' if len(str(v)) > 30 else str(v) 
                          for k, v in row.items()}
            progress_updater(f"  行{i+1}: {row_preview}")
        
        progress_updater("")
        progress_updater(f"开始导入文件: {file_path}")
        progress_updater(f"共有 {len(df)} 条记录待导入")
    
    def _execute_import(self, df: pd.DataFrame, progress_updater) -> Dict[str, int]:
        """执行导入操作"""
        imported = 0
        failed = 0
        duplicate_count = 0
        
        # 获取数据库字段列表
        db_fields = [field[1] for field in self.db_manager.get_table_info(Config.DB.EQUIPMENT_TABLE)]
        
        for row_idx, row in df.iterrows():
            try:
                result = self._process_single_row(row, row_idx + 1, progress_updater, db_fields)
                
                if result == "imported":
                    imported += 1
                elif result == "duplicate":
                    duplicate_count += 1
                    imported += 1
                else:  # failed
                    failed += 1
                    
            except Exception as e:
                failed += 1
                chair_serial_no = str(row.get("Chair_Serial_No", "")).strip()
                error_detail = self._get_error_detail(e)
                progress_updater(f"❌ 导入失败: {chair_serial_no} - {error_detail}")
        
        return {
            "imported": imported,
            "failed": failed,
            "duplicate_count": duplicate_count
        }
    
    def _process_single_row(self, row: pd.Series, row_number: int, 
                           progress_updater, db_fields: List[str]) -> str:
        """处理单行数据"""
        # 获取序列号
        chair_serial_no = str(row.get("Chair_Serial_No", "")).strip()
        if not chair_serial_no or chair_serial_no == 'nan':
            progress_updater(f"❌ 行{row_number}: 跳过空序列号记录")
            return "failed"
        
        progress_updater(f"🔄 处理行{row_number}: 序列号 {chair_serial_no}")
        
        # 检查必填字段
        missing_fields = self._check_required_fields(row)
        if missing_fields:
            # 这里可以实现智能补全逻辑
            progress_updater(f"⚠️ 行{row_number}: 缺少必填字段 {', '.join(missing_fields)}")
            return "failed"
        
        # 检查重复序列号
        is_duplicate = self._check_duplicate_serial(chair_serial_no)
        
        # 准备数据
        values = self._prepare_row_data(row, db_fields)
        
        # 执行插入
        if is_duplicate:
            # 处理重复序列号的逻辑
            if self._handle_duplicate_serial(chair_serial_no, values, progress_updater):
                return "duplicate"
            else:
                return "failed"
        else:
            # 直接插入
            self._insert_row_data(values)
            progress_updater(f"✅ 成功导入: {chair_serial_no}")
            return "imported"
    
    def _check_required_fields(self, row: pd.Series) -> List[str]:
        """检查必填字段"""
        missing_fields = []
        for field in Config.DB.REQUIRED_FIELDS:
            field_value = str(row.get(field, "")).strip()
            if not field_value or field_value == 'nan':
                missing_fields.append(field)
        return missing_fields
    
    def _check_duplicate_serial(self, chair_serial_no: str) -> bool:
        """检查序列号是否重复"""
        try:
            query = "SELECT COUNT(*) FROM Equipment_ID WHERE Chair_Serial_No=?"
            result = self.db_manager.execute_query(query, (chair_serial_no,), fetch_all=False)
            return result[0] > 0
        except Exception:
            return False
    
    def _prepare_row_data(self, row: pd.Series, db_fields: List[str]) -> List[Any]:
        """准备行数据"""
        def safe_get_value(row, key, default=None):
            """安全获取值，处理pandas的NaN和各种数据格式"""
            value = row.get(key, default)
            
            if pd.isna(value):
                return None
            
            str_value = str(value).strip()
            if str_value == '' or str_value.lower() == 'nan':
                return None
            
            return str_value
        
        # 获取各字段值
        values = []
        for field in db_fields[1:]:  # 跳过ID字段
            if field in ["Import_Date", "Last_Updated"]:
                values.append(self._get_current_timestamp())
            else:
                values.append(safe_get_value(row, field))
        
        return values
    
    def _insert_row_data(self, values: List[Any]):
        """插入行数据"""
        # 构建插入SQL
        columns = Config.DB.EQUIPMENT_COLUMNS[1:]  # 跳过ID
        placeholders = ', '.join('?' * len(columns))
        query = f"INSERT INTO {Config.DB.EQUIPMENT_TABLE} ({', '.join(columns)}) VALUES ({placeholders})"
        
        self.db_manager.execute_update(query, tuple(values))
    
    def _handle_duplicate_serial(self, chair_serial_no: str, values: List[Any], 
                                progress_updater) -> bool:
        """处理重复序列号"""
        progress_updater(f"检测到重复序列号: {chair_serial_no}，请设置生效日期...")
        # 这里可以实现设置生效日期的逻辑
        # 暂时返回False，表示跳过
        progress_updater(f"❌ 跳过重复序列号: {chair_serial_no}")
        return False
    
    def _get_error_detail(self, e: Exception) -> str:
        """获取错误详情"""
        error_msg = str(e)
        if "UNIQUE constraint failed" in error_msg:
            if "Chair_Serial_No" in error_msg:
                return "序列号重复"
            elif "SIMCARDID" in error_msg:
                return "SIM卡ID重复"
            else:
                return "数据重复"
        elif "NOT NULL constraint failed" in error_msg:
            field_name = error_msg.split(".")[-1] if "." in error_msg else "未知字段"
            return f"必填字段 {field_name} 为空"
        else:
            return error_msg
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime(Config.VALIDATION.DATETIME_FORMAT)
    
    def _show_import_result(self, result: Dict[str, int], progress_updater):
        """显示导入结果"""
        imported = result["imported"]
        failed = result["failed"]
        duplicate_count = result["duplicate_count"]
        
        progress_updater(f"导入完成: 成功 {imported} 条，失败 {failed} 条，处理重复序列号 {duplicate_count} 个")
    
    def _add_completion_button(self, import_window: tk.Toplevel):
        """添加完成按钮"""
        ttk.Button(import_window.main_frame, text="完成", 
                  command=import_window.destroy, width=15).pack(pady=10)


# 便捷函数
def import_excel_data(parent_window, refresh_callback=None):
    """便捷的Excel导入函数"""
    importer = ExcelImportManager(parent_window, refresh_callback)
    importer.start_import()
