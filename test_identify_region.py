#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Treeview identify_region 方法的行为
"""

import tkinter as tk
from tkinter import ttk

def test_identify_region():
    """测试 identify_region 方法"""
    root = tk.Tk()
    root.title("测试 identify_region")
    root.geometry("600x400")
    
    # 创建 Treeview
    columns = ["ID", "Name", "Value"]
    tree = ttk.Treeview(root, columns=columns, show="tree headings")
    
    # 设置列标题
    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, width=100)
    
    # 添加一些测试数据
    for i in range(5):
        tree.insert("", "end", values=[i+1, f"Name{i+1}", f"Value{i+1}"])
    
    tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 创建状态标签
    status_label = tk.Label(root, text="点击不同区域查看 identify_region 结果", 
                           font=("Arial", 12), fg="blue")
    status_label.pack(pady=5)
    
    def on_click(event):
        """处理点击事件"""
        region = tree.identify_region(event.x, event.y)
        element = tree.identify_element(event.x, event.y)
        row = tree.identify_row(event.y)
        column = tree.identify_column(event.x)
        
        info = f"Region: {region}, Element: {element}, Row: {row}, Column: {column}"
        status_label.config(text=info)
        print(f"点击位置: ({event.x}, {event.y}) - {info}")
    
    def on_double_click(event):
        """处理双击事件 - 模拟我们的修复逻辑"""
        region = tree.identify_region(event.x, event.y)
        
        if region == "heading":
            print("双击了列标题 - 不执行编辑操作")
            status_label.config(text="双击了列标题 - 不执行编辑操作", fg="red")
        elif region == "cell" or region == "tree":
            print("双击了数据行 - 执行编辑操作")
            status_label.config(text="双击了数据行 - 执行编辑操作", fg="green")
        else:
            print(f"双击了其他区域: {region}")
            status_label.config(text=f"双击了其他区域: {region}", fg="orange")
    
    # 绑定事件
    tree.bind("<Button-1>", on_click)
    tree.bind("<Double-1>", on_double_click)
    
    # 添加说明
    instruction = tk.Label(root, 
                          text="说明：单击查看区域信息，双击测试编辑逻辑\n"
                               "列标题区域应该显示 'heading'，数据行应该显示 'cell' 或 'tree'",
                          font=("Arial", 10), fg="gray")
    instruction.pack(pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    test_identify_region()
