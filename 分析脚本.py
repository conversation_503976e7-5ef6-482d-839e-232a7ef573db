import os
import sqlite3
import pandas as pd
import datetime
import re

# 配置常量
DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
ID_FILE_PATH = r"C:\Users\<USER>\Desktop\Day Report\ID INFO\LOCATION ID.xlsx"

def analyze_data():
    """分析Excel文件和数据库中的数据匹配情况"""
    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {ID_FILE_PATH}")
        df = pd.read_excel(ID_FILE_PATH)
        df = df.fillna('')
        
        # 标准化Excel列名
        column_mapping = {
            'Chair Serial No': 'Chair_Serial_No',
            'STATE': 'STATE',
            'LOCATION': 'Location',
            'QTY': 'Quantity'
        }
        df.rename(columns=column_mapping, inplace=True)
        
        # 连接数据库
        print(f"正在连接数据库: {DB_PATH}")
        conn = sqlite3.connect(DB_PATH)
        
        # 获取当前有效的数据库记录
        current_df = pd.read_sql(
            "SELECT Chair_Serial_No, STATE, Location, Quantity, Effective_From FROM Equipment_ID WHERE Effective_To IS NULL", 
            conn
        )
        current_df = current_df.fillna('')
        
        # 使用集合运算进行数据分析
        excel_serials = set(df['Chair_Serial_No'])
        db_serials = set(current_df['Chair_Serial_No'])
        
        # 计算新增和遗失的数据
        new_serials = excel_serials - db_serials
        missing_serials = db_serials - excel_serials
        
        # 输出分析结果
        print("\n=== 数据分析结果 ===")
        print(f"1. 新数据（Excel有但数据库没有）: {len(new_serials)} 条")
        if new_serials:
            new_devices = df[df['Chair_Serial_No'].isin(new_serials)]
            print("\n新数据列表:")
            print(new_devices[['Chair_Serial_No', 'STATE', 'Location', 'Quantity']])
        
        print(f"\n2. 遗失数据（数据库有但Excel没有）: {len(missing_serials)} 条")
        if missing_serials:
            missing_df = current_df[current_df['Chair_Serial_No'].isin(missing_serials)]
            print("\n遗失数据列表:")
            print(missing_df[['Chair_Serial_No', 'STATE', 'Location', 'Quantity']])
        
        print("\n=== 总结 ===")
        print(f"Excel文件总记录: {len(df)} 条")
        print(f"数据库有效记录: {len(current_df)} 条")
        print(f"需要新增: {len(new_serials)} 条")
        print(f"需要标记遗失: {len(missing_serials)} 条")
        
    except Exception as e:
        print(f"分析过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    analyze_data()