# -*- coding: utf-8 -*-
"""
配置管理模块
统一管理所有常量、配置项和魔法数字
"""

import os
from typing import Dict, Any
from dataclasses import dataclass


@dataclass
class UIConfig:
    """UI界面配置"""
    # 主窗口配置
    MAIN_WINDOW_SIZE = "1400x800"
    MAIN_WINDOW_TITLE = "设备ID管理系统"
    
    # 对话框尺寸
    DIALOG_SIZES = {
        "add_equipment": "900x700",
        "batch_add": "800x600",
        "edit_dates": "400x300",
        "expired_list": "1200x550",
        "find_empty": "400x150",
        "duplicate_records": "1200x600",
        "unmatched_sales": "800x600",
        "batch_edit": "600x650",
        "import_progress": "600x400",
        "edit_expiry_date": "300x120",
        "set_effective_dates": "500x400"
    }
    
    # 字体配置
    FONTS = {
        "default": ("Arial", 10),
        "bold": ("Arial", 10, "bold"),
        "title": ("Arial", 14, "bold"),
        "large_title": ("Arial", 12, "bold"),
        "small": ("Arial", 8),
        "italic": ("Arial", 10, "italic"),
        "code": ("Courier New", 9)
    }
    
    # 颜色配置
    COLORS = {
        "background": "#f5f5f7",
        "primary": "#0071e3",
        "primary_hover": "#005bbd",
        "success": "green",
        "warning": "orange", 
        "error": "red",
        "info": "blue",
        "gray": "gray",
        "border": "#e6e6e6"
    }
    
    # 间距配置
    PADDING = {
        "small": 5,
        "medium": 10,
        "large": 15,
        "xlarge": 20
    }


@dataclass
class DatabaseConfig:
    """数据库配置"""
    # 默认数据库路径
    DEFAULT_DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
    CONFIG_FILE = "db_config.ini"
    
    # 连接配置
    CONNECTION_TIMEOUT = 30
    MAX_RETRIES = 3
    
    # 查询限制
    DEFAULT_PAGE_SIZE = 50
    MAX_PAGE_SIZE = 200
    QUERY_LIMIT = 20
    AUTOCOMPLETE_LIMIT = 20
    PREVIEW_LIMIT = 100
    
    # 历史记录配置
    OPERATION_HISTORY_LIMIT = 50
    
    # 数据库表名
    TABLES = {
        "equipment": "Equipment_ID",
        "sales": "Daily_Equipment_Sales", 
        "logs": "Logs"
    }


@dataclass
class BusinessConfig:
    """业务逻辑配置"""
    # 价格阈值
    PRICE_THRESHOLD = 5
    
    # 字符串长度限制
    STRING_LIMITS = {
        "serial_number_min": 3,
        "general_text_max": 100,
        "description_max": 500
    }
    
    # 日期格式
    DATE_FORMATS = {
        "standard": "%Y-%m-%d",
        "timestamp": "%Y-%m-%d %H:%M:%S",
        "timestamp_ms": "%Y-%m-%d %H:%M:%S.%f",
        "display": "%Y年%m月%d日"
    }
    
    # 验证规则
    VALIDATION_PATTERNS = {
        "serial_number": r'^[A-Za-z0-9_-]+$',
        "date": r'^\d{4}-\d{2}-\d{2}$',
        "email": r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    }
    
    # 必填字段
    REQUIRED_FIELDS = ["STATE", "Location", "Quantity", "Chair_Serial_No"]
    
    # 可批量编辑的字段（排除ID和序列号）
    BATCH_EDITABLE_FIELDS = [
        "STATE", "Location", "Quantity", "Sim_Card_Model", "Sim_Card_No",
        "Layer", "Company", "Effective_From", "Effective_To", "Rental", 
        "SIMCARDID", "DATE"
    ]


@dataclass
class ColumnConfig:
    """列配置"""
    # 数据库列定义
    COLUMNS = (
        "ID", "STATE", "Location", "Quantity", "Chair_Serial_No",
        "Sim_Card_Model", "Sim_Card_No", "Layer", "Company",
        "Effective_From", "Effective_To", "Rental", "SIMCARDID",
        "Import_Date", "Last_Updated", "CurrentFlag", "DATE"
    )
    
    # 列宽配置
    COLUMN_WIDTHS = {
        "ID": 50,
        "STATE": 80,
        "Location": 120,
        "Quantity": 60,
        "Chair_Serial_No": 150,
        "Sim_Card_Model": 120,
        "Sim_Card_No": 120,
        "Layer": 80,
        "Company": 150,
        "Effective_From": 100,
        "Effective_To": 100,
        "Rental": 80,
        "SIMCARDID": 120,
        "Import_Date": 100,
        "Last_Updated": 120,
        "CurrentFlag": 80,
        "DATE": 100
    }
    
    # 列显示名称（用于国际化）
    COLUMN_DISPLAY_NAMES = {
        "ID": "ID",
        "STATE": "状态",
        "Location": "位置", 
        "Quantity": "数量",
        "Chair_Serial_No": "椅子序列号",
        "Sim_Card_Model": "SIM卡型号",
        "Sim_Card_No": "SIM卡号",
        "Layer": "层级",
        "Company": "公司",
        "Effective_From": "生效日期",
        "Effective_To": "失效日期",
        "Rental": "租金",
        "SIMCARDID": "SIM卡ID",
        "Import_Date": "导入日期",
        "Last_Updated": "最后更新",
        "CurrentFlag": "当前标志",
        "DATE": "日期"
    }
    
    # 可搜索的字段
    SEARCHABLE_FIELDS = ["Chair_Serial_No", "Location", "STATE"]
    
    # 可查找空值的字段
    EMPTY_VALUE_FIELDS = [
        "Rental", "Layer", "DATE", "Chair_Serial_No", "Sim_Card_No", 
        "Effective_To", "Company", "Effective_From", "Sim_Card_Model", 
        "STATE", "Location"
    ]


@dataclass
class MessageConfig:
    """消息配置"""
    # 消息标题
    TITLES = {
        "info": "提示",
        "warning": "警告", 
        "error": "错误",
        "success": "成功",
        "confirm": "确认",
        "question": "询问"
    }
    
    # 常用消息
    MESSAGES = {
        "no_selection": "请先选择要操作的记录",
        "operation_success": "操作成功",
        "operation_failed": "操作失败",
        "data_saved": "数据保存成功",
        "data_deleted": "数据删除成功",
        "invalid_input": "输入数据无效",
        "database_error": "数据库操作失败",
        "file_not_found": "文件未找到",
        "permission_denied": "权限不足"
    }


@dataclass
class FileConfig:
    """文件配置"""
    # 支持的文件类型
    SUPPORTED_FORMATS = {
        "excel": [("Excel文件", "*.xlsx *.xls")],
        "csv": [("CSV文件", "*.csv")],
        "all": [("所有文件", "*.*")]
    }
    
    # 导出文件名模板
    EXPORT_TEMPLATES = {
        "equipment": "设备数据_{date}.xlsx",
        "expired": "过期设备_{date}.xlsx", 
        "duplicates": "重复序列号_{date}.xlsx",
        "abnormal": "异常序列号_{year}_{month}.xlsx"
    }
    
    # 日志配置
    LOG_CONFIG = {
        "filename": "equipment_manager.log",
        "max_size": 10 * 1024 * 1024,  # 10MB
        "backup_count": 5,
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    }


class Config:
    """统一配置管理类"""
    
    def __init__(self):
        self.ui = UIConfig()
        self.database = DatabaseConfig()
        self.business = BusinessConfig()
        self.columns = ColumnConfig()
        self.messages = MessageConfig()
        self.files = FileConfig()
    
    def get_window_size(self, window_type: str = "main") -> str:
        """获取窗口尺寸"""
        if window_type == "main":
            return self.ui.MAIN_WINDOW_SIZE
        return self.ui.DIALOG_SIZES.get(window_type, "800x600")
    
    def get_column_width(self, column_name: str) -> int:
        """获取列宽"""
        return self.columns.COLUMN_WIDTHS.get(column_name, 100)
    
    def get_font(self, font_type: str = "default") -> tuple:
        """获取字体配置"""
        return self.ui.FONTS.get(font_type, ("Arial", 10))
    
    def get_color(self, color_name: str) -> str:
        """获取颜色配置"""
        return self.ui.COLORS.get(color_name, "black")
    
    def get_message(self, message_key: str) -> str:
        """获取消息文本"""
        return self.messages.MESSAGES.get(message_key, "未知消息")
    
    def get_title(self, title_key: str) -> str:
        """获取标题文本"""
        return self.messages.TITLES.get(title_key, "提示")


# 全局配置实例
config = Config()

# 向后兼容的常量导出
PAGE_SIZE = config.database.DEFAULT_PAGE_SIZE
HISTORY_LIMIT = config.database.OPERATION_HISTORY_LIMIT
WINDOW_SIZE = config.ui.MAIN_WINDOW_SIZE
PRICE_THRESHOLD = config.business.PRICE_THRESHOLD
COLUMN_WIDTHS = config.columns.COLUMN_WIDTHS
COLUMNS = config.columns.COLUMNS
