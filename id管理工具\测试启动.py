# -*- coding: utf-8 -*-
"""
测试启动脚本 - 逐步诊断启动问题
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

def test_basic_imports():
    """测试基本导入"""
    print("🧪 测试基本导入...")
    try:
        import sqlite3
        import pandas as pd
        import datetime
        import threading
        import time
        print("  ✅ 基本模块导入成功")
        return True
    except Exception as e:
        print(f"  ❌ 基本模块导入失败: {e}")
        return False

def test_tkinter():
    """测试Tkinter"""
    print("🧪 测试Tkinter...")
    try:
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("300x200")
        
        label = tk.Label(root, text="Tkinter测试成功！")
        label.pack(pady=50)
        
        def close_window():
            root.destroy()
        
        button = tk.Button(root, text="关闭", command=close_window)
        button.pack()
        
        print("  ✅ Tkinter测试窗口已创建")
        print("  ℹ️ 请关闭测试窗口继续...")
        
        root.mainloop()
        print("  ✅ Tkinter测试完成")
        return True
        
    except Exception as e:
        print(f"  ❌ Tkinter测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("🧪 测试数据库连接...")
    try:
        import sqlite3
        
        # 测试内存数据库
        conn = sqlite3.connect(":memory:")
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        conn.close()
        
        print(f"  ✅ 内存数据库测试成功: {result}")
        
        # 测试实际数据库文件
        db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path, timeout=5.0)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            conn.close()
            print(f"  ✅ 实际数据库连接成功，找到 {len(tables)} 个表")
        else:
            print(f"  ⚠️ 数据库文件不存在: {db_path}")
            
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库连接测试失败: {e}")
        return False

def test_optimization_modules():
    """测试优化模块"""
    print("🧪 测试优化模块...")
    try:
        # 禁用优化模块
        os.environ['DISABLE_OPTIMIZATION'] = '1'
        
        # 尝试导入主程序
        import ID管理工具
        print("  ✅ 主程序模块导入成功（优化模块已禁用）")
        
        # 检查是否有EquipmentManager类
        if hasattr(ID管理工具, 'EquipmentManager'):
            print("  ✅ EquipmentManager类存在")
        else:
            print("  ❌ EquipmentManager类不存在")
            return False
            
        return True
        
    except Exception as e:
        print(f"  ❌ 优化模块测试失败: {e}")
        traceback.print_exc()
        return False

def test_minimal_app():
    """测试最小化应用程序"""
    print("🧪 测试最小化应用程序...")
    try:
        # 确保禁用优化模块
        os.environ['DISABLE_OPTIMIZATION'] = '1'
        
        # 创建最小化的应用程序
        root = tk.Tk()
        root.title("ID管理工具 - 测试版")
        root.geometry("800x600")
        
        # 添加简单的界面元素
        frame = tk.Frame(root)
        frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        title_label = tk.Label(frame, text="ID管理工具测试", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        status_label = tk.Label(frame, text="✅ 基本功能正常", fg="green")
        status_label.pack(pady=5)
        
        info_text = tk.Text(frame, height=10, width=60)
        info_text.pack(pady=10)
        info_text.insert(tk.END, "这是一个测试版本的ID管理工具。\n")
        info_text.insert(tk.END, "如果您看到这个窗口，说明基本功能正常。\n")
        info_text.insert(tk.END, "请关闭此窗口，然后尝试启动完整版本。\n")
        
        def close_app():
            root.destroy()
        
        close_button = tk.Button(frame, text="关闭", command=close_app, width=20)
        close_button.pack(pady=10)
        
        print("  ✅ 最小化应用程序已创建")
        print("  ℹ️ 请关闭应用程序窗口继续...")
        
        root.mainloop()
        print("  ✅ 最小化应用程序测试完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 最小化应用程序测试失败: {e}")
        traceback.print_exc()
        return False

def test_full_app():
    """测试完整应用程序"""
    print("🧪 测试完整应用程序...")
    try:
        # 禁用优化模块以避免初始化问题
        os.environ['DISABLE_OPTIMIZATION'] = '1'
        
        print("  🔄 导入主程序...")
        import ID管理工具
        
        print("  🔄 创建根窗口...")
        root = tk.Tk()
        
        print("  🔄 创建EquipmentManager...")
        # 这里可能会卡住
        app = ID管理工具.EquipmentManager(root)
        
        print("  ✅ 完整应用程序创建成功")
        print("  ℹ️ 启动主循环...")
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"  ❌ 完整应用程序测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔬 ID管理工具启动诊断")
    print("=" * 50)
    
    tests = [
        ("基本导入", test_basic_imports),
        ("Tkinter", test_tkinter),
        ("数据库连接", test_database_connection),
        ("优化模块", test_optimization_modules),
        ("最小化应用", test_minimal_app),
        ("完整应用", test_full_app)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
                response = input(f"是否继续下一个测试? (y/n): ")
                if response.lower() != 'y':
                    break
        except Exception as e:
            print(f"💥 {test_name}测试异常: {e}")
            traceback.print_exc()
            response = input(f"是否继续下一个测试? (y/n): ")
            if response.lower() != 'y':
                break
    
    print("\n" + "=" * 50)
    print("🎯 诊断完成")
    print("如果所有测试都通过，程序应该能够正常运行。")
    print("如果某个测试失败，请根据错误信息进行修复。")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    finally:
        input("\n按回车键退出...")
