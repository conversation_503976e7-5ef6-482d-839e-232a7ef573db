# -*- coding: utf-8 -*-
"""
测试新文件夹中的程序启动
"""

import sys
import os

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    modules_to_test = [
        "constants",
        "database_connection_manager", 
        "error_handler",
        "config_manager",
        "excel_import_manager",
        "database_switcher"
    ]
    
    success_count = 0
    
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"  ✅ {module}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {module}: {e}")
    
    print(f"\n📊 导入结果: {success_count}/{len(modules_to_test)} 成功")
    return success_count == len(modules_to_test)

def test_main_program():
    """测试主程序"""
    print("\n🧪 测试主程序...")
    
    try:
        print("  导入主程序...")
        import ID管理工具
        print("  ✅ 主程序导入成功")
        
        print("  检查主要类...")
        if hasattr(ID管理工具, 'EquipmentManager'):
            print("  ✅ EquipmentManager 类存在")
        else:
            print("  ❌ EquipmentManager 类不存在")
            return False
        
        print("  检查数据库切换方法...")
        if hasattr(ID管理工具.EquipmentManager, 'show_database_manager'):
            print("  ✅ show_database_manager 方法存在")
        else:
            print("  ❌ show_database_manager 方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 主程序测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_switcher():
    """测试数据库切换器"""
    print("\n🧪 测试数据库切换器...")
    
    try:
        from database_switcher import DatabaseSwitcher, show_database_manager
        print("  ✅ 数据库切换器导入成功")
        
        # 测试创建实例
        class MockWindow:
            pass
        
        mock_window = MockWindow()
        switcher = DatabaseSwitcher(mock_window)
        print("  ✅ 数据库切换器实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库切换器测试失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器"""
    print("\n🧪 测试配置管理器...")
    
    try:
        from config_manager import user_preferences, config_manager
        print("  ✅ 配置管理器导入成功")
        
        # 测试获取数据库路径
        db_path = user_preferences.get_current_database()
        print(f"  ✅ 获取数据库路径: {db_path}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔬 新文件夹程序启动测试")
    print("=" * 50)
    print(f"📁 当前目录: {os.getcwd()}")
    print(f"🐍 Python 版本: {sys.version}")
    
    tests = [
        ("模块导入", test_imports),
        ("配置管理器", test_config_manager),
        ("数据库切换器", test_database_switcher),
        ("主程序", test_main_program),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                failed += 1
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            failed += 1
            print(f"💥 {test_name}测试异常: {e}")
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"  ✅ 通过: {passed}")
    print(f"  ❌ 失败: {failed}")
    print(f"  📈 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("🎉 所有测试通过！程序应该可以正常启动。")
        
        # 尝试启动GUI（非阻塞测试）
        print("\n🚀 尝试启动GUI测试...")
        try:
            import tkinter as tk
            import ID管理工具
            
            print("  创建根窗口...")
            root = tk.Tk()
            root.withdraw()  # 隐藏窗口
            
            print("  创建应用实例...")
            app = ID管理工具.EquipmentManager(root)
            
            print("  ✅ GUI创建成功！")
            print("  🎯 程序可以正常启动")
            
            root.destroy()
            
        except Exception as e:
            print(f"  ❌ GUI测试失败: {e}")
            
    else:
        print("⚠️ 部分测试失败，程序可能无法正常启动。")
    
    return failed == 0

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 新文件夹中的程序可以正常使用！")
        else:
            print("\n❌ 新文件夹中的程序存在问题，请检查。")
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    finally:
        input("\n按回车键退出...")
