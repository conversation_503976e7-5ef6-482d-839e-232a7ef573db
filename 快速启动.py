# -*- coding: utf-8 -*-
"""
快速启动脚本 - 修复数据库阻塞问题后的测试
"""

import sys
import os

def main():
    """主函数"""
    print("🚀 快速启动ID管理工具")
    print("=" * 40)
    
    try:
        print("📦 导入主程序...")
        
        # 导入并启动主程序
        import ID管理工具
        
        print("🎯 创建应用程序...")
        
        # 创建并启动应用
        import tkinter as tk
        root = tk.Tk()
        app = ID管理工具.EquipmentManager(root)
        
        print("✅ 应用程序启动成功！")
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
