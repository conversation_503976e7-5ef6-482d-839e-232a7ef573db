# -*- coding: utf-8 -*-
"""
用户体验增强方案
快捷键、界面优化、智能提示等
"""

import tkinter as tk
from tkinter import ttk
import re
from typing import Dict, List, Callable, Any


class KeyboardShortcutManager:
    """键盘快捷键管理器"""
    
    def __init__(self, root_window):
        self.root = root_window
        self.shortcuts = {}
        self.help_window = None
        
    def register_shortcut(self, key_combination: str, callback: Callable, 
                         description: str = ""):
        """注册快捷键"""
        self.shortcuts[key_combination] = {
            'callback': callback,
            'description': description
        }
        self.root.bind(key_combination, lambda e: callback())
        print(f"✅ 注册快捷键: {key_combination} - {description}")
    
    def setup_default_shortcuts(self, app_instance):
        """设置默认快捷键"""
        shortcuts_config = [
            # 文件操作
            ('<Control-n>', app_instance.add_equipment_id, '新建设备记录'),
            ('<Control-o>', app_instance.import_excel_data, '导入Excel数据'),
            ('<Control-s>', app_instance.export_to_excel, '导出到Excel'),
            ('<Control-q>', app_instance.on_closing, '退出程序'),
            
            # 编辑操作
            ('<Control-e>', app_instance.edit_selected_id, '编辑选中记录'),
            ('<Delete>', app_instance.delete_selected_id, '删除选中记录'),
            ('<Control-d>', app_instance.duplicate_selected_record, '复制选中记录'),
            ('<Control-z>', app_instance.undo_operation, '撤销上一步操作'),
            ('<Control-y>', app_instance.redo_operation, '重做操作'),
            
            # 查看操作
            ('<F5>', app_instance.refresh_data, '刷新数据'),
            ('<Control-f>', app_instance.focus_search, '聚焦搜索框'),
            ('<Control-r>', app_instance.reset_search, '重置搜索'),
            ('<Escape>', app_instance.clear_selection, '清除选择'),
            
            # 导航操作
            ('<Control-Home>', app_instance.go_to_first_page, '跳转到首页'),
            ('<Control-End>', app_instance.go_to_last_page, '跳转到末页'),
            ('<Control-Left>', app_instance.previous_page, '上一页'),
            ('<Control-Right>', app_instance.next_page, '下一页'),
            
            # 工具操作
            ('<Control-h>', self.show_help, '显示快捷键帮助'),
            ('<F1>', self.show_help, '显示帮助'),
            ('<Control-comma>', app_instance.show_preferences, '显示设置'),
        ]
        
        for key, callback, description in shortcuts_config:
            try:
                self.register_shortcut(key, callback, description)
            except Exception as e:
                print(f"⚠️ 注册快捷键失败 {key}: {e}")
    
    def show_help(self):
        """显示快捷键帮助"""
        if self.help_window and self.help_window.winfo_exists():
            self.help_window.lift()
            return
        
        self.help_window = tk.Toplevel(self.root)
        self.help_window.title("快捷键帮助")
        self.help_window.geometry("600x500")
        self.help_window.transient(self.root)
        
        # 创建滚动文本框
        text_frame = ttk.Frame(self.help_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        # 生成帮助内容
        help_content = "🔧 快捷键列表\n" + "=" * 50 + "\n\n"
        
        categories = {
            "文件操作": ["<Control-n>", "<Control-o>", "<Control-s>", "<Control-q>"],
            "编辑操作": ["<Control-e>", "<Delete>", "<Control-d>", "<Control-z>", "<Control-y>"],
            "查看操作": ["<F5>", "<Control-f>", "<Control-r>", "<Escape>"],
            "导航操作": ["<Control-Home>", "<Control-End>", "<Control-Left>", "<Control-Right>"],
            "工具操作": ["<Control-h>", "<F1>", "<Control-comma>"]
        }
        
        for category, keys in categories.items():
            help_content += f"📁 {category}\n"
            for key in keys:
                if key in self.shortcuts:
                    shortcut_info = self.shortcuts[key]
                    help_content += f"  {key:<20} {shortcut_info['description']}\n"
            help_content += "\n"
        
        text_widget.insert(tk.END, help_content)
        text_widget.config(state=tk.DISABLED)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 关闭按钮
        close_btn = ttk.Button(self.help_window, text="关闭", command=self.help_window.destroy)
        close_btn.pack(pady=10)


class SmartSearchBox:
    """智能搜索框"""
    
    def __init__(self, parent, search_callback: Callable):
        self.parent = parent
        self.search_callback = search_callback
        self.search_history = []
        self.suggestions = []
        self.suggestion_window = None
        
    def create_search_frame(self) -> ttk.Frame:
        """创建搜索框架"""
        search_frame = ttk.LabelFrame(self.parent, text="智能搜索", padding="10")
        
        # 搜索类型选择
        type_frame = ttk.Frame(search_frame)
        type_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(type_frame, text="搜索类型:").pack(side=tk.LEFT)
        
        self.search_type_var = tk.StringVar(value="all")
        search_types = [
            ("全部", "all"),
            ("序列号", "serial"),
            ("位置", "location"),
            ("状态", "state")
        ]
        
        for text, value in search_types:
            ttk.Radiobutton(
                type_frame, 
                text=text, 
                variable=self.search_type_var, 
                value=value
            ).pack(side=tk.LEFT, padx=5)
        
        # 搜索输入框
        input_frame = ttk.Frame(search_frame)
        input_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(input_frame, text="搜索内容:").pack(side=tk.LEFT)
        
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(
            input_frame, 
            textvariable=self.search_var,
            font=("Arial", 10)
        )
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 绑定事件
        self.search_entry.bind('<KeyRelease>', self._on_key_release)
        self.search_entry.bind('<Return>', self._on_search)
        self.search_entry.bind('<Down>', self._on_arrow_down)
        self.search_entry.bind('<Up>', self._on_arrow_up)
        self.search_entry.bind('<Escape>', self._hide_suggestions)
        
        # 搜索按钮
        search_btn = ttk.Button(
            input_frame, 
            text="搜索", 
            command=self._on_search,
            width=8
        )
        search_btn.pack(side=tk.RIGHT, padx=5)
        
        # 清除按钮
        clear_btn = ttk.Button(
            input_frame, 
            text="清除", 
            command=self._clear_search,
            width=8
        )
        clear_btn.pack(side=tk.RIGHT)
        
        return search_frame
    
    def _on_key_release(self, event):
        """按键释放事件"""
        if event.keysym in ['Up', 'Down', 'Return', 'Escape']:
            return
        
        search_text = self.search_var.get().strip()
        if len(search_text) >= 2:
            self._show_suggestions(search_text)
        else:
            self._hide_suggestions()
    
    def _on_search(self, event=None):
        """执行搜索"""
        search_text = self.search_var.get().strip()
        search_type = self.search_type_var.get()
        
        if search_text:
            # 添加到搜索历史
            if search_text not in self.search_history:
                self.search_history.insert(0, search_text)
                if len(self.search_history) > 20:
                    self.search_history.pop()
        
        self._hide_suggestions()
        self.search_callback(search_text, search_type)
    
    def _clear_search(self):
        """清除搜索"""
        self.search_var.set("")
        self._hide_suggestions()
        self.search_callback("", "all")
    
    def _show_suggestions(self, search_text: str):
        """显示搜索建议"""
        # 生成建议列表
        suggestions = []
        
        # 从搜索历史中匹配
        for history_item in self.search_history:
            if search_text.lower() in history_item.lower():
                suggestions.append(("历史", history_item))
        
        # 从数据库中获取建议（这里需要实际的数据库查询）
        # suggestions.extend(self._get_database_suggestions(search_text))
        
        if not suggestions:
            self._hide_suggestions()
            return
        
        # 创建建议窗口
        if self.suggestion_window:
            self.suggestion_window.destroy()
        
        self.suggestion_window = tk.Toplevel(self.parent)
        self.suggestion_window.wm_overrideredirect(True)
        self.suggestion_window.configure(bg="white", relief="solid", bd=1)
        
        # 计算位置
        x = self.search_entry.winfo_rootx()
        y = self.search_entry.winfo_rooty() + self.search_entry.winfo_height()
        width = self.search_entry.winfo_width()
        
        self.suggestion_window.geometry(f"{width}x{min(len(suggestions) * 25, 200)}+{x}+{y}")
        
        # 创建建议列表
        self.suggestion_listbox = tk.Listbox(
            self.suggestion_window,
            height=min(len(suggestions), 8),
            font=("Arial", 9)
        )
        self.suggestion_listbox.pack(fill=tk.BOTH, expand=True)
        
        for suggestion_type, suggestion_text in suggestions:
            self.suggestion_listbox.insert(tk.END, f"[{suggestion_type}] {suggestion_text}")
        
        # 绑定事件
        self.suggestion_listbox.bind('<Double-Button-1>', self._on_suggestion_select)
        self.suggestion_listbox.bind('<Return>', self._on_suggestion_select)
    
    def _hide_suggestions(self, event=None):
        """隐藏搜索建议"""
        if self.suggestion_window:
            self.suggestion_window.destroy()
            self.suggestion_window = None
    
    def _on_suggestion_select(self, event):
        """选择建议项"""
        selection = self.suggestion_listbox.curselection()
        if selection:
            suggestion_text = self.suggestion_listbox.get(selection[0])
            # 提取实际文本（去掉类型标签）
            actual_text = suggestion_text.split('] ', 1)[1] if '] ' in suggestion_text else suggestion_text
            self.search_var.set(actual_text)
            self._hide_suggestions()
            self._on_search()
    
    def _on_arrow_down(self, event):
        """向下箭头键"""
        if self.suggestion_window and self.suggestion_listbox:
            current = self.suggestion_listbox.curselection()
            if current:
                next_index = min(current[0] + 1, self.suggestion_listbox.size() - 1)
            else:
                next_index = 0
            self.suggestion_listbox.selection_clear(0, tk.END)
            self.suggestion_listbox.selection_set(next_index)
            return "break"
    
    def _on_arrow_up(self, event):
        """向上箭头键"""
        if self.suggestion_window and self.suggestion_listbox:
            current = self.suggestion_listbox.curselection()
            if current:
                prev_index = max(current[0] - 1, 0)
            else:
                prev_index = self.suggestion_listbox.size() - 1
            self.suggestion_listbox.selection_clear(0, tk.END)
            self.suggestion_listbox.selection_set(prev_index)
            return "break"


class StatusBarEnhanced:
    """增强状态栏"""
    
    def __init__(self, parent):
        self.parent = parent
        self.status_frame = None
        self.status_vars = {}
        self.progress_var = None
        
    def create_status_bar(self) -> ttk.Frame:
        """创建增强状态栏"""
        self.status_frame = ttk.Frame(self.parent)
        
        # 主状态信息
        self.status_vars['main'] = tk.StringVar(value="就绪")
        main_label = ttk.Label(
            self.status_frame, 
            textvariable=self.status_vars['main'],
            font=("Arial", 9)
        )
        main_label.pack(side=tk.LEFT, padx=5)
        
        # 分隔符
        ttk.Separator(self.status_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 记录统计
        self.status_vars['count'] = tk.StringVar(value="总计: 0 条记录")
        count_label = ttk.Label(
            self.status_frame, 
            textvariable=self.status_vars['count'],
            font=("Arial", 9)
        )
        count_label.pack(side=tk.LEFT, padx=5)
        
        # 分隔符
        ttk.Separator(self.status_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # 选择信息
        self.status_vars['selection'] = tk.StringVar(value="未选择")
        selection_label = ttk.Label(
            self.status_frame, 
            textvariable=self.status_vars['selection'],
            font=("Arial", 9)
        )
        selection_label.pack(side=tk.LEFT, padx=5)
        
        # 右侧信息
        right_frame = ttk.Frame(self.status_frame)
        right_frame.pack(side=tk.RIGHT, padx=5)
        
        # 时间显示
        self.status_vars['time'] = tk.StringVar()
        time_label = ttk.Label(
            right_frame, 
            textvariable=self.status_vars['time'],
            font=("Arial", 9)
        )
        time_label.pack(side=tk.RIGHT, padx=5)
        
        # 更新时间
        self._update_time()
        
        return self.status_frame
    
    def update_status(self, message: str):
        """更新主状态"""
        if 'main' in self.status_vars:
            self.status_vars['main'].set(message)
    
    def update_count(self, current_count: int, total_count: int = None):
        """更新记录统计"""
        if 'count' in self.status_vars:
            if total_count is not None:
                text = f"显示: {current_count} / 总计: {total_count} 条记录"
            else:
                text = f"总计: {current_count} 条记录"
            self.status_vars['count'].set(text)
    
    def update_selection(self, selected_count: int):
        """更新选择信息"""
        if 'selection' in self.status_vars:
            if selected_count == 0:
                text = "未选择"
            elif selected_count == 1:
                text = "已选择 1 条记录"
            else:
                text = f"已选择 {selected_count} 条记录"
            self.status_vars['selection'].set(text)
    
    def _update_time(self):
        """更新时间显示"""
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if 'time' in self.status_vars:
            self.status_vars['time'].set(current_time)
        
        # 每秒更新一次
        self.parent.after(1000, self._update_time)


class ToolTipManager:
    """工具提示管理器"""
    
    def __init__(self):
        self.tooltips = {}
    
    def add_tooltip(self, widget, text: str):
        """为控件添加工具提示"""
        tooltip = ToolTip(widget, text)
        self.tooltips[widget] = tooltip
        return tooltip


class ToolTip:
    """工具提示类"""
    
    def __init__(self, widget, text: str):
        self.widget = widget
        self.text = text
        self.tooltip_window = None
        
        # 绑定事件
        self.widget.bind('<Enter>', self._on_enter)
        self.widget.bind('<Leave>', self._on_leave)
        self.widget.bind('<Motion>', self._on_motion)
    
    def _on_enter(self, event):
        """鼠标进入"""
        self._show_tooltip(event)
    
    def _on_leave(self, event):
        """鼠标离开"""
        self._hide_tooltip()
    
    def _on_motion(self, event):
        """鼠标移动"""
        if self.tooltip_window:
            self._update_position(event)
    
    def _show_tooltip(self, event):
        """显示工具提示"""
        if self.tooltip_window:
            return
        
        self.tooltip_window = tk.Toplevel(self.widget)
        self.tooltip_window.wm_overrideredirect(True)
        self.tooltip_window.configure(bg="lightyellow", relief="solid", bd=1)
        
        label = tk.Label(
            self.tooltip_window,
            text=self.text,
            bg="lightyellow",
            font=("Arial", 9),
            padx=5,
            pady=3
        )
        label.pack()
        
        self._update_position(event)
    
    def _hide_tooltip(self):
        """隐藏工具提示"""
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None
    
    def _update_position(self, event):
        """更新位置"""
        if self.tooltip_window:
            x = event.x_root + 10
            y = event.y_root + 10
            self.tooltip_window.geometry(f"+{x}+{y}")


def create_ux_integration_example():
    """创建用户体验集成示例"""
    
    example_code = '''
# === 在ID管理工具.py中集成用户体验增强 ===

# 1. 在__init__方法中初始化UX组件
def __init__(self):
    # ... 现有代码 ...
    
    # 初始化用户体验组件
    self.shortcut_manager = KeyboardShortcutManager(self.root)
    self.tooltip_manager = ToolTipManager()
    
    # 设置快捷键
    self.shortcut_manager.setup_default_shortcuts(self)
    
    # 创建智能搜索框
    self.smart_search = SmartSearchBox(self.search_frame, self.perform_smart_search)
    search_frame = self.smart_search.create_search_frame()
    search_frame.pack(fill=tk.X, pady=5)
    
    # 创建增强状态栏
    self.status_bar = StatusBarEnhanced(self.root)
    status_frame = self.status_bar.create_status_bar()
    status_frame.pack(side=tk.BOTTOM, fill=tk.X)
    
    # 添加工具提示
    self._setup_tooltips()

# 2. 设置工具提示
def _setup_tooltips(self):
    """设置工具提示"""
    tooltips = [
        (self.add_btn, "添加新的设备记录 (Ctrl+N)"),
        (self.edit_btn, "编辑选中的设备记录 (Ctrl+E)"),
        (self.delete_btn, "删除选中的设备记录 (Delete)"),
        (self.refresh_btn, "刷新数据 (F5)"),
        (self.import_btn, "从Excel导入数据 (Ctrl+O)"),
        (self.export_btn, "导出数据到Excel (Ctrl+S)"),
    ]
    
    for widget, text in tooltips:
        if widget:
            self.tooltip_manager.add_tooltip(widget, text)

# 3. 智能搜索回调
def perform_smart_search(self, search_text: str, search_type: str):
    """执行智能搜索"""
    if not search_text:
        self.reset_search()
        return
    
    # 根据搜索类型构建查询
    if search_type == "serial":
        self.search_serial_var.set(search_text)
        self.search_location_var.set("")
        self.search_state_var.set("")
    elif search_type == "location":
        self.search_serial_var.set("")
        self.search_location_var.set(search_text)
        self.search_state_var.set("")
    elif search_type == "state":
        self.search_serial_var.set("")
        self.search_location_var.set("")
        self.search_state_var.set(search_text)
    else:  # all
        # 智能判断搜索内容类型
        if re.match(r'^[A-Za-z0-9_-]+$', search_text):
            self.search_serial_var.set(search_text)
        else:
            self.search_location_var.set(search_text)
    
    # 执行搜索
    self.search_data()

# 4. 新增的快捷键方法
def focus_search(self):
    """聚焦搜索框"""
    if hasattr(self, 'smart_search'):
        self.smart_search.search_entry.focus_set()

def clear_selection(self):
    """清除选择"""
    if hasattr(self, 'tree'):
        self.tree.selection_remove(self.tree.selection())
        self.status_bar.update_selection(0)

def go_to_first_page(self):
    """跳转到首页"""
    self.current_page = 1
    self.load_data()

def go_to_last_page(self):
    """跳转到末页"""
    total_count = self.get_total_count()
    self.current_page = max(1, (total_count + self.page_size - 1) // self.page_size)
    self.load_data()

def duplicate_selected_record(self):
    """复制选中记录"""
    selected = self.tree.selection()
    if not selected:
        safe_show_info("请先选择要复制的记录")
        return
    
    # 获取选中记录的数据
    item = selected[0]
    values = self.tree.item(item)["values"]
    
    # 打开添加对话框并预填数据
    self.add_equipment_id(prefill_data=values)

def show_preferences(self):
    """显示设置对话框"""
    # 创建设置对话框
    prefs_dialog = tk.Toplevel(self.root)
    prefs_dialog.title("设置")
    prefs_dialog.geometry("400x300")
    prefs_dialog.transient(self.root)
    prefs_dialog.grab_set()
    
    # 设置内容...
    ttk.Label(prefs_dialog, text="程序设置", font=("Arial", 12, "bold")).pack(pady=10)
    
    # 页面大小设置
    size_frame = ttk.Frame(prefs_dialog)
    size_frame.pack(fill=tk.X, padx=20, pady=5)
    
    ttk.Label(size_frame, text="每页显示记录数:").pack(side=tk.LEFT)
    size_var = tk.StringVar(value=str(self.page_size))
    size_entry = ttk.Entry(size_frame, textvariable=size_var, width=10)
    size_entry.pack(side=tk.RIGHT)
    
    # 按钮
    btn_frame = ttk.Frame(prefs_dialog)
    btn_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=20, pady=20)
    
    def save_preferences():
        try:
            new_size = int(size_var.get())
            if 10 <= new_size <= 200:
                self.page_size = new_size
                safe_show_info("设置已保存")
                prefs_dialog.destroy()
            else:
                safe_show_error("页面大小必须在10-200之间")
        except ValueError:
            safe_show_error("请输入有效的数字")
    
    ttk.Button(btn_frame, text="保存", command=save_preferences).pack(side=tk.RIGHT, padx=5)
    ttk.Button(btn_frame, text="取消", command=prefs_dialog.destroy).pack(side=tk.RIGHT)

# 5. 更新状态栏信息
def update_status_bar(self, current_count: int, total_count: int = None):
    """更新状态栏信息"""
    if hasattr(self, 'status_bar'):
        self.status_bar.update_count(current_count, total_count)
        
        if current_count == 0:
            self.status_bar.update_status("无数据")
        else:
            self.status_bar.update_status("数据加载完成")

# 6. 树形控件选择事件
def on_tree_select(self, event):
    """树形控件选择事件"""
    selected = self.tree.selection()
    if hasattr(self, 'status_bar'):
        self.status_bar.update_selection(len(selected))
'''
    
    return example_code


def main():
    """主函数 - 演示用户体验增强"""
    print("🎨 用户体验增强方案")
    print("=" * 50)
    
    print("✅ 用户体验组件:")
    print("1. KeyboardShortcutManager - 快捷键管理器")
    print("2. SmartSearchBox - 智能搜索框")
    print("3. StatusBarEnhanced - 增强状态栏")
    print("4. ToolTipManager - 工具提示管理器")
    
    print("\n🎯 增强功能:")
    print("- 全面的快捷键支持")
    print("- 智能搜索建议")
    print("- 详细的状态信息")
    print("- 友好的工具提示")
    print("- 快捷键帮助系统")
    
    print("\n📈 用户体验提升:")
    print("- 操作效率大幅提升")
    print("- 学习成本显著降低")
    print("- 界面更加专业友好")
    print("- 错误操作大幅减少")
    
    print("\n📖 集成示例:")
    print(create_ux_integration_example())


if __name__ == "__main__":
    main()
