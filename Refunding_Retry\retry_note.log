2025-05-27 09:13:54.627770 第6行 找到匹配: 表=IOT_Sales, rowid=267, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:13:54.628855 删除操作影响行数: 1
2025-05-27 09:13:54.709492 第7行 找到匹配: 表=IOT_Sales, rowid=428, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:13:54.710325 删除操作影响行数: 1
2025-05-27 09:13:54.779134 第8行 找到匹配: 表=IOT_Sales, rowid=823, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:13:54.780206 更新操作影响行数: 1
2025-05-27 09:13:54.845949 第9行 找到匹配: 表=IOT_Sales, rowid=1238, 原金额=4.0, 退款=15.0, 新金额=-11.0
2025-05-27 09:13:54.846893 删除操作影响行数: 1
2025-05-27 09:13:54.914198 第10行 找到匹配: 表=IOT_Sales, rowid=554, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:13:54.915478 删除操作影响行数: 1
2025-05-27 09:13:55.051903 第11行 找到匹配: 表=IOT_Sales, rowid=1009, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:13:55.052583 删除操作影响行数: 1
2025-05-27 09:13:55.128266 第12行 找到匹配: 表=IOT_Sales, rowid=1010, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:13:55.128885 删除操作影响行数: 1
2025-05-27 09:13:55.216663 第13行 找到匹配: 表=IOT_Sales, rowid=268, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:55.217777 删除操作影响行数: 1
2025-05-27 09:13:55.295420 第14行 找到匹配: 表=IOT_Sales, rowid=36, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:13:55.296310 删除操作影响行数: 1
2025-05-27 09:13:55.467457 第16行 找到匹配: 表=IOT_Sales, rowid=97, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:13:55.468711 更新操作影响行数: 1
2025-05-27 09:13:55.548545 第17行 找到匹配: 表=IOT_Sales, rowid=3, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:13:55.549398 删除操作影响行数: 1
2025-05-27 09:13:55.632823 第18行 找到匹配: 表=IOT_Sales, rowid=38, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:55.633450 删除操作影响行数: 1
2025-05-27 09:13:55.705941 第19行 找到匹配: 表=IOT_Sales, rowid=97, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:55.707302 删除操作影响行数: 1
2025-05-27 09:13:55.787772 第20行 找到匹配: 表=IOT_Sales, rowid=3963, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:13:55.788754 删除操作影响行数: 1
2025-05-27 09:13:55.868179 第21行 找到匹配: 表=IOT_Sales, rowid=4097, 原金额=3.0, 退款=5.0, 新金额=-2.0
2025-05-27 09:13:55.869256 删除操作影响行数: 1
2025-05-27 09:13:55.945264 第22行 找到匹配: 表=IOT_Sales, rowid=5138, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:13:55.946293 删除操作影响行数: 1
2025-05-27 09:13:56.111203 第24行 找到匹配: 表=IOT_Sales, rowid=5537, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:56.111852 删除操作影响行数: 1
2025-05-27 09:13:56.184451 第25行 找到匹配: 表=IOT_Sales, rowid=3027, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:13:56.185072 删除操作影响行数: 1
2025-05-27 09:13:56.253225 第26行 找到匹配: 表=IOT_Sales, rowid=4083, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:13:56.254177 删除操作影响行数: 1
2025-05-27 09:13:56.325730 第27行 找到匹配: 表=IOT_Sales, rowid=3141, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:13:56.326641 删除操作影响行数: 1
2025-05-27 09:13:56.392044 第28行 找到匹配: 表=IOT_Sales, rowid=4938, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:13:56.393284 更新操作影响行数: 1
2025-05-27 09:13:56.461035 第29行 找到匹配: 表=IOT_Sales, rowid=3973, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:56.462617 删除操作影响行数: 1
2025-05-27 09:13:56.528686 第30行 找到匹配: 表=IOT_Sales, rowid=3969, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:13:56.529934 更新操作影响行数: 1
2025-05-27 09:13:56.597070 第31行 找到匹配: 表=IOT_Sales, rowid=6078, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:13:56.598128 删除操作影响行数: 1
2025-05-27 09:13:56.765316 第33行 找到匹配: 表=IOT_Sales, rowid=8085, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:56.766315 删除操作影响行数: 1
2025-05-27 09:13:57.134826 第37行 找到匹配: 表=IOT_Sales, rowid=13811, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:13:57.136418 更新操作影响行数: 1
2025-05-27 09:13:57.237053 第38行 找到匹配: 表=IOT_Sales, rowid=17525, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:13:57.238381 删除操作影响行数: 1
2025-05-27 09:13:57.358423 第39行 找到匹配: 表=IOT_Sales, rowid=17072, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:57.359389 删除操作影响行数: 1
2025-05-27 09:13:57.460285 第40行 找到匹配: 表=IOT_Sales, rowid=17191, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:13:57.461384 删除操作影响行数: 1
2025-05-27 09:13:57.653719 第42行 找到匹配: 表=IOT_Sales, rowid=16362, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:13:57.655010 更新操作影响行数: 1
2025-05-27 09:13:57.733602 第43行 找到匹配: 表=IOT_Sales, rowid=15218, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:57.734760 删除操作影响行数: 1
2025-05-27 09:13:57.817705 第44行 找到匹配: 表=IOT_Sales, rowid=18049, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:13:57.818687 删除操作影响行数: 1
2025-05-27 09:13:57.895779 第45行 找到匹配: 表=IOT_Sales, rowid=17995, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:13:57.896753 删除操作影响行数: 1
2025-05-27 09:13:57.967468 第46行 找到匹配: 表=IOT_Sales, rowid=17762, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:13:57.968429 删除操作影响行数: 1
2025-05-27 09:13:58.137849 第48行 找到匹配: 表=IOT_Sales, rowid=15339, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:58.138889 删除操作影响行数: 1
2025-05-27 09:13:58.208075 第49行 找到匹配: 表=IOT_Sales, rowid=15226, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:13:58.209399 删除操作影响行数: 1
2025-05-27 09:13:58.278803 第50行 找到匹配: 表=IOT_Sales, rowid=17296, 原金额=5.0, 退款=20.0, 新金额=-15.0
2025-05-27 09:13:58.280023 删除操作影响行数: 1
2025-05-27 09:13:58.365458 第51行 找到匹配: 表=IOT_Sales, rowid=18228, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:13:58.366181 删除操作影响行数: 1
2025-05-27 09:13:58.529795 第53行 找到匹配: 表=IOT_Sales, rowid=15397, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:13:58.530795 删除操作影响行数: 1
2025-05-27 09:13:58.602439 第54行 找到匹配: 表=IOT_Sales, rowid=15398, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:58.604082 删除操作影响行数: 1
2025-05-27 09:13:58.699207 第55行 找到匹配: 表=IOT_Sales, rowid=20114, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:13:58.700079 删除操作影响行数: 1
2025-05-27 09:13:58.810093 第56行 找到匹配: 表=IOT_Sales, rowid=20070, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:13:58.812105 删除操作影响行数: 1
2025-05-27 09:13:58.913947 第57行 找到匹配: 表=IOT_Sales, rowid=22389, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:13:58.914923 删除操作影响行数: 1
2025-05-27 09:13:58.998969 第58行 找到匹配: 表=IOT_Sales, rowid=20075, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:13:59.000170 删除操作影响行数: 1
2025-05-27 09:13:59.088036 第59行 找到匹配: 表=IOT_Sales, rowid=22388, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:13:59.089987 删除操作影响行数: 1
2025-05-27 09:13:59.177935 第60行 找到匹配: 表=IOT_Sales, rowid=19068, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:59.178649 删除操作影响行数: 1
2025-05-27 09:13:59.369889 第62行 找到匹配: 表=IOT_Sales, rowid=20115, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:13:59.371338 删除操作影响行数: 1
2025-05-27 09:13:59.456644 第63行 找到匹配: 表=IOT_Sales, rowid=19059, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:59.457874 删除操作影响行数: 1
2025-05-27 09:13:59.534071 第64行 找到匹配: 表=IOT_Sales, rowid=21732, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:13:59.535134 更新操作影响行数: 1
2025-05-27 09:13:59.622735 第65行 找到匹配: 表=IOT_Sales, rowid=19701, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:59.624239 删除操作影响行数: 1
2025-05-27 09:13:59.701219 第66行 找到匹配: 表=IOT_Sales, rowid=18656, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:59.702210 删除操作影响行数: 1
2025-05-27 09:13:59.893428 第68行 找到匹配: 表=IOT_Sales, rowid=18867, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:13:59.894551 删除操作影响行数: 1
2025-05-27 09:13:59.978205 第69行 找到匹配: 表=IOT_Sales, rowid=22436, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:13:59.979247 删除操作影响行数: 1
2025-05-27 09:14:00.074926 第70行 找到匹配: 表=IOT_Sales, rowid=22435, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:00.076195 删除操作影响行数: 1
2025-05-27 09:14:00.161265 第71行 找到匹配: 表=IOT_Sales, rowid=20373, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:00.162337 删除操作影响行数: 1
2025-05-27 09:14:00.246106 第72行 找到匹配: 表=IOT_Sales, rowid=20116, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:00.246896 删除操作影响行数: 1
2025-05-27 09:14:00.324075 第73行 找到匹配: 表=IOT_Sales, rowid=20553, 原金额=5.0, 退款=3.0, 新金额=2.0
2025-05-27 09:14:00.325296 更新操作影响行数: 1
2025-05-27 09:14:00.509882 第75行 找到匹配: 表=IOT_Sales, rowid=18891, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:00.511119 删除操作影响行数: 1
2025-05-27 09:14:00.580815 第76行 找到匹配: 表=IOT_Sales, rowid=22640, 原金额=20.0, 退款=10.0, 新金额=10.0
2025-05-27 09:14:00.581564 更新操作影响行数: 1
2025-05-27 09:14:00.666274 第77行 找到匹配: 表=IOT_Sales, rowid=26168, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:00.667473 删除操作影响行数: 1
2025-05-27 09:14:00.746091 第78行 找到匹配: 表=IOT_Sales, rowid=29243, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:00.747025 删除操作影响行数: 1
2025-05-27 09:14:00.820646 第79行 找到匹配: 表=IOT_Sales, rowid=29462, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:00.821915 删除操作影响行数: 1
2025-05-27 09:14:00.900280 第80行 找到匹配: 表=IOT_Sales, rowid=29144, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:00.901387 删除操作影响行数: 1
2025-05-27 09:14:00.993736 第81行 找到匹配: 表=IOT_Sales, rowid=29131, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:00.994486 删除操作影响行数: 1
2025-05-27 09:14:01.072596 第82行 找到匹配: 表=IOT_Sales, rowid=29127, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:01.073516 删除操作影响行数: 1
2025-05-27 09:14:01.156392 第83行 找到匹配: 表=IOT_Sales, rowid=31949, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:01.157833 删除操作影响行数: 1
2025-05-27 09:14:01.239014 第84行 找到匹配: 表=IOT_Sales, rowid=31624, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:01.240485 删除操作影响行数: 1
2025-05-27 09:14:01.314087 第85行 找到匹配: 表=IOT_Sales, rowid=31512, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:01.314836 删除操作影响行数: 1
2025-05-27 09:14:01.404149 第86行 找到匹配: 表=IOT_Sales, rowid=34137, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:01.405504 删除操作影响行数: 1
2025-05-27 09:14:01.489088 第87行 找到匹配: 表=IOT_Sales, rowid=35576, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:01.491238 删除操作影响行数: 1
2025-05-27 09:14:01.590950 第88行 找到匹配: 表=IOT_Sales, rowid=34373, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:01.591956 删除操作影响行数: 1
2025-05-27 09:14:01.672311 第89行 找到匹配: 表=IOT_Sales, rowid=33940, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:01.673398 删除操作影响行数: 1
2025-05-27 09:14:01.755295 第90行 找到匹配: 表=IOT_Sales, rowid=33841, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:01.756912 删除操作影响行数: 1
2025-05-27 09:14:01.841212 第91行 找到匹配: 表=IOT_Sales, rowid=38957, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:01.842287 删除操作影响行数: 1
2025-05-27 09:14:01.924854 第92行 找到匹配: 表=IOT_Sales, rowid=38931, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:01.925802 删除操作影响行数: 1
2025-05-27 09:14:02.000128 第93行 找到匹配: 表=IOT_Sales, rowid=39155, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:02.001125 更新操作影响行数: 1
2025-05-27 09:14:02.074120 第94行 找到匹配: 表=IOT_Sales, rowid=38262, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:02.075224 删除操作影响行数: 1
2025-05-27 09:14:02.154294 第95行 找到匹配: 表=IOT_Sales, rowid=40506, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:02.155041 更新操作影响行数: 1
2025-05-27 09:14:02.226668 第96行 找到匹配: 表=IOT_Sales, rowid=39130, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:02.227708 更新操作影响行数: 1
2025-05-27 09:14:02.320352 第97行 找到匹配: 表=IOT_Sales, rowid=39125, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:02.321504 更新操作影响行数: 1
2025-05-27 09:14:02.405139 第98行 找到匹配: 表=IOT_Sales, rowid=39125, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:02.406122 删除操作影响行数: 1
2025-05-27 09:14:02.491387 第99行 找到匹配: 表=IOT_Sales, rowid=40652, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:02.492520 删除操作影响行数: 1
2025-05-27 09:14:02.579613 第100行 找到匹配: 表=IOT_Sales, rowid=40020, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:02.580645 删除操作影响行数: 1
2025-05-27 09:14:02.668913 第101行 找到匹配: 表=IOT_Sales, rowid=41100, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:02.669770 删除操作影响行数: 1
2025-05-27 09:14:02.750608 第102行 找到匹配: 表=IOT_Sales, rowid=38879, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:02.751396 删除操作影响行数: 1
2025-05-27 09:14:02.833175 第103行 找到匹配: 表=IOT_Sales, rowid=39240, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:02.834108 删除操作影响行数: 1
2025-05-27 09:14:02.918136 第104行 找到匹配: 表=IOT_Sales, rowid=38927, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:02.919194 删除操作影响行数: 1
2025-05-27 09:14:03.010142 第105行 找到匹配: 表=IOT_Sales, rowid=38107, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:03.011606 更新操作影响行数: 1
2025-05-27 09:14:03.084330 第106行 找到匹配: 表=IOT_Sales, rowid=42622, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:03.085342 删除操作影响行数: 1
2025-05-27 09:14:03.166156 第107行 找到匹配: 表=IOT_Sales, rowid=43555, 原金额=8.0, 退款=5.0, 新金额=3.0
2025-05-27 09:14:03.167737 更新操作影响行数: 1
2025-05-27 09:14:03.238362 第108行 找到匹配: 表=IOT_Sales, rowid=43783, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:03.239259 删除操作影响行数: 1
2025-05-27 09:14:03.315459 第109行 找到匹配: 表=IOT_Sales, rowid=45092, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:03.316907 更新操作影响行数: 1
2025-05-27 09:14:03.385740 第110行 找到匹配: 表=IOT_Sales, rowid=42722, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:03.386891 删除操作影响行数: 1
2025-05-27 09:14:03.465451 第111行 找到匹配: 表=IOT_Sales, rowid=43711, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:03.466420 删除操作影响行数: 1
2025-05-27 09:14:03.536720 第112行 找到匹配: 表=IOT_Sales, rowid=43784, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:03.537647 删除操作影响行数: 1
2025-05-27 09:14:03.604551 第113行 找到匹配: 表=IOT_Sales, rowid=44793, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:03.605634 删除操作影响行数: 1
2025-05-27 09:14:03.673760 第114行 找到匹配: 表=IOT_Sales, rowid=45000, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:03.675059 删除操作影响行数: 1
2025-05-27 09:14:03.742621 第115行 找到匹配: 表=IOT_Sales, rowid=43229, 原金额=8.0, 退款=5.0, 新金额=3.0
2025-05-27 09:14:03.743929 更新操作影响行数: 1
2025-05-27 09:14:03.813815 第116行 找到匹配: 表=IOT_Sales, rowid=44585, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:03.814500 删除操作影响行数: 1
2025-05-27 09:14:03.885098 第117行 找到匹配: 表=IOT_Sales, rowid=44609, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:03.886013 删除操作影响行数: 1
2025-05-27 09:14:03.964555 第118行 找到匹配: 表=IOT_Sales, rowid=44537, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:03.965608 删除操作影响行数: 1
2025-05-27 09:14:04.050515 第119行 找到匹配: 表=IOT_Sales, rowid=44567, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:04.051879 删除操作影响行数: 1
2025-05-27 09:14:04.141621 第120行 找到匹配: 表=IOT_Sales, rowid=43812, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:04.142581 删除操作影响行数: 1
2025-05-27 09:14:04.218970 第121行 找到匹配: 表=IOT_Sales, rowid=43584, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:04.220152 更新操作影响行数: 1
2025-05-27 09:14:04.311713 第122行 找到匹配: 表=IOT_Sales, rowid=43596, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:04.312910 更新操作影响行数: 1
2025-05-27 09:14:04.398592 第123行 找到匹配: 表=IOT_Sales, rowid=43584, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:04.399389 删除操作影响行数: 1
2025-05-27 09:14:04.483394 第124行 找到匹配: 表=IOT_Sales, rowid=43596, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:04.484544 删除操作影响行数: 1
2025-05-27 09:14:04.562436 第125行 找到匹配: 表=IOT_Sales, rowid=43005, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:04.564024 删除操作影响行数: 1
2025-05-27 09:14:04.643514 第126行 找到匹配: 表=IOT_Sales, rowid=43003, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:04.644620 删除操作影响行数: 1
2025-05-27 09:14:04.727294 第127行 找到匹配: 表=IOT_Sales, rowid=42770, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:04.728310 删除操作影响行数: 1
2025-05-27 09:14:04.802932 第128行 找到匹配: 表=IOT_Sales, rowid=42789, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:04.804266 更新操作影响行数: 1
2025-05-27 09:14:04.874299 第129行 找到匹配: 表=IOT_Sales, rowid=43208, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:04.875319 删除操作影响行数: 1
2025-05-27 09:14:04.951687 第130行 找到匹配: 表=IOT_Sales, rowid=42905, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:04.952664 删除操作影响行数: 1
2025-05-27 09:14:05.020979 第131行 找到匹配: 表=IOT_Sales, rowid=42779, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:05.021937 删除操作影响行数: 1
2025-05-27 09:14:05.094346 第132行 找到匹配: 表=IOT_Sales, rowid=43035, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:05.095281 删除操作影响行数: 1
2025-05-27 09:14:05.163972 第133行 找到匹配: 表=IOT_Sales, rowid=42773, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:05.164701 删除操作影响行数: 1
2025-05-27 09:14:05.233151 第134行 找到匹配: 表=IOT_Sales, rowid=44597, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:05.234095 更新操作影响行数: 1
2025-05-27 09:14:05.301446 第135行 找到匹配: 表=IOT_Sales, rowid=42650, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:05.302768 删除操作影响行数: 1
2025-05-27 09:14:05.370127 第136行 找到匹配: 表=IOT_Sales, rowid=45245, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:05.371221 删除操作影响行数: 1
2025-05-27 09:14:05.441372 第137行 找到匹配: 表=IOT_Sales, rowid=45365, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:05.442371 删除操作影响行数: 1
2025-05-27 09:14:05.514973 第138行 找到匹配: 表=IOT_Sales, rowid=45310, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:05.516211 删除操作影响行数: 1
2025-05-27 09:14:05.586013 第139行 找到匹配: 表=IOT_Sales, rowid=45326, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:05.586747 删除操作影响行数: 1
2025-05-27 09:14:05.655779 第140行 找到匹配: 表=IOT_Sales, rowid=45327, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:05.656664 删除操作影响行数: 1
2025-05-27 09:14:05.724533 第141行 找到匹配: 表=IOT_Sales, rowid=45246, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:05.725909 删除操作影响行数: 1
2025-05-27 09:14:05.801156 第142行 找到匹配: 表=IOT_Sales, rowid=45546, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:05.802109 删除操作影响行数: 1
2025-05-27 09:14:05.883777 第143行 找到匹配: 表=IOT_Sales, rowid=45911, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:05.884678 删除操作影响行数: 1
2025-05-27 09:14:05.961310 第144行 找到匹配: 表=IOT_Sales, rowid=45918, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:05.962899 删除操作影响行数: 1
2025-05-27 09:14:06.055084 第145行 找到匹配: 表=IOT_Sales, rowid=45492, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:06.056896 删除操作影响行数: 1
2025-05-27 09:14:06.151013 第146行 找到匹配: 表=IOT_Sales, rowid=46029, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:06.151760 删除操作影响行数: 1
2025-05-27 09:14:06.312093 第147行 找到匹配: 表=IOT_Sales, rowid=46412, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:14:06.313145 删除操作影响行数: 1
2025-05-27 09:14:06.390731 第148行 找到匹配: 表=IOT_Sales, rowid=46704, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:14:06.391860 删除操作影响行数: 1
2025-05-27 09:14:06.470217 第149行 找到匹配: 表=IOT_Sales, rowid=46859, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:14:06.471337 删除操作影响行数: 1
2025-05-27 09:14:06.548289 第150行 找到匹配: 表=IOT_Sales, rowid=45436, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:06.548978 更新操作影响行数: 1
2025-05-27 09:14:06.619407 第151行 找到匹配: 表=IOT_Sales, rowid=45552, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:06.620032 删除操作影响行数: 1
2025-05-27 09:14:06.697669 第152行 找到匹配: 表=IOT_Sales, rowid=46112, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:06.698589 删除操作影响行数: 1
2025-05-27 09:14:06.769024 第153行 找到匹配: 表=IOT_Sales, rowid=45532, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:06.770234 删除操作影响行数: 1
2025-05-27 09:14:06.840061 第154行 找到匹配: 表=IOT_Sales, rowid=45467, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:06.841049 删除操作影响行数: 1
2025-05-27 09:14:06.911470 第155行 找到匹配: 表=IOT_Sales, rowid=48339, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:06.912437 删除操作影响行数: 1
2025-05-27 09:14:06.979326 第156行 找到匹配: 表=IOT_Sales, rowid=46599, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:06.980407 更新操作影响行数: 1
2025-05-27 09:14:07.047503 第157行 找到匹配: 表=IOT_Sales, rowid=49199, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:07.048528 更新操作影响行数: 1
2025-05-27 09:14:07.113196 第158行 找到匹配: 表=IOT_Sales, rowid=48866, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:07.113993 删除操作影响行数: 1
2025-05-27 09:14:07.186409 第159行 找到匹配: 表=IOT_Sales, rowid=49136, 原金额=15.0, 退款=10.0, 新金额=5.0
2025-05-27 09:14:07.187405 更新操作影响行数: 1
2025-05-27 09:14:07.259849 第160行 找到匹配: 表=IOT_Sales, rowid=50820, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:07.260936 删除操作影响行数: 1
2025-05-27 09:14:07.330583 第161行 找到匹配: 表=IOT_Sales, rowid=48768, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:07.331709 删除操作影响行数: 1
2025-05-27 09:14:07.401845 第162行 找到匹配: 表=IOT_Sales, rowid=48913, 原金额=15.0, 退款=10.0, 新金额=5.0
2025-05-27 09:14:07.402792 更新操作影响行数: 1
2025-05-27 09:14:07.480003 第163行 找到匹配: 表=IOT_Sales, rowid=50717, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:07.481579 删除操作影响行数: 1
2025-05-27 09:14:07.557099 第164行 找到匹配: 表=IOT_Sales, rowid=49546, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:07.558052 删除操作影响行数: 1
2025-05-27 09:14:07.640367 第165行 找到匹配: 表=IOT_Sales, rowid=49094, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:07.641355 删除操作影响行数: 1
2025-05-27 09:14:07.724274 第166行 找到匹配: 表=IOT_Sales, rowid=48706, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:07.725313 删除操作影响行数: 1
2025-05-27 09:14:07.857394 第167行 找到匹配: 表=IOT_Sales, rowid=48770, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:07.858608 删除操作影响行数: 1
2025-05-27 09:14:07.936565 第168行 找到匹配: 表=IOT_Sales, rowid=49004, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:07.937259 更新操作影响行数: 1
2025-05-27 09:14:08.011419 第169行 找到匹配: 表=IOT_Sales, rowid=48639, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:08.012487 删除操作影响行数: 1
2025-05-27 09:14:08.085693 第170行 找到匹配: 表=IOT_Sales, rowid=48707, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:08.086756 删除操作影响行数: 1
2025-05-27 09:14:08.166890 第171行 找到匹配: 表=IOT_Sales, rowid=49214, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:08.167965 更新操作影响行数: 1
2025-05-27 09:14:08.238927 第172行 找到匹配: 表=IOT_Sales, rowid=48646, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:08.239841 删除操作影响行数: 1
2025-05-27 09:14:08.317933 第173行 找到匹配: 表=IOT_Sales, rowid=49140, 原金额=15.0, 退款=10.0, 新金额=5.0
2025-05-27 09:14:08.319263 更新操作影响行数: 1
2025-05-27 09:14:08.392745 第174行 找到匹配: 表=IOT_Sales, rowid=49077, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:08.393733 删除操作影响行数: 1
2025-05-27 09:14:08.469132 第175行 找到匹配: 表=IOT_Sales, rowid=49633, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:08.470396 删除操作影响行数: 1
2025-05-27 09:14:08.541634 第176行 找到匹配: 表=IOT_Sales, rowid=49819, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:08.542802 更新操作影响行数: 1
2025-05-27 09:14:08.617460 第177行 找到匹配: 表=IOT_Sales, rowid=48645, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:08.618442 删除操作影响行数: 1
2025-05-27 09:14:08.688052 第178行 找到匹配: 表=IOT_Sales, rowid=48701, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:08.688834 删除操作影响行数: 1
2025-05-27 09:14:08.760047 第179行 找到匹配: 表=IOT_Sales, rowid=48742, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:08.761189 删除操作影响行数: 1
2025-05-27 09:14:08.838118 第180行 找到匹配: 表=IOT_Sales, rowid=50670, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:08.839227 删除操作影响行数: 1
2025-05-27 09:14:08.905122 第181行 找到匹配: 表=IOT_Sales, rowid=49130, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:08.906399 删除操作影响行数: 1
2025-05-27 09:14:08.972597 第182行 找到匹配: 表=IOT_Sales, rowid=48834, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:08.973938 删除操作影响行数: 1
2025-05-27 09:14:09.040536 第183行 找到匹配: 表=IOT_Sales, rowid=48740, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:09.041921 删除操作影响行数: 1
2025-05-27 09:14:09.108690 第184行 找到匹配: 表=IOT_Sales, rowid=49075, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:09.109823 删除操作影响行数: 1
2025-05-27 09:14:09.175982 第185行 找到匹配: 表=IOT_Sales, rowid=48836, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:09.177356 删除操作影响行数: 1
2025-05-27 09:14:09.241689 第186行 找到匹配: 表=IOT_Sales, rowid=48807, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:09.242497 删除操作影响行数: 1
2025-05-27 09:14:09.309533 第187行 找到匹配: 表=IOT_Sales, rowid=50498, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:09.310173 删除操作影响行数: 1
2025-05-27 09:14:09.377414 第188行 找到匹配: 表=IOT_Sales, rowid=48648, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:09.378086 删除操作影响行数: 1
2025-05-27 09:14:09.442422 第189行 找到匹配: 表=IOT_Sales, rowid=50544, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:09.443219 删除操作影响行数: 1
2025-05-27 09:14:09.516606 第190行 找到匹配: 表=IOT_Sales, rowid=48860, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:09.517884 删除操作影响行数: 1
2025-05-27 09:14:09.602339 第191行 找到匹配: 表=IOT_Sales, rowid=50844, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:09.603322 删除操作影响行数: 1
2025-05-27 09:14:09.677405 第192行 找到匹配: 表=IOT_Sales, rowid=48953, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:09.678819 更新操作影响行数: 1
2025-05-27 09:14:09.758680 第193行 找到匹配: 表=IOT_Sales, rowid=48963, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:09.759879 删除操作影响行数: 1
2025-05-27 09:14:09.847524 第194行 找到匹配: 表=IOT_Sales, rowid=48821, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:09.848845 删除操作影响行数: 1
2025-05-27 09:14:09.956139 第195行 找到匹配: 表=IOT_Sales, rowid=49121, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:09.958150 删除操作影响行数: 1
2025-05-27 09:14:10.039468 第196行 找到匹配: 表=IOT_Sales, rowid=49971, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:10.040368 更新操作影响行数: 1
2025-05-27 09:14:10.128353 第197行 找到匹配: 表=IOT_Sales, rowid=48880, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:10.129493 删除操作影响行数: 1
2025-05-27 09:14:10.214749 第198行 找到匹配: 表=IOT_Sales, rowid=48778, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:10.215775 删除操作影响行数: 1
2025-05-27 09:14:10.294808 第199行 找到匹配: 表=IOT_Sales, rowid=49519, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:10.296141 删除操作影响行数: 1
2025-05-27 09:14:10.375630 第200行 找到匹配: 表=IOT_Sales, rowid=49936, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:10.376602 删除操作影响行数: 1
2025-05-27 09:14:10.447723 第201行 找到匹配: 表=IOT_Sales, rowid=50496, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:10.448658 删除操作影响行数: 1
2025-05-27 09:14:10.515539 第202行 找到匹配: 表=IOT_Sales, rowid=48632, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:10.517286 删除操作影响行数: 1
2025-05-27 09:14:10.586223 第203行 找到匹配: 表=IOT_Sales, rowid=48819, 原金额=5.55, 退款=10.0, 新金额=-4.45
2025-05-27 09:14:10.587362 删除操作影响行数: 1
2025-05-27 09:14:10.658978 第204行 找到匹配: 表=IOT_Sales, rowid=50852, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:10.659621 删除操作影响行数: 1
2025-05-27 09:14:10.727009 第205行 找到匹配: 表=IOT_Sales, rowid=50247, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:10.728047 删除操作影响行数: 1
2025-05-27 09:14:10.796139 第206行 找到匹配: 表=IOT_Sales, rowid=49616, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:10.797718 更新操作影响行数: 1
2025-05-27 09:14:10.864558 第207行 找到匹配: 表=IOT_Sales, rowid=48815, 原金额=5.55, 退款=10.0, 新金额=-4.45
2025-05-27 09:14:10.865883 删除操作影响行数: 1
2025-05-27 09:14:10.934203 第208行 找到匹配: 表=IOT_Sales, rowid=50050, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:10.935196 删除操作影响行数: 1
2025-05-27 09:14:11.004929 第209行 找到匹配: 表=IOT_Sales, rowid=50460, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:11.006034 删除操作影响行数: 1
2025-05-27 09:14:11.077210 第210行 找到匹配: 表=IOT_Sales, rowid=49425, 原金额=10.0, 退款=8.0, 新金额=2.0
2025-05-27 09:14:11.078256 更新操作影响行数: 1
2025-05-27 09:14:11.147709 第211行 找到匹配: 表=IOT_Sales, rowid=48801, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:11.148391 删除操作影响行数: 1
2025-05-27 09:14:11.392660 第212行 找到匹配: 表=IOT_Sales, rowid=49593, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:11.393654 删除操作影响行数: 1
2025-05-27 09:14:11.486266 第213行 找到匹配: 表=IOT_Sales, rowid=50443, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:11.487364 删除操作影响行数: 1
2025-05-27 09:14:11.564690 第214行 找到匹配: 表=IOT_Sales, rowid=48711, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:11.566102 删除操作影响行数: 1
2025-05-27 09:14:11.647000 第215行 找到匹配: 表=IOT_Sales, rowid=50444, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:11.647965 删除操作影响行数: 1
2025-05-27 09:14:11.742443 第216行 找到匹配: 表=IOT_Sales, rowid=49616, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:11.744873 删除操作影响行数: 1
2025-05-27 09:14:11.820049 第217行 找到匹配: 表=IOT_Sales, rowid=48661, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:11.821088 删除操作影响行数: 1
2025-05-27 09:14:11.897073 第218行 找到匹配: 表=IOT_Sales, rowid=49425, 原金额=2.0, 退款=5.0, 新金额=-3.0
2025-05-27 09:14:11.898056 删除操作影响行数: 1
2025-05-27 09:14:11.969066 第219行 找到匹配: 表=IOT_Sales, rowid=48683, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:11.970894 删除操作影响行数: 1
2025-05-27 09:14:12.042820 第220行 找到匹配: 表=IOT_Sales, rowid=50436, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:12.043508 删除操作影响行数: 1
2025-05-27 09:14:12.118074 第221行 找到匹配: 表=IOT_Sales, rowid=48845, 原金额=15.0, 退款=10.0, 新金额=5.0
2025-05-27 09:14:12.119361 更新操作影响行数: 1
2025-05-27 09:14:12.192695 第222行 找到匹配: 表=IOT_Sales, rowid=49054, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:12.193602 删除操作影响行数: 1
2025-05-27 09:14:12.264263 第223行 找到匹配: 表=IOT_Sales, rowid=49834, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:12.265080 删除操作影响行数: 1
2025-05-27 09:14:12.334216 第224行 找到匹配: 表=IOT_Sales, rowid=49517, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:12.335098 更新操作影响行数: 1
2025-05-27 09:14:12.400953 第225行 找到匹配: 表=IOT_Sales, rowid=48905, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:12.402202 删除操作影响行数: 1
2025-05-27 09:14:12.472689 第226行 找到匹配: 表=IOT_Sales, rowid=48936, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:12.473603 删除操作影响行数: 1
2025-05-27 09:14:12.541764 第227行 找到匹配: 表=IOT_Sales, rowid=49555, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:12.543033 删除操作影响行数: 1
2025-05-27 09:14:12.607355 第228行 找到匹配: 表=IOT_Sales, rowid=49919, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:12.608026 删除操作影响行数: 1
2025-05-27 09:14:12.672828 第229行 找到匹配: 表=IOT_Sales, rowid=48930, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:12.673597 删除操作影响行数: 1
2025-05-27 09:14:12.743072 第230行 找到匹配: 表=IOT_Sales, rowid=49013, 原金额=15.0, 退款=10.0, 新金额=5.0
2025-05-27 09:14:12.744383 更新操作影响行数: 1
2025-05-27 09:14:12.813805 第231行 找到匹配: 表=IOT_Sales, rowid=49225, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:12.814851 删除操作影响行数: 1
2025-05-27 09:14:12.888953 第232行 找到匹配: 表=IOT_Sales, rowid=48839, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:12.890433 删除操作影响行数: 1
2025-05-27 09:14:12.962706 第233行 找到匹配: 表=IOT_Sales, rowid=50742, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:12.964212 删除操作影响行数: 1
2025-05-27 09:14:13.040252 第234行 找到匹配: 表=IOT_Sales, rowid=50576, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:13.041233 删除操作影响行数: 1
2025-05-27 09:14:13.113765 第235行 找到匹配: 表=IOT_Sales, rowid=49812, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:13.114975 删除操作影响行数: 1
2025-05-27 09:14:13.223762 第236行 找到匹配: 表=IOT_Sales, rowid=48984, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:13.225180 删除操作影响行数: 1
2025-05-27 09:14:13.313108 第237行 找到匹配: 表=IOT_Sales, rowid=50083, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:13.314120 删除操作影响行数: 1
2025-05-27 09:14:13.387424 第238行 找到匹配: 表=IOT_Sales, rowid=48962, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:13.388144 删除操作影响行数: 1
2025-05-27 09:14:13.468475 第239行 找到匹配: 表=IOT_Sales, rowid=49722, 原金额=15.0, 退款=10.0, 新金额=5.0
2025-05-27 09:14:13.469455 更新操作影响行数: 1
2025-05-27 09:14:13.541338 第240行 找到匹配: 表=IOT_Sales, rowid=50814, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:13.542725 删除操作影响行数: 1
2025-05-27 09:14:13.614622 第241行 找到匹配: 表=IOT_Sales, rowid=48982, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:13.615648 删除操作影响行数: 1
2025-05-27 09:14:13.686720 第242行 找到匹配: 表=IOT_Sales, rowid=49065, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:13.687636 删除操作影响行数: 1
2025-05-27 09:14:13.763994 第243行 找到匹配: 表=IOT_Sales, rowid=50207, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:13.765265 删除操作影响行数: 1
2025-05-27 09:14:13.837939 第244行 找到匹配: 表=IOT_Sales, rowid=49988, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:13.839228 删除操作影响行数: 1
2025-05-27 09:14:13.913627 第245行 找到匹配: 表=IOT_Sales, rowid=49415, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:13.914804 删除操作影响行数: 1
2025-05-27 09:14:13.983475 第246行 找到匹配: 表=IOT_Sales, rowid=48772, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:13.984550 删除操作影响行数: 1
2025-05-27 09:14:14.054149 第247行 找到匹配: 表=IOT_Sales, rowid=48718, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:14.055050 删除操作影响行数: 1
2025-05-27 09:14:14.121472 第248行 找到匹配: 表=IOT_Sales, rowid=51042, 原金额=15.0, 退款=20.0, 新金额=-5.0
2025-05-27 09:14:14.122863 删除操作影响行数: 1
2025-05-27 09:14:14.217954 第249行 找到匹配: 表=IOT_Sales, rowid=51653, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:14.219021 删除操作影响行数: 1
2025-05-27 09:14:14.291443 第250行 找到匹配: 表=IOT_Sales, rowid=51450, 原金额=20.0, 退款=20.0, 新金额=0.0
2025-05-27 09:14:14.292369 删除操作影响行数: 1
2025-05-27 09:14:14.363081 第251行 找到匹配: 表=IOT_Sales, rowid=51449, 原金额=20.0, 退款=20.0, 新金额=0.0
2025-05-27 09:14:14.364099 删除操作影响行数: 1
2025-05-27 09:14:14.434927 第252行 找到匹配: 表=IOT_Sales, rowid=53114, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:14.436014 更新操作影响行数: 1
2025-05-27 09:14:14.508673 第253行 找到匹配: 表=IOT_Sales, rowid=53113, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:14.509679 删除操作影响行数: 1
2025-05-27 09:14:14.581297 第254行 找到匹配: 表=IOT_Sales, rowid=54828, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:14.582304 删除操作影响行数: 1
2025-05-27 09:14:14.652546 第255行 找到匹配: 表=IOT_Sales, rowid=53456, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:14.653319 删除操作影响行数: 1
2025-05-27 09:14:14.728608 第256行 找到匹配: 表=IOT_Sales, rowid=53500, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:14.729649 删除操作影响行数: 1
2025-05-27 09:14:14.798612 第257行 找到匹配: 表=IOT_Sales, rowid=53182, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:14.799571 删除操作影响行数: 1
2025-05-27 09:14:14.873482 第258行 找到匹配: 表=IOT_Sales, rowid=60259, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:14:14.874431 删除操作影响行数: 1
2025-05-27 09:14:14.951702 第259行 找到匹配: 表=IOT_Sales, rowid=60254, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:14:14.953054 删除操作影响行数: 1
2025-05-27 09:14:15.041240 第260行 找到匹配: 表=IOT_Sales, rowid=56257, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:15.042275 删除操作影响行数: 1
2025-05-27 09:14:15.120953 第261行 找到匹配: 表=IOT_Sales, rowid=56495, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:15.121978 删除操作影响行数: 1
2025-05-27 09:14:15.201100 第262行 找到匹配: 表=IOT_Sales, rowid=56958, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:15.202364 删除操作影响行数: 1
2025-05-27 09:14:15.282957 第263行 找到匹配: 表=IOT_Sales, rowid=56361, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:15.284017 更新操作影响行数: 1
2025-05-27 09:14:15.373378 第264行 找到匹配: 表=IOT_Sales, rowid=56276, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:15.374358 删除操作影响行数: 1
2025-05-27 09:14:15.451129 第265行 找到匹配: 表=IOT_Sales, rowid=56642, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:15.452068 删除操作影响行数: 1
2025-05-27 09:14:15.531834 第266行 找到匹配: 表=IOT_Sales, rowid=57302, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:15.532950 删除操作影响行数: 1
2025-05-27 09:14:15.624420 第267行 找到匹配: 表=IOT_Sales, rowid=55971, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:15.625736 删除操作影响行数: 1
2025-05-27 09:14:15.714123 第268行 找到匹配: 表=IOT_Sales, rowid=57255, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:15.714825 删除操作影响行数: 1
2025-05-27 09:14:15.793096 第269行 找到匹配: 表=IOT_Sales, rowid=56565, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:15.794155 删除操作影响行数: 1
2025-05-27 09:14:15.873778 第270行 找到匹配: 表=IOT_Sales, rowid=57330, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:15.874879 更新操作影响行数: 1
2025-05-27 09:14:15.955558 第271行 找到匹配: 表=IOT_Sales, rowid=56152, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:15.957349 删除操作影响行数: 1
2025-05-27 09:14:16.029216 第272行 找到匹配: 表=IOT_Sales, rowid=56818, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:16.030164 删除操作影响行数: 1
2025-05-27 09:14:16.106326 第273行 找到匹配: 表=IOT_Sales, rowid=56078, 原金额=5.0, 退款=8.0, 新金额=-3.0
2025-05-27 09:14:16.107260 删除操作影响行数: 1
2025-05-27 09:14:16.181110 第274行 找到匹配: 表=IOT_Sales, rowid=56464, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:16.181815 更新操作影响行数: 1
2025-05-27 09:14:16.363371 第275行 找到匹配: 表=IOT_Sales, rowid=56464, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:16.364333 删除操作影响行数: 1
2025-05-27 09:14:16.439462 第276行 找到匹配: 表=IOT_Sales, rowid=56407, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:16.440391 删除操作影响行数: 1
2025-05-27 09:14:16.511661 第277行 找到匹配: 表=IOT_Sales, rowid=56144, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:16.512390 删除操作影响行数: 1
2025-05-27 09:14:16.585303 第278行 找到匹配: 表=IOT_Sales, rowid=56154, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:16.586260 删除操作影响行数: 1
2025-05-27 09:14:16.659243 第279行 找到匹配: 表=IOT_Sales, rowid=56242, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:16.660229 更新操作影响行数: 1
2025-05-27 09:14:16.747661 第280行 找到匹配: 表=IOT_Sales, rowid=56579, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:16.748379 删除操作影响行数: 1
2025-05-27 09:14:16.838407 第281行 找到匹配: 表=IOT_Sales, rowid=56242, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:16.839436 删除操作影响行数: 1
2025-05-27 09:14:16.929118 第282行 找到匹配: 表=IOT_Sales, rowid=60943, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:16.930935 删除操作影响行数: 1
2025-05-27 09:14:17.022957 第283行 找到匹配: 表=IOT_Sales, rowid=61573, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:17.024284 删除操作影响行数: 1
2025-05-27 09:14:17.113921 第284行 找到匹配: 表=IOT_Sales, rowid=60868, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:17.115090 删除操作影响行数: 1
2025-05-27 09:14:17.204001 第285行 找到匹配: 表=IOT_Sales, rowid=61311, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:17.205108 删除操作影响行数: 1
2025-05-27 09:14:17.284650 第286行 找到匹配: 表=IOT_Sales, rowid=63429, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:17.285789 删除操作影响行数: 1
2025-05-27 09:14:17.374914 第287行 找到匹配: 表=IOT_Sales, rowid=64300, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:17.375978 删除操作影响行数: 1
2025-05-27 09:14:17.454840 第288行 找到匹配: 表=IOT_Sales, rowid=60881, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:17.455563 删除操作影响行数: 1
2025-05-27 09:14:17.536017 第289行 找到匹配: 表=IOT_Sales, rowid=61806, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:17.537219 删除操作影响行数: 1
2025-05-27 09:14:17.630587 第290行 找到匹配: 表=IOT_Sales, rowid=61461, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:17.631636 删除操作影响行数: 1
2025-05-27 09:14:17.734105 第291行 找到匹配: 表=IOT_Sales, rowid=61985, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:17.735115 删除操作影响行数: 1
2025-05-27 09:14:17.832540 第292行 找到匹配: 表=IOT_Sales, rowid=61613, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:17.833587 删除操作影响行数: 1
2025-05-27 09:14:17.932699 第293行 找到匹配: 表=IOT_Sales, rowid=61351, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:17.933847 更新操作影响行数: 1
2025-05-27 09:14:18.028321 第294行 找到匹配: 表=IOT_Sales, rowid=60912, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:18.029592 删除操作影响行数: 1
2025-05-27 09:14:18.120952 第295行 找到匹配: 表=IOT_Sales, rowid=60691, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:18.122002 删除操作影响行数: 1
2025-05-27 09:14:18.209688 第296行 找到匹配: 表=IOT_Sales, rowid=60685, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:18.212070 删除操作影响行数: 1
2025-05-27 09:14:18.311512 第297行 找到匹配: 表=IOT_Sales, rowid=60492, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:18.312496 删除操作影响行数: 1
2025-05-27 09:14:18.431701 第298行 找到匹配: 表=IOT_Sales, rowid=60503, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:18.432949 删除操作影响行数: 1
2025-05-27 09:14:18.521742 第299行 找到匹配: 表=IOT_Sales, rowid=67582, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:18.523401 删除操作影响行数: 1
2025-05-27 09:14:18.617422 第300行 找到匹配: 表=IOT_Sales, rowid=66456, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:18.618261 删除操作影响行数: 1
2025-05-27 09:14:18.696137 第301行 找到匹配: 表=IOT_Sales, rowid=66819, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:18.697218 删除操作影响行数: 1
2025-05-27 09:14:18.788748 第302行 找到匹配: 表=IOT_Sales, rowid=67973, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:18.790221 删除操作影响行数: 1
2025-05-27 09:14:18.870145 第303行 找到匹配: 表=IOT_Sales, rowid=65879, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:18.871190 删除操作影响行数: 1
2025-05-27 09:14:18.951205 第304行 找到匹配: 表=IOT_Sales, rowid=67785, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:18.952349 删除操作影响行数: 1
2025-05-27 09:14:19.031475 第305行 找到匹配: 表=IOT_Sales, rowid=67196, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:19.032389 删除操作影响行数: 1
2025-05-27 09:14:19.102844 第306行 找到匹配: 表=IOT_Sales, rowid=66079, 原金额=15.0, 退款=10.0, 新金额=5.0
2025-05-27 09:14:19.104392 更新操作影响行数: 1
2025-05-27 09:14:19.175845 第307行 找到匹配: 表=IOT_Sales, rowid=66940, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:19.176870 更新操作影响行数: 1
2025-05-27 09:14:19.251350 第308行 找到匹配: 表=IOT_Sales, rowid=65532, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:19.252409 删除操作影响行数: 1
2025-05-27 09:14:19.323369 第309行 找到匹配: 表=IOT_Sales, rowid=66118, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:19.324260 删除操作影响行数: 1
2025-05-27 09:14:19.394726 第310行 找到匹配: 表=IOT_Sales, rowid=65671, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:19.395386 删除操作影响行数: 1
2025-05-27 09:14:19.464292 第311行 找到匹配: 表=IOT_Sales, rowid=68941, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:19.465258 删除操作影响行数: 1
2025-05-27 09:14:19.538382 第312行 找到匹配: 表=IOT_Sales, rowid=70905, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:19.539288 删除操作影响行数: 1
2025-05-27 09:14:19.607020 第313行 找到匹配: 表=IOT_Sales, rowid=69070, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:19.608051 删除操作影响行数: 1
2025-05-27 09:14:19.673774 第314行 找到匹配: 表=IOT_Sales, rowid=69038, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:19.674470 删除操作影响行数: 1
2025-05-27 09:14:19.739667 第315行 找到匹配: 表=IOT_Sales, rowid=68657, 原金额=5.0, 退款=20.0, 新金额=-15.0
2025-05-27 09:14:19.740850 删除操作影响行数: 1
2025-05-27 09:14:19.807397 第316行 找到匹配: 表=IOT_Sales, rowid=68450, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:19.808702 删除操作影响行数: 1
2025-05-27 09:14:19.885220 第317行 找到匹配: 表=IOT_Sales, rowid=68421, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:19.886549 删除操作影响行数: 1
2025-05-27 09:14:19.966814 第318行 找到匹配: 表=IOT_Sales, rowid=68342, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:19.967876 删除操作影响行数: 1
2025-05-27 09:14:20.057465 第319行 找到匹配: 表=IOT_Sales, rowid=68381, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:20.058573 删除操作影响行数: 1
2025-05-27 09:14:20.130572 第320行 找到匹配: 表=IOT_Sales, rowid=68360, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:20.131718 删除操作影响行数: 1
2025-05-27 09:14:20.210405 第321行 找到匹配: 表=IOT_Sales, rowid=68356, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:20.211478 删除操作影响行数: 1
2025-05-27 09:14:20.282527 第322行 找到匹配: 表=IOT_Sales, rowid=70979, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:20.283692 删除操作影响行数: 1
2025-05-27 09:14:20.368335 第323行 找到匹配: 表=IOT_Sales, rowid=73139, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:20.369563 删除操作影响行数: 1
2025-05-27 09:14:20.449923 第324行 找到匹配: 表=IOT_Sales, rowid=71264, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:20.451217 删除操作影响行数: 1
2025-05-27 09:14:20.537531 第325行 找到匹配: 表=IOT_Sales, rowid=72933, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:20.538583 删除操作影响行数: 1
2025-05-27 09:14:20.615759 第326行 找到匹配: 表=IOT_Sales, rowid=74402, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:20.616819 删除操作影响行数: 1
2025-05-27 09:14:20.694784 第327行 找到匹配: 表=IOT_Sales, rowid=74857, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:20.695946 删除操作影响行数: 1
2025-05-27 09:14:20.784746 第328行 找到匹配: 表=IOT_Sales, rowid=74565, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:20.786629 删除操作影响行数: 1
2025-05-27 09:14:20.868444 第329行 找到匹配: 表=IOT_Sales, rowid=74318, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:20.869598 删除操作影响行数: 1
2025-05-27 09:14:20.948407 第330行 找到匹配: 表=IOT_Sales, rowid=74077, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:20.949557 删除操作影响行数: 1
2025-05-27 09:14:21.027795 第331行 找到匹配: 表=IOT_Sales, rowid=81073, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:21.028944 删除操作影响行数: 1
2025-05-27 09:14:21.111338 第332行 找到匹配: 表=IOT_Sales, rowid=81071, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:21.112359 删除操作影响行数: 1
2025-05-27 09:14:21.185034 第333行 找到匹配: 表=IOT_Sales, rowid=77449, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:21.185714 删除操作影响行数: 1
2025-05-27 09:14:21.367725 第334行 找到匹配: 表=IOT_Sales, rowid=77394, 原金额=5.0, 退款=8.0, 新金额=-3.0
2025-05-27 09:14:21.368750 删除操作影响行数: 1
2025-05-27 09:14:21.447455 第335行 找到匹配: 表=IOT_Sales, rowid=77671, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:21.448515 删除操作影响行数: 1
2025-05-27 09:14:21.523837 第336行 找到匹配: 表=IOT_Sales, rowid=77422, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:21.524549 删除操作影响行数: 1
2025-05-27 09:14:21.610979 第337行 找到匹配: 表=IOT_Sales, rowid=77416, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:21.611907 删除操作影响行数: 1
2025-05-27 09:14:21.687628 第338行 找到匹配: 表=IOT_Sales, rowid=78084, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:21.688584 删除操作影响行数: 1
2025-05-27 09:14:21.758116 第339行 找到匹配: 表=IOT_Sales, rowid=77204, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:21.759125 删除操作影响行数: 1
2025-05-27 09:14:21.831340 第340行 找到匹配: 表=IOT_Sales, rowid=77852, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:21.832449 删除操作影响行数: 1
2025-05-27 09:14:21.901426 第341行 找到匹配: 表=IOT_Sales, rowid=77431, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:21.902469 删除操作影响行数: 1
2025-05-27 09:14:21.972082 第342行 找到匹配: 表=IOT_Sales, rowid=77596, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:21.972963 删除操作影响行数: 1
2025-05-27 09:14:22.042252 第343行 找到匹配: 表=IOT_Sales, rowid=77185, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:22.043235 删除操作影响行数: 1
2025-05-27 09:14:22.136631 第344行 找到匹配: 表=IOT_Sales, rowid=77138, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:22.137966 删除操作影响行数: 1
2025-05-27 09:14:22.225415 第345行 找到匹配: 表=IOT_Sales, rowid=77135, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:22.226408 删除操作影响行数: 1
2025-05-27 09:14:22.312746 第346行 找到匹配: 表=IOT_Sales, rowid=85627, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:22.313864 删除操作影响行数: 1
2025-05-27 09:14:22.395208 第347行 找到匹配: 表=IOT_Sales, rowid=81356, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:22.396438 删除操作影响行数: 1
2025-05-27 09:14:22.487145 第348行 找到匹配: 表=IOT_Sales, rowid=81602, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:22.487823 删除操作影响行数: 1
2025-05-27 09:14:22.573650 第349行 找到匹配: 表=IOT_Sales, rowid=82341, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:22.574657 删除操作影响行数: 1
2025-05-27 09:14:22.676368 第350行 找到匹配: 表=IOT_Sales, rowid=82968, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:22.677355 删除操作影响行数: 1
2025-05-27 09:14:22.761944 第351行 找到匹配: 表=IOT_Sales, rowid=83094, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:22.762923 删除操作影响行数: 1
2025-05-27 09:14:22.853117 第352行 找到匹配: 表=IOT_Sales, rowid=83514, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:22.854118 删除操作影响行数: 1
2025-05-27 09:14:22.938233 第353行 找到匹配: 表=IOT_Sales, rowid=83545, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:22.939169 删除操作影响行数: 1
2025-05-27 09:14:23.029602 第354行 找到匹配: 表=IOT_Sales, rowid=84363, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:23.030612 删除操作影响行数: 1
2025-05-27 09:14:23.114339 第355行 找到匹配: 表=IOT_Sales, rowid=81155, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:23.115019 删除操作影响行数: 1
2025-05-27 09:14:23.204551 第356行 找到匹配: 表=IOT_Sales, rowid=81154, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:23.205658 删除操作影响行数: 1
2025-05-27 09:14:23.309270 第357行 找到匹配: 表=IOT_Sales, rowid=89017, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:23.310685 删除操作影响行数: 1
2025-05-27 09:14:23.400582 第358行 找到匹配: 表=IOT_Sales, rowid=87120, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:23.401602 删除操作影响行数: 1
2025-05-27 09:14:23.484629 第359行 找到匹配: 表=IOT_Sales, rowid=87117, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:23.485757 删除操作影响行数: 1
2025-05-27 09:14:23.566259 第360行 找到匹配: 表=IOT_Sales, rowid=87085, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:23.567275 删除操作影响行数: 1
2025-05-27 09:14:23.643718 第361行 找到匹配: 表=IOT_Sales, rowid=95956, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:23.644674 删除操作影响行数: 1
2025-05-27 09:14:23.720833 第362行 找到匹配: 表=IOT_Sales, rowid=93119, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:23.721468 删除操作影响行数: 1
2025-05-27 09:14:23.794439 第363行 找到匹配: 表=IOT_Sales, rowid=92707, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:23.795435 删除操作影响行数: 1
2025-05-27 09:14:23.914001 第364行 找到匹配: 表=IOT_Sales, rowid=96169, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:23.915043 删除操作影响行数: 1
2025-05-27 09:14:24.002474 第365行 找到匹配: 表=IOT_Sales, rowid=96658, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:24.003591 删除操作影响行数: 1
2025-05-27 09:14:24.095341 第366行 找到匹配: 表=IOT_Sales, rowid=96235, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:24.096623 删除操作影响行数: 1
2025-05-27 09:14:24.190518 第367行 找到匹配: 表=IOT_Sales, rowid=97036, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:24.191683 删除操作影响行数: 1
2025-05-27 09:14:24.286933 第368行 找到匹配: 表=IOT_Sales, rowid=97198, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:24.287735 删除操作影响行数: 1
2025-05-27 09:14:24.375864 第369行 找到匹配: 表=IOT_Sales, rowid=96095, 原金额=5.0, 退款=8.0, 新金额=-3.0
2025-05-27 09:14:24.376842 删除操作影响行数: 1
2025-05-27 09:14:24.465444 第370行 找到匹配: 表=IOT_Sales, rowid=96097, 原金额=5.0, 退款=8.0, 新金额=-3.0
2025-05-27 09:14:24.466168 删除操作影响行数: 1
2025-05-27 09:14:24.542409 第371行 找到匹配: 表=IOT_Sales, rowid=96493, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:24.544184 删除操作影响行数: 1
2025-05-27 09:14:24.617671 第372行 找到匹配: 表=IOT_Sales, rowid=100125, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:24.618834 更新操作影响行数: 1
2025-05-27 09:14:24.700916 第373行 找到匹配: 表=IOT_Sales, rowid=103352, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:24.701871 删除操作影响行数: 1
2025-05-27 09:14:24.773953 第374行 找到匹配: 表=IOT_Sales, rowid=103361, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:24.774942 删除操作影响行数: 1
2025-05-27 09:14:24.847372 第375行 找到匹配: 表=IOT_Sales, rowid=101124, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:24.848420 删除操作影响行数: 1
2025-05-27 09:14:24.918109 第376行 找到匹配: 表=IOT_Sales, rowid=100702, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:24.919139 删除操作影响行数: 1
2025-05-27 09:14:24.989083 第377行 找到匹配: 表=IOT_Sales, rowid=100015, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:24.990021 删除操作影响行数: 1
2025-05-27 09:14:25.062459 第378行 找到匹配: 表=IOT_Sales, rowid=101815, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:25.063716 删除操作影响行数: 1
2025-05-27 09:14:25.134374 第379行 找到匹配: 表=IOT_Sales, rowid=101920, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:25.135229 删除操作影响行数: 1
2025-05-27 09:14:25.207878 第380行 找到匹配: 表=IOT_Sales, rowid=103337, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:25.208803 删除操作影响行数: 1
2025-05-27 09:14:25.280319 第381行 找到匹配: 表=IOT_Sales, rowid=101098, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:25.281493 删除操作影响行数: 1
2025-05-27 09:14:25.350182 第382行 找到匹配: 表=IOT_Sales, rowid=101001, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:25.351145 更新操作影响行数: 1
2025-05-27 09:14:25.420088 第383行 找到匹配: 表=IOT_Sales, rowid=101001, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:25.420947 更新操作影响行数: 1
2025-05-27 09:14:25.494113 第384行 找到匹配: 表=IOT_Sales, rowid=101061, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:25.495123 删除操作影响行数: 1
2025-05-27 09:14:25.568322 第385行 找到匹配: 表=IOT_Sales, rowid=100757, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:25.569663 删除操作影响行数: 1
2025-05-27 09:14:25.644421 第386行 找到匹配: 表=IOT_Sales, rowid=101821, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:25.645276 删除操作影响行数: 1
2025-05-27 09:14:25.730051 第387行 找到匹配: 表=IOT_Sales, rowid=103979, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:25.731516 更新操作影响行数: 1
2025-05-27 09:14:25.826018 第388行 找到匹配: 表=IOT_Sales, rowid=104020, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:25.827864 删除操作影响行数: 1
2025-05-27 09:14:25.910134 第389行 找到匹配: 表=IOT_Sales, rowid=104094, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:25.911267 删除操作影响行数: 1
2025-05-27 09:14:25.999020 第390行 找到匹配: 表=IOT_Sales, rowid=103821, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:26.000067 删除操作影响行数: 1
2025-05-27 09:14:26.079891 第391行 找到匹配: 表=IOT_Sales, rowid=109345, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:14:26.081070 删除操作影响行数: 1
2025-05-27 09:14:26.163169 第392行 找到匹配: 表=IOT_Sales, rowid=108233, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:26.163956 删除操作影响行数: 1
2025-05-27 09:14:26.363532 第393行 找到匹配: 表=IOT_Sales, rowid=107623, 原金额=15.0, 退款=20.0, 新金额=-5.0
2025-05-27 09:14:26.364492 删除操作影响行数: 1
2025-05-27 09:14:26.443574 第394行 找到匹配: 表=IOT_Sales, rowid=107630, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:26.444519 删除操作影响行数: 1
2025-05-27 09:14:26.520468 第395行 找到匹配: 表=IOT_Sales, rowid=108750, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:26.521453 删除操作影响行数: 1
2025-05-27 09:14:26.593071 第396行 找到匹配: 表=IOT_Sales, rowid=108161, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:26.594075 删除操作影响行数: 1
2025-05-27 09:14:26.667015 第397行 找到匹配: 表=IOT_Sales, rowid=112832, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:26.668014 更新操作影响行数: 1
2025-05-27 09:14:26.745629 第398行 找到匹配: 表=IOT_Sales, rowid=111487, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:26.746333 删除操作影响行数: 1
2025-05-27 09:14:26.825588 第399行 找到匹配: 表=IOT_Sales, rowid=114065, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:26.826585 删除操作影响行数: 1
2025-05-27 09:14:26.908404 第400行 找到匹配: 表=IOT_Sales, rowid=111377, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:26.909394 删除操作影响行数: 1
2025-05-27 09:14:26.990256 第401行 找到匹配: 表=IOT_Sales, rowid=111655, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:26.990993 删除操作影响行数: 1
2025-05-27 09:14:27.069334 第402行 找到匹配: 表=IOT_Sales, rowid=111348, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:27.070326 更新操作影响行数: 1
2025-05-27 09:14:27.145607 第403行 找到匹配: 表=IOT_Sales, rowid=112414, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:27.146607 删除操作影响行数: 1
2025-05-27 09:14:27.227965 第404行 找到匹配: 表=IOT_Sales, rowid=112630, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:27.228924 删除操作影响行数: 1
2025-05-27 09:14:27.300858 第405行 找到匹配: 表=IOT_Sales, rowid=111270, 原金额=5.0, 退款=20.0, 新金额=-15.0
2025-05-27 09:14:27.301890 删除操作影响行数: 1
2025-05-27 09:14:27.374042 第406行 找到匹配: 表=IOT_Sales, rowid=111648, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:27.374656 删除操作影响行数: 1
2025-05-27 09:14:27.459654 第407行 找到匹配: 表=IOT_Sales, rowid=111496, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:27.461198 删除操作影响行数: 1
2025-05-27 09:14:27.559008 第408行 找到匹配: 表=IOT_Sales, rowid=111436, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:27.560166 删除操作影响行数: 1
2025-05-27 09:14:27.662543 第409行 找到匹配: 表=IOT_Sales, rowid=111891, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:27.664654 删除操作影响行数: 1
2025-05-27 09:14:27.776042 第410行 找到匹配: 表=IOT_Sales, rowid=120200, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:27.777184 删除操作影响行数: 1
2025-05-27 09:14:27.884230 第411行 找到匹配: 表=IOT_Sales, rowid=115912, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:27.885726 删除操作影响行数: 1
2025-05-27 09:14:27.975579 第412行 找到匹配: 表=IOT_Sales, rowid=119937, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:27.976740 删除操作影响行数: 1
2025-05-27 09:14:28.064036 第413行 找到匹配: 表=IOT_Sales, rowid=116616, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:28.065193 删除操作影响行数: 1
2025-05-27 09:14:28.154385 第414行 找到匹配: 表=IOT_Sales, rowid=115907, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:28.155336 更新操作影响行数: 1
2025-05-27 09:14:28.250684 第415行 找到匹配: 表=IOT_Sales, rowid=115956, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:28.252170 删除操作影响行数: 1
2025-05-27 09:14:28.361908 第416行 找到匹配: 表=IOT_Sales, rowid=116888, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:28.362908 更新操作影响行数: 1
2025-05-27 09:14:28.450501 第417行 找到匹配: 表=IOT_Sales, rowid=117557, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:28.451732 删除操作影响行数: 1
2025-05-27 09:14:28.551058 第418行 找到匹配: 表=IOT_Sales, rowid=117529, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:28.552203 删除操作影响行数: 1
2025-05-27 09:14:28.655286 第419行 找到匹配: 表=IOT_Sales, rowid=116213, 原金额=15.0, 退款=20.0, 新金额=-5.0
2025-05-27 09:14:28.656708 删除操作影响行数: 1
2025-05-27 09:14:28.737035 第420行 找到匹配: 表=IOT_Sales, rowid=116140, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:28.738015 删除操作影响行数: 1
2025-05-27 09:14:28.832035 第421行 找到匹配: 表=IOT_Sales, rowid=116091, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:28.834287 删除操作影响行数: 1
2025-05-27 09:14:28.929205 第422行 找到匹配: 表=IOT_Sales, rowid=116056, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:28.931684 删除操作影响行数: 1
2025-05-27 09:14:29.016765 第423行 找到匹配: 表=IOT_Sales, rowid=116047, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:29.017764 删除操作影响行数: 1
2025-05-27 09:14:29.096618 第424行 找到匹配: 表=IOT_Sales, rowid=115905, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:29.097698 删除操作影响行数: 1
2025-05-27 09:14:29.178649 第425行 找到匹配: 表=IOT_Sales, rowid=120737, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:29.179435 删除操作影响行数: 1
2025-05-27 09:14:29.264071 第426行 找到匹配: 表=IOT_Sales, rowid=120950, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:29.265190 更新操作影响行数: 1
2025-05-27 09:14:29.340191 第427行 找到匹配: 表=IOT_Sales, rowid=123133, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:29.341257 删除操作影响行数: 1
2025-05-27 09:14:29.418718 第428行 找到匹配: 表=IOT_Sales, rowid=122160, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:29.419683 删除操作影响行数: 1
2025-05-27 09:14:29.499844 第429行 找到匹配: 表=IOT_Sales, rowid=121716, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:29.500922 删除操作影响行数: 1
2025-05-27 09:14:29.576676 第430行 找到匹配: 表=IOT_Sales, rowid=121211, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:29.577768 删除操作影响行数: 1
2025-05-27 09:14:29.660099 第431行 找到匹配: 表=IOT_Sales, rowid=125859, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:29.661023 删除操作影响行数: 1
2025-05-27 09:14:29.794904 第432行 找到匹配: 表=IOT_Sales, rowid=124213, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:29.796517 删除操作影响行数: 1
2025-05-27 09:14:29.890807 第433行 找到匹配: 表=IOT_Sales, rowid=123969, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:29.892033 删除操作影响行数: 1
2025-05-27 09:14:29.987771 第434行 找到匹配: 表=IOT_Sales, rowid=123789, 原金额=5.0, 退款=8.0, 新金额=-3.0
2025-05-27 09:14:29.988905 删除操作影响行数: 1
2025-05-27 09:14:30.099627 第435行 找到匹配: 表=IOT_Sales, rowid=124084, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:30.101482 删除操作影响行数: 1
2025-05-27 09:14:30.222355 第436行 找到匹配: 表=IOT_Sales, rowid=131821, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:30.224597 删除操作影响行数: 1
2025-05-27 09:14:30.304061 第437行 找到匹配: 表=IOT_Sales, rowid=131670, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:30.305266 删除操作影响行数: 1
2025-05-27 09:14:30.385530 第438行 找到匹配: 表=IOT_Sales, rowid=131680, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:30.386599 更新操作影响行数: 1
2025-05-27 09:14:30.493802 第439行 找到匹配: 表=IOT_Sales, rowid=134553, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:30.495478 删除操作影响行数: 1
2025-05-27 09:14:30.594098 第440行 找到匹配: 表=IOT_Sales, rowid=137617, 原金额=15.0, 退款=3.0, 新金额=12.0
2025-05-27 09:14:30.595221 更新操作影响行数: 1
2025-05-27 09:14:30.687206 第441行 找到匹配: 表=IOT_Sales, rowid=134879, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:30.688062 删除操作影响行数: 1
2025-05-27 09:14:30.769722 第442行 找到匹配: 表=IOT_Sales, rowid=135175, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:30.771073 删除操作影响行数: 1
2025-05-27 09:14:30.863836 第443行 找到匹配: 表=IOT_Sales, rowid=143956, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:30.865010 删除操作影响行数: 1
2025-05-27 09:14:30.946018 第444行 找到匹配: 表=IOT_Sales, rowid=143928, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:30.946976 删除操作影响行数: 1
2025-05-27 09:14:31.024148 第445行 找到匹配: 表=IOT_Sales, rowid=139430, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:31.025321 删除操作影响行数: 1
2025-05-27 09:14:31.110777 第446行 找到匹配: 表=IOT_Sales, rowid=139347, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:31.111569 删除操作影响行数: 1
2025-05-27 09:14:31.192258 第447行 找到匹配: 表=IOT_Sales, rowid=139519, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:31.193270 删除操作影响行数: 1
2025-05-27 09:14:31.278993 第448行 找到匹配: 表=IOT_Sales, rowid=140476, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:31.280069 删除操作影响行数: 1
2025-05-27 09:14:31.368663 第449行 找到匹配: 表=IOT_Sales, rowid=139632, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:31.370231 删除操作影响行数: 1
2025-05-27 09:14:31.458939 第450行 找到匹配: 表=IOT_Sales, rowid=139364, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:31.459955 删除操作影响行数: 1
2025-05-27 09:14:31.540399 第451行 找到匹配: 表=IOT_Sales, rowid=140406, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:31.541754 删除操作影响行数: 1
2025-05-27 09:14:31.641160 第452行 找到匹配: 表=IOT_Sales, rowid=142688, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:31.642291 删除操作影响行数: 1
2025-05-27 09:14:31.728149 第453行 找到匹配: 表=IOT_Sales, rowid=139407, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:31.729137 更新操作影响行数: 1
2025-05-27 09:14:31.995374 第454行 找到匹配: 表=IOT_Sales, rowid=142634, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:31.997040 删除操作影响行数: 1
2025-05-27 09:14:32.099304 第455行 找到匹配: 表=IOT_Sales, rowid=140711, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:32.100552 删除操作影响行数: 1
2025-05-27 09:14:32.187569 第456行 找到匹配: 表=IOT_Sales, rowid=139969, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:32.188531 删除操作影响行数: 1
2025-05-27 09:14:32.270076 第457行 找到匹配: 表=IOT_Sales, rowid=139511, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:32.271026 删除操作影响行数: 1
2025-05-27 09:14:32.352376 第458行 找到匹配: 表=IOT_Sales, rowid=140605, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:32.353150 删除操作影响行数: 1
2025-05-27 09:14:32.438575 第459行 找到匹配: 表=IOT_Sales, rowid=141601, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:32.439605 删除操作影响行数: 1
2025-05-27 09:14:32.518742 第460行 找到匹配: 表=IOT_Sales, rowid=139816, 原金额=3.0, 退款=5.0, 新金额=-2.0
2025-05-27 09:14:32.519807 删除操作影响行数: 1
2025-05-27 09:14:32.601624 第461行 找到匹配: 表=IOT_Sales, rowid=139888, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:32.602818 更新操作影响行数: 1
2025-05-27 09:14:32.681900 第462行 找到匹配: 表=IOT_Sales, rowid=139636, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:32.682876 删除操作影响行数: 1
2025-05-27 09:14:32.765519 第463行 找到匹配: 表=IOT_Sales, rowid=139250, 原金额=5.0, 退款=20.0, 新金额=-15.0
2025-05-27 09:14:32.766233 删除操作影响行数: 1
2025-05-27 09:14:32.841922 第464行 找到匹配: 表=IOT_Sales, rowid=141413, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:32.843023 删除操作影响行数: 1
2025-05-27 09:14:32.918892 第465行 找到匹配: 表=IOT_Sales, rowid=140091, 原金额=5.0, 退款=8.0, 新金额=-3.0
2025-05-27 09:14:32.919834 删除操作影响行数: 1
2025-05-27 09:14:33.003282 第466行 找到匹配: 表=IOT_Sales, rowid=139334, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:33.004357 更新操作影响行数: 1
2025-05-27 09:14:33.084095 第467行 找到匹配: 表=IOT_Sales, rowid=140081, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:33.085544 删除操作影响行数: 1
2025-05-27 09:14:33.168473 第468行 找到匹配: 表=IOT_Sales, rowid=139454, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:33.169282 删除操作影响行数: 1
2025-05-27 09:14:33.245319 第469行 找到匹配: 表=IOT_Sales, rowid=139773, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:33.246313 删除操作影响行数: 1
2025-05-27 09:14:33.324830 第470行 找到匹配: 表=IOT_Sales, rowid=139308, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:33.325845 删除操作影响行数: 1
2025-05-27 09:14:33.405248 第471行 找到匹配: 表=IOT_Sales, rowid=139279, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:33.406546 删除操作影响行数: 1
2025-05-27 09:14:33.478947 第472行 找到匹配: 表=IOT_Sales, rowid=146248, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:33.479947 删除操作影响行数: 1
2025-05-27 09:14:33.547839 第473行 找到匹配: 表=IOT_Sales, rowid=144287, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:33.548843 删除操作影响行数: 1
2025-05-27 09:14:33.617394 第474行 找到匹配: 表=IOT_Sales, rowid=146635, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:33.618260 删除操作影响行数: 1
2025-05-27 09:14:33.703646 第475行 找到匹配: 表=IOT_Sales, rowid=144756, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:33.704669 更新操作影响行数: 1
2025-05-27 09:14:33.789403 第476行 找到匹配: 表=IOT_Sales, rowid=145408, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:33.790114 删除操作影响行数: 1
2025-05-27 09:14:33.868279 第477行 找到匹配: 表=IOT_Sales, rowid=147295, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:33.869072 更新操作影响行数: 1
2025-05-27 09:14:33.945383 第478行 找到匹配: 表=IOT_Sales, rowid=154234, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:33.946830 删除操作影响行数: 1
2025-05-27 09:14:34.025433 第479行 找到匹配: 表=IOT_Sales, rowid=155077, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:34.026524 删除操作影响行数: 1
2025-05-27 09:14:34.130278 第480行 找到匹配: 表=IOT_Sales, rowid=159382, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:34.131611 删除操作影响行数: 1
2025-05-27 09:14:34.213159 第481行 找到匹配: 表=IOT_Sales, rowid=158670, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:34.214165 删除操作影响行数: 1
2025-05-27 09:14:34.335453 第482行 找到匹配: 表=IOT_Sales, rowid=168361, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:34.336554 删除操作影响行数: 1
2025-05-27 09:14:34.433535 第483行 找到匹配: 表=IOT_Sales, rowid=168359, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:34.434608 删除操作影响行数: 1
2025-05-27 09:14:34.524551 第484行 找到匹配: 表=IOT_Sales, rowid=164563, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:34.525693 删除操作影响行数: 1
2025-05-27 09:14:34.611206 第485行 找到匹配: 表=IOT_Sales, rowid=170964, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:34.612240 删除操作影响行数: 1
2025-05-27 09:14:34.701193 第486行 找到匹配: 表=IOT_Sales, rowid=171020, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:34.702324 删除操作影响行数: 1
2025-05-27 09:14:34.773789 第487行 找到匹配: 表=IOT_Sales, rowid=171440, 原金额=10.0, 退款=2.0, 新金额=8.0
2025-05-27 09:14:34.774572 更新操作影响行数: 1
2025-05-27 09:14:34.852243 第488行 找到匹配: 表=IOT_Sales, rowid=172731, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:34.853755 删除操作影响行数: 1
2025-05-27 09:14:34.923647 第489行 找到匹配: 表=IOT_Sales, rowid=172371, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:34.924641 删除操作影响行数: 1
2025-05-27 09:14:34.991362 第490行 找到匹配: 表=IOT_Sales, rowid=171149, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:34.992463 删除操作影响行数: 1
2025-05-27 09:14:35.061086 第491行 找到匹配: 表=IOT_Sales, rowid=172190, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:35.062033 删除操作影响行数: 1
2025-05-27 09:14:35.130904 第492行 找到匹配: 表=IOT_Sales, rowid=171707, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:35.132099 删除操作影响行数: 1
2025-05-27 09:14:35.202258 第493行 找到匹配: 表=IOT_Sales, rowid=171334, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:35.202926 更新操作影响行数: 1
2025-05-27 09:14:35.273911 第494行 找到匹配: 表=IOT_Sales, rowid=171334, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:35.274856 删除操作影响行数: 1
2025-05-27 09:14:35.340999 第495行 找到匹配: 表=IOT_Sales, rowid=171081, 原金额=8.0, 退款=8.0, 新金额=0.0
2025-05-27 09:14:35.342225 删除操作影响行数: 1
2025-05-27 09:14:35.487880 第497行 找到匹配: 表=IOT_Sales, rowid=175536, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:35.489431 删除操作影响行数: 1
2025-05-27 09:14:35.759846 第500行 找到匹配: 表=ZERO_Sales, rowid=991, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:35.760844 删除操作影响行数: 1
2025-05-27 09:14:35.863935 第501行 找到匹配: 表=ZERO_Sales, rowid=596, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:35.865387 删除操作影响行数: 1
2025-05-27 09:14:35.978162 第502行 找到匹配: 表=ZERO_Sales, rowid=128, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:35.979316 删除操作影响行数: 1
2025-05-27 09:14:36.187071 第504行 找到匹配: 表=ZERO_Sales, rowid=690, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:36.188130 删除操作影响行数: 1
2025-05-27 09:14:36.290580 第505行 找到匹配: 表=ZERO_Sales, rowid=521, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:36.291675 删除操作影响行数: 1
2025-05-27 09:14:36.382771 第506行 找到匹配: 表=ZERO_Sales, rowid=992, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:36.383913 删除操作影响行数: 1
2025-05-27 09:14:36.466744 第507行 找到匹配: 表=IOT_Sales, rowid=2924, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:36.467710 删除操作影响行数: 1
2025-05-27 09:14:36.557740 第508行 找到匹配: 表=ZERO_Sales, rowid=781, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:36.558429 删除操作影响行数: 1
2025-05-27 09:14:36.757211 第510行 找到匹配: 表=ZERO_Sales, rowid=60, 原金额=6.0, 退款=15.0, 新金额=-9.0
2025-05-27 09:14:36.757873 删除操作影响行数: 1
2025-05-27 09:14:37.097001 第512行 找到匹配: 表=ZERO_Sales, rowid=3112, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:37.098005 删除操作影响行数: 1
2025-05-27 09:14:37.187101 第513行 找到匹配: 表=ZERO_Sales, rowid=7449, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:37.188004 删除操作影响行数: 1
2025-05-27 09:14:37.269736 第514行 找到匹配: 表=ZERO_Sales, rowid=8182, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:37.270671 删除操作影响行数: 1
2025-05-27 09:14:37.439417 第516行 找到匹配: 表=IOT_Sales, rowid=18136, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:37.440551 删除操作影响行数: 1
2025-05-27 09:14:37.554768 第517行 找到匹配: 表=ZERO_Sales, rowid=10536, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:37.557806 删除操作影响行数: 1
2025-05-27 09:14:37.641786 第518行 找到匹配: 表=IOT_Sales, rowid=18031, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:37.642763 删除操作影响行数: 1
2025-05-27 09:14:37.766463 第519行 找到匹配: 表=ZERO_Sales, rowid=10016, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:37.767606 删除操作影响行数: 1
2025-05-27 09:14:37.863929 第520行 找到匹配: 表=ZERO_Sales, rowid=12748, 原金额=6.0, 退款=6.0, 新金额=0.0
2025-05-27 09:14:37.864987 删除操作影响行数: 1
2025-05-27 09:14:37.962279 第521行 找到匹配: 表=ZERO_Sales, rowid=11965, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:37.963268 删除操作影响行数: 1
2025-05-27 09:14:38.064383 第522行 找到匹配: 表=ZERO_Sales, rowid=12002, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:38.065438 删除操作影响行数: 1
2025-05-27 09:14:38.166086 第523行 找到匹配: 表=ZERO_Sales, rowid=12006, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:38.167180 删除操作影响行数: 1
2025-05-27 09:14:38.240242 第524行 找到匹配: 表=IOT_Sales, rowid=29181, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:38.241199 删除操作影响行数: 1
2025-05-27 09:14:38.339930 第525行 找到匹配: 表=ZERO_Sales, rowid=17629, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:38.341190 删除操作影响行数: 1
2025-05-27 09:14:38.433164 第526行 找到匹配: 表=ZERO_Sales, rowid=17310, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:38.434167 删除操作影响行数: 1
2025-05-27 09:14:38.522772 第527行 找到匹配: 表=ZERO_Sales, rowid=17127, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:38.523716 删除操作影响行数: 1
2025-05-27 09:14:38.598641 第528行 找到匹配: 表=IOT_Sales, rowid=38769, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:38.599319 删除操作影响行数: 1
2025-05-27 09:14:38.691193 第529行 找到匹配: 表=ZERO_Sales, rowid=19300, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:38.691811 删除操作影响行数: 1
2025-05-27 09:14:38.772228 第530行 找到匹配: 表=IOT_Sales, rowid=39782, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:38.773232 删除操作影响行数: 1
2025-05-27 09:14:38.869298 第531行 找到匹配: 表=ZERO_Sales, rowid=19251, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:38.870297 删除操作影响行数: 1
2025-05-27 09:14:38.959207 第532行 找到匹配: 表=ZERO_Sales, rowid=20422, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:38.960204 删除操作影响行数: 1
2025-05-27 09:14:39.046205 第533行 找到匹配: 表=ZERO_Sales, rowid=20423, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:39.047276 删除操作影响行数: 1
2025-05-27 09:14:39.133504 第534行 找到匹配: 表=ZERO_Sales, rowid=20490, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:39.134464 删除操作影响行数: 1
2025-05-27 09:14:39.225680 第535行 找到匹配: 表=ZERO_Sales, rowid=20424, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:39.226416 删除操作影响行数: 1
2025-05-27 09:14:39.327970 第536行 找到匹配: 表=ZERO_Sales, rowid=20706, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:39.329004 删除操作影响行数: 1
2025-05-27 09:14:39.433287 第537行 找到匹配: 表=ZERO_Sales, rowid=20611, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:39.434446 删除操作影响行数: 1
2025-05-27 09:14:39.549436 第538行 找到匹配: 表=ZERO_Sales, rowid=21855, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:39.550272 删除操作影响行数: 1
2025-05-27 09:14:39.652656 第539行 找到匹配: 表=ZERO_Sales, rowid=21274, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:39.653622 删除操作影响行数: 1
2025-05-27 09:14:39.748453 第540行 找到匹配: 表=ZERO_Sales, rowid=22045, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:39.749376 删除操作影响行数: 1
2025-05-27 09:14:39.845204 第541行 找到匹配: 表=ZERO_Sales, rowid=21921, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:39.846217 删除操作影响行数: 1
2025-05-27 09:14:39.937886 第542行 找到匹配: 表=ZERO_Sales, rowid=21600, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:39.938875 删除操作影响行数: 1
2025-05-27 09:14:40.025480 第543行 找到匹配: 表=ZERO_Sales, rowid=21351, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:40.026462 删除操作影响行数: 1
2025-05-27 09:14:40.120083 第544行 找到匹配: 表=ZERO_Sales, rowid=21354, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:40.120998 删除操作影响行数: 1
2025-05-27 09:14:40.216507 第545行 找到匹配: 表=ZERO_Sales, rowid=21244, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:40.217840 删除操作影响行数: 1
2025-05-27 09:14:40.308165 第546行 找到匹配: 表=IOT_Sales, rowid=49081, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:40.309176 删除操作影响行数: 1
2025-05-27 09:14:40.391678 第547行 找到匹配: 表=ZERO_Sales, rowid=22420, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:40.392677 更新操作影响行数: 1
2025-05-27 09:14:40.467143 第548行 找到匹配: 表=IOT_Sales, rowid=49972, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:40.467804 删除操作影响行数: 1
2025-05-27 09:14:40.547929 第549行 找到匹配: 表=ZERO_Sales, rowid=22761, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:40.548891 删除操作影响行数: 1
2025-05-27 09:14:40.628360 第550行 找到匹配: 表=ZERO_Sales, rowid=22762, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:40.628991 删除操作影响行数: 1
2025-05-27 09:14:40.708112 第551行 找到匹配: 表=ZERO_Sales, rowid=22475, 原金额=6.0, 退款=15.0, 新金额=-9.0
2025-05-27 09:14:40.709119 删除操作影响行数: 1
2025-05-27 09:14:40.777979 第552行 找到匹配: 表=IOT_Sales, rowid=50477, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:40.778882 更新操作影响行数: 1
2025-05-27 09:14:40.860847 第553行 找到匹配: 表=ZERO_Sales, rowid=22815, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:40.861798 删除操作影响行数: 1
2025-05-27 09:14:40.940491 第554行 找到匹配: 表=ZERO_Sales, rowid=22506, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:40.941541 更新操作影响行数: 1
2025-05-27 09:14:41.005757 第555行 找到匹配: 表=IOT_Sales, rowid=50478, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:41.006562 删除操作影响行数: 1
2025-05-27 09:14:41.074131 第556行 找到匹配: 表=IOT_Sales, rowid=49768, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:41.075504 更新操作影响行数: 1
2025-05-27 09:14:41.166185 第557行 找到匹配: 表=ZERO_Sales, rowid=22763, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:41.167262 更新操作影响行数: 1
2025-05-27 09:14:41.268991 第558行 找到匹配: 表=ZERO_Sales, rowid=22314, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:41.270232 删除操作影响行数: 1
2025-05-27 09:14:41.370080 第559行 找到匹配: 表=ZERO_Sales, rowid=22376, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:41.371078 更新操作影响行数: 1
2025-05-27 09:14:41.459841 第560行 找到匹配: 表=ZERO_Sales, rowid=22548, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:41.461058 更新操作影响行数: 1
2025-05-27 09:14:41.563205 第561行 找到匹配: 表=ZERO_Sales, rowid=22376, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:41.564282 更新操作影响行数: 1
2025-05-27 09:14:41.681139 第562行 找到匹配: 表=ZERO_Sales, rowid=22315, 原金额=20.0, 退款=10.0, 新金额=10.0
2025-05-27 09:14:41.682184 更新操作影响行数: 1
2025-05-27 09:14:41.783538 第563行 找到匹配: 表=ZERO_Sales, rowid=22393, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:41.784811 删除操作影响行数: 1
2025-05-27 09:14:41.877848 第564行 找到匹配: 表=ZERO_Sales, rowid=22763, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:41.879171 更新操作影响行数: 1
2025-05-27 09:14:42.007183 第565行 找到匹配: 表=ZERO_Sales, rowid=22316, 原金额=20.0, 退款=20.0, 新金额=0.0
2025-05-27 09:14:42.008000 删除操作影响行数: 1
2025-05-27 09:14:42.098273 第566行 找到匹配: 表=ZERO_Sales, rowid=22546, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:42.099673 删除操作影响行数: 1
2025-05-27 09:14:42.176050 第567行 找到匹配: 表=IOT_Sales, rowid=48613, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:42.176877 删除操作影响行数: 1
2025-05-27 09:14:42.247529 第568行 找到匹配: 表=IOT_Sales, rowid=49117, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:42.248460 更新操作影响行数: 1
2025-05-27 09:14:42.341926 第569行 找到匹配: 表=ZERO_Sales, rowid=23487, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:42.342921 删除操作影响行数: 1
2025-05-27 09:14:42.579984 第570行 找到匹配: 表=IOT_Sales, rowid=56898, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:42.580974 删除操作影响行数: 1
2025-05-27 09:14:42.653227 第571行 找到匹配: 表=IOT_Sales, rowid=55979, 原金额=30.0, 退款=15.0, 新金额=15.0
2025-05-27 09:14:42.654294 更新操作影响行数: 1
2025-05-27 09:14:42.756599 第572行 找到匹配: 表=ZERO_Sales, rowid=25038, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:42.757619 删除操作影响行数: 1
2025-05-27 09:14:42.854780 第573行 找到匹配: 表=ZERO_Sales, rowid=24081, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:42.855718 删除操作影响行数: 1
2025-05-27 09:14:42.947580 第574行 找到匹配: 表=ZERO_Sales, rowid=24068, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:42.948496 删除操作影响行数: 1
2025-05-27 09:14:43.025647 第575行 找到匹配: 表=IOT_Sales, rowid=62615, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:43.026616 删除操作影响行数: 1
2025-05-27 09:14:43.141450 第576行 找到匹配: 表=ZERO_Sales, rowid=29419, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:43.142612 删除操作影响行数: 1
2025-05-27 09:14:43.220703 第577行 找到匹配: 表=IOT_Sales, rowid=95776, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:43.221901 更新操作影响行数: 1
2025-05-27 09:14:43.325938 第578行 找到匹配: 表=ZERO_Sales, rowid=33824, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:43.327028 删除操作影响行数: 1
2025-05-27 09:14:43.442039 第579行 找到匹配: 表=ZERO_Sales, rowid=32895, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:43.443480 删除操作影响行数: 1
2025-05-27 09:14:43.535910 第580行 找到匹配: 表=ZERO_Sales, rowid=33999, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:43.536943 删除操作影响行数: 1
2025-05-27 09:14:43.632740 第581行 找到匹配: 表=ZERO_Sales, rowid=37857, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:43.633713 删除操作影响行数: 1
2025-05-27 09:14:43.731393 第582行 找到匹配: 表=ZERO_Sales, rowid=30120, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:43.732371 删除操作影响行数: 1
2025-05-27 09:14:43.824615 第583行 找到匹配: 表=ZERO_Sales, rowid=26998, 原金额=20.0, 退款=20.0, 新金额=0.0
2025-05-27 09:14:43.825631 删除操作影响行数: 1
2025-05-27 09:14:43.921499 第584行 找到匹配: 表=ZERO_Sales, rowid=38988, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:43.922446 删除操作影响行数: 1
2025-05-27 09:14:44.010976 第585行 找到匹配: 表=ZERO_Sales, rowid=38982, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:44.011711 更新操作影响行数: 1
2025-05-27 09:14:44.080872 第586行 找到匹配: 表=IOT_Sales, rowid=98007, 原金额=15.0, 退款=10.0, 新金额=5.0
2025-05-27 09:14:44.081840 更新操作影响行数: 1
2025-05-27 09:14:44.163543 第587行 找到匹配: 表=ZERO_Sales, rowid=27482, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:44.164515 删除操作影响行数: 1
2025-05-27 09:14:44.242987 第588行 找到匹配: 表=IOT_Sales, rowid=83797, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:44.243973 删除操作影响行数: 1
2025-05-27 09:14:44.318633 第589行 找到匹配: 表=IOT_Sales, rowid=83351, 原金额=5.0, 退款=20.0, 新金额=-15.0
2025-05-27 09:14:44.319641 删除操作影响行数: 1
2025-05-27 09:14:44.394828 第590行 找到匹配: 表=IOT_Sales, rowid=98638, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:44.395909 删除操作影响行数: 1
2025-05-27 09:14:44.515938 第591行 找到匹配: 表=ZERO_Sales, rowid=35184, 原金额=5.0, 退款=30.0, 新金额=-25.0
2025-05-27 09:14:44.518254 删除操作影响行数: 1
2025-05-27 09:14:44.627045 第592行 找到匹配: 表=ZERO_Sales, rowid=35745, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:44.627837 删除操作影响行数: 1
2025-05-27 09:14:44.732128 第593行 找到匹配: 表=ZERO_Sales, rowid=29005, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:44.732897 删除操作影响行数: 1
2025-05-27 09:14:44.824501 第594行 找到匹配: 表=IOT_Sales, rowid=81670, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:44.825641 删除操作影响行数: 1
2025-05-27 09:14:44.950739 第595行 找到匹配: 表=ZERO_Sales, rowid=39108, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:44.952709 删除操作影响行数: 1
2025-05-27 09:14:45.055290 第596行 找到匹配: 表=ZERO_Sales, rowid=38935, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:45.057206 删除操作影响行数: 1
2025-05-27 09:14:45.148720 第597行 找到匹配: 表=ZERO_Sales, rowid=40197, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:45.149715 删除操作影响行数: 1
2025-05-27 09:14:45.248077 第598行 找到匹配: 表=ZERO_Sales, rowid=43522, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:45.249228 删除操作影响行数: 1
2025-05-27 09:14:45.326644 第599行 找到匹配: 表=IOT_Sales, rowid=139785, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:45.327384 更新操作影响行数: 1
2025-05-27 09:14:45.408332 第600行 找到匹配: 表=IOT_Sales, rowid=137840, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:45.409351 删除操作影响行数: 1
2025-05-27 09:14:45.497730 第601行 找到匹配: 表=ZERO_Sales, rowid=44434, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:45.498682 删除操作影响行数: 1
2025-05-27 09:14:45.585713 第602行 找到匹配: 表=ZERO_Sales, rowid=44624, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:45.586683 删除操作影响行数: 1
2025-05-27 09:14:45.658369 第603行 找到匹配: 表=IOT_Sales, rowid=137030, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:45.659365 删除操作影响行数: 1
2025-05-27 09:14:45.745998 第604行 找到匹配: 表=ZERO_Sales, rowid=42838, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:45.746938 删除操作影响行数: 1
2025-05-27 09:14:45.825299 第605行 找到匹配: 表=ZERO_Sales, rowid=41054, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:45.826247 删除操作影响行数: 1
2025-05-27 09:14:45.911221 第606行 找到匹配: 表=ZERO_Sales, rowid=40894, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:45.912194 删除操作影响行数: 1
2025-05-27 09:14:45.981236 第607行 找到匹配: 表=IOT_Sales, rowid=126323, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:45.983206 删除操作影响行数: 1
2025-05-27 09:14:46.071492 第608行 找到匹配: 表=ZERO_Sales, rowid=41214, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:46.072115 删除操作影响行数: 1
2025-05-27 09:14:46.168143 第609行 找到匹配: 表=ZERO_Sales, rowid=36050, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:46.169323 删除操作影响行数: 1
2025-05-27 09:14:46.255976 第610行 找到匹配: 表=IOT_Sales, rowid=86248, 原金额=15.0, 退款=10.0, 新金额=5.0
2025-05-27 09:14:46.256988 更新操作影响行数: 1
2025-05-27 09:14:46.354274 第611行 找到匹配: 表=ZERO_Sales, rowid=41239, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:46.355201 删除操作影响行数: 1
2025-05-27 09:14:46.436821 第612行 找到匹配: 表=ZERO_Sales, rowid=41275, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:46.437834 删除操作影响行数: 1
2025-05-27 09:14:46.522245 第613行 找到匹配: 表=ZERO_Sales, rowid=41948, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:46.523177 删除操作影响行数: 1
2025-05-27 09:14:46.611664 第614行 找到匹配: 表=ZERO_Sales, rowid=22283, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:46.612837 删除操作影响行数: 1
2025-05-27 09:14:46.713346 第615行 找到匹配: 表=ZERO_Sales, rowid=43651, 原金额=10.0, 退款=20.0, 新金额=-10.0
2025-05-27 09:14:46.714281 删除操作影响行数: 1
2025-05-27 09:14:46.810295 第616行 找到匹配: 表=ZERO_Sales, rowid=34777, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:46.811503 删除操作影响行数: 1
2025-05-27 09:14:46.916058 第617行 找到匹配: 表=ZERO_Sales, rowid=30169, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:46.917228 删除操作影响行数: 1
2025-05-27 09:14:47.024613 第618行 找到匹配: 表=ZERO_Sales, rowid=25168, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:47.025700 删除操作影响行数: 1
2025-05-27 09:14:47.145754 第619行 找到匹配: 表=ZERO_Sales, rowid=28193, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:47.147034 更新操作影响行数: 1
2025-05-27 09:14:47.235824 第620行 找到匹配: 表=ZERO_Sales, rowid=27580, 原金额=30.0, 退款=30.0, 新金额=0.0
2025-05-27 09:14:47.237356 删除操作影响行数: 1
2025-05-27 09:14:47.342023 第621行 找到匹配: 表=ZERO_Sales, rowid=27582, 原金额=30.0, 退款=30.0, 新金额=0.0
2025-05-27 09:14:47.344401 删除操作影响行数: 1
2025-05-27 09:14:47.585784 第622行 找到匹配: 表=IOT_Sales, rowid=160010, 原金额=5.0, 退款=20.0, 新金额=-15.0
2025-05-27 09:14:47.586769 删除操作影响行数: 1
2025-05-27 09:14:47.662766 第623行 找到匹配: 表=IOT_Sales, rowid=160018, 原金额=5.0, 退款=20.0, 新金额=-15.0
2025-05-27 09:14:47.663575 删除操作影响行数: 1
2025-05-27 09:14:47.775824 第624行 找到匹配: 表=ZERO_Sales, rowid=47502, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:47.777282 删除操作影响行数: 1
2025-05-27 09:14:47.877631 第625行 找到匹配: 表=ZERO_Sales, rowid=30106, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:47.878738 删除操作影响行数: 1
2025-05-27 09:14:47.947120 第626行 找到匹配: 表=IOT_Sales, rowid=73593, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:47.948115 删除操作影响行数: 1
2025-05-27 09:14:48.033914 第627行 找到匹配: 表=ZERO_Sales, rowid=26815, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:48.034918 删除操作影响行数: 1
2025-05-27 09:14:48.103937 第628行 找到匹配: 表=IOT_Sales, rowid=70001, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:48.105064 删除操作影响行数: 1
2025-05-27 09:14:48.190556 第629行 找到匹配: 表=ZERO_Sales, rowid=42800, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:48.191580 删除操作影响行数: 1
2025-05-27 09:14:48.272485 第630行 找到匹配: 表=ZERO_Sales, rowid=26199, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:48.273483 删除操作影响行数: 1
2025-05-27 09:14:48.356358 第631行 找到匹配: 表=ZERO_Sales, rowid=26164, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:48.357355 删除操作影响行数: 1
2025-05-27 09:14:48.442680 第632行 找到匹配: 表=ZERO_Sales, rowid=26336, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:48.443321 更新操作影响行数: 1
2025-05-27 09:14:48.531663 第633行 找到匹配: 表=IOT_Sales, rowid=50015, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:48.532959 删除操作影响行数: 1
2025-05-27 09:14:48.637466 第634行 找到匹配: 表=ZERO_Sales, rowid=24319, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:48.638185 删除操作影响行数: 1
2025-05-27 09:14:48.739116 第635行 找到匹配: 表=ZERO_Sales, rowid=22811, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:48.740122 删除操作影响行数: 1
2025-05-27 09:14:48.822775 第636行 找到匹配: 表=IOT_Sales, rowid=66852, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:48.824126 删除操作影响行数: 1
2025-05-27 09:14:48.920751 第637行 找到匹配: 表=ZERO_Sales, rowid=23065, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:48.921824 删除操作影响行数: 1
2025-05-27 09:14:49.016223 第638行 找到匹配: 表=ZERO_Sales, rowid=22524, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:49.017193 删除操作影响行数: 1
2025-05-27 09:14:49.109607 第639行 找到匹配: 表=IOT_Sales, rowid=185966, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:49.110882 删除操作影响行数: 1
2025-05-27 09:14:49.187165 第640行 找到匹配: 表=IOT_Sales, rowid=185176, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:49.188125 删除操作影响行数: 1
2025-05-27 09:14:49.346828 第642行 找到匹配: 表=IOT_Sales, rowid=193919, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:14:49.347832 删除操作影响行数: 1
2025-05-27 09:14:49.414959 第643行 找到匹配: 表=IOT_Sales, rowid=193038, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:14:49.416142 删除操作影响行数: 1
2025-05-27 09:14:49.482584 第644行 找到匹配: 表=IOT_Sales, rowid=193020, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:14:49.483221 删除操作影响行数: 1
2025-05-27 09:14:49.550958 第645行 找到匹配: 表=IOT_Sales, rowid=197113, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:49.551992 删除操作影响行数: 1
2025-05-27 09:14:49.630195 第646行 找到匹配: 表=IOT_Sales, rowid=197505, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:49.631235 删除操作影响行数: 1
2025-05-27 09:14:49.715565 第647行 找到匹配: 表=IOT_Sales, rowid=197502, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:49.717338 删除操作影响行数: 1
2025-05-27 09:14:49.785542 第648行 找到匹配: 表=IOT_Sales, rowid=197053, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:49.786624 删除操作影响行数: 1
2025-05-27 09:14:49.858135 第649行 找到匹配: 表=IOT_Sales, rowid=3969, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:49.858878 删除操作影响行数: 1
2025-05-27 09:14:49.937366 第650行 找到匹配: 表=IOT_Sales, rowid=197393, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:49.938299 删除操作影响行数: 1
2025-05-27 09:14:50.007818 第651行 找到匹配: 表=IOT_Sales, rowid=197398, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:50.008819 删除操作影响行数: 1
2025-05-27 09:14:50.076311 第652行 找到匹配: 表=IOT_Sales, rowid=197960, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:50.077629 更新操作影响行数: 1
2025-05-27 09:14:50.152739 第653行 找到匹配: 表=IOT_Sales, rowid=196896, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:50.153445 删除操作影响行数: 1
2025-05-27 09:14:50.228622 第654行 找到匹配: 表=IOT_Sales, rowid=196895, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:50.230122 删除操作影响行数: 1
2025-05-27 09:14:50.630154 第658行 找到匹配: 表=IOT_Sales, rowid=197224, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:50.631074 删除操作影响行数: 1
2025-05-27 09:14:50.896113 第661行 找到匹配: 表=IOT_Sales, rowid=198276, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:50.896860 删除操作影响行数: 1
2025-05-27 09:14:50.965193 第662行 找到匹配: 表=IOT_Sales, rowid=3971, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:50.966274 删除操作影响行数: 1
2025-05-27 09:14:51.043152 第663行 找到匹配: 表=IOT_Sales, rowid=197960, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:51.044147 删除操作影响行数: 1
2025-05-27 09:14:51.113646 第664行 找到匹配: 表=IOT_Sales, rowid=198121, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:51.114567 更新操作影响行数: 1
2025-05-27 09:14:51.184012 第665行 找到匹配: 表=IOT_Sales, rowid=198121, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:51.185286 更新操作影响行数: 1
2025-05-27 09:14:51.676708 第671行 找到匹配: 表=IOT_Sales, rowid=197355, 原金额=3.0, 退款=10.0, 新金额=-7.0
2025-05-27 09:14:51.677767 删除操作影响行数: 1
2025-05-27 09:14:51.934286 第674行 找到匹配: 表=IOT_Sales, rowid=198383, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:51.935192 删除操作影响行数: 1
2025-05-27 09:14:52.003494 第675行 找到匹配: 表=IOT_Sales, rowid=3972, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:52.004471 删除操作影响行数: 1
2025-05-27 09:14:52.092313 第676行 找到匹配: 表=IOT_Sales, rowid=198121, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:52.093370 删除操作影响行数: 1
2025-05-27 09:14:52.186936 第677行 找到匹配: 表=IOT_Sales, rowid=198560, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:52.187975 更新操作影响行数: 1
2025-05-27 09:14:52.307220 第678行 找到匹配: 表=IOT_Sales, rowid=198560, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:52.308357 删除操作影响行数: 1
2025-05-27 09:14:52.931618 第684行 找到匹配: 表=IOT_Sales, rowid=197371, 原金额=2.0, 退款=10.0, 新金额=-8.0
2025-05-27 09:14:52.934077 删除操作影响行数: 1
2025-05-27 09:14:53.207159 第687行 找到匹配: 表=IOT_Sales, rowid=199829, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:53.207834 删除操作影响行数: 1
2025-05-27 09:14:53.279576 第688行 找到匹配: 表=IOT_Sales, rowid=3976, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:53.280245 删除操作影响行数: 1
2025-05-27 09:14:53.858815 第695行 找到匹配: 表=IOT_Sales, rowid=77886, 原金额=5.0, 退款=1.0, 新金额=4.0
2025-05-27 09:14:53.860399 更新操作影响行数: 1
2025-05-27 09:14:53.944164 第696行 找到匹配: 表=IOT_Sales, rowid=223373, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:53.945243 删除操作影响行数: 1
2025-05-27 09:14:54.145589 第698行 找到匹配: 表=IOT_Sales, rowid=77886, 原金额=4.0, 退款=1.0, 新金额=3.0
2025-05-27 09:14:54.146702 更新操作影响行数: 1
2025-05-27 09:14:54.231883 第699行 找到匹配: 表=IOT_Sales, rowid=231510, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:54.232867 更新操作影响行数: 1
2025-05-27 09:14:54.313412 第700行 找到匹配: 表=IOT_Sales, rowid=233335, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:54.314400 删除操作影响行数: 1
2025-05-27 09:14:54.403776 第701行 找到匹配: 表=IOT_Sales, rowid=234194, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:54.404531 更新操作影响行数: 1
2025-05-27 09:14:54.495895 第702行 找到匹配: 表=IOT_Sales, rowid=232716, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:54.496616 删除操作影响行数: 1
2025-05-27 09:14:54.579108 第703行 找到匹配: 表=IOT_Sales, rowid=232094, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:54.579983 删除操作影响行数: 1
2025-05-27 09:14:54.664428 第704行 找到匹配: 表=IOT_Sales, rowid=233351, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:54.665585 删除操作影响行数: 1
2025-05-27 09:14:54.747361 第705行 找到匹配: 表=IOT_Sales, rowid=232997, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:54.748294 删除操作影响行数: 1
2025-05-27 09:14:54.836001 第706行 找到匹配: 表=IOT_Sales, rowid=232878, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:54.837622 删除操作影响行数: 1
2025-05-27 09:14:54.923499 第707行 找到匹配: 表=IOT_Sales, rowid=233981, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:54.924456 删除操作影响行数: 1
2025-05-27 09:14:54.999904 第708行 找到匹配: 表=IOT_Sales, rowid=232457, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:55.000912 删除操作影响行数: 1
2025-05-27 09:14:55.074720 第709行 找到匹配: 表=IOT_Sales, rowid=231692, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:55.075968 删除操作影响行数: 1
2025-05-27 09:14:55.161359 第710行 找到匹配: 表=IOT_Sales, rowid=232303, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:55.162360 删除操作影响行数: 1
2025-05-27 09:14:55.232929 第711行 找到匹配: 表=IOT_Sales, rowid=232309, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:55.233919 删除操作影响行数: 1
2025-05-27 09:14:55.306999 第712行 找到匹配: 表=IOT_Sales, rowid=231292, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:55.307655 删除操作影响行数: 1
2025-05-27 09:14:55.379984 第713行 找到匹配: 表=IOT_Sales, rowid=231961, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:55.380840 删除操作影响行数: 1
2025-05-27 09:14:55.450330 第714行 找到匹配: 表=IOT_Sales, rowid=231365, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:55.451243 删除操作影响行数: 1
2025-05-27 09:14:55.520176 第715行 找到匹配: 表=IOT_Sales, rowid=231280, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:55.521102 删除操作影响行数: 1
2025-05-27 09:14:55.591253 第716行 找到匹配: 表=IOT_Sales, rowid=231252, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:55.592159 删除操作影响行数: 1
2025-05-27 09:14:55.672611 第717行 找到匹配: 表=IOT_Sales, rowid=234614, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:55.673685 删除操作影响行数: 1
2025-05-27 09:14:55.748119 第718行 找到匹配: 表=IOT_Sales, rowid=234627, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:55.749136 删除操作影响行数: 1
2025-05-27 09:14:55.835799 第719行 找到匹配: 表=IOT_Sales, rowid=234639, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:55.837343 删除操作影响行数: 1
2025-05-27 09:14:55.943721 第720行 找到匹配: 表=IOT_Sales, rowid=234658, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:55.944805 更新操作影响行数: 1
2025-05-27 09:14:56.042439 第721行 找到匹配: 表=IOT_Sales, rowid=237779, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:56.043790 删除操作影响行数: 1
2025-05-27 09:14:56.131748 第722行 找到匹配: 表=IOT_Sales, rowid=235088, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:14:56.132910 更新操作影响行数: 1
2025-05-27 09:14:56.223469 第723行 找到匹配: 表=IOT_Sales, rowid=237223, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:56.224529 更新操作影响行数: 1
2025-05-27 09:14:56.313093 第724行 找到匹配: 表=IOT_Sales, rowid=237816, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:56.314151 删除操作影响行数: 1
2025-05-27 09:14:56.396973 第725行 找到匹配: 表=IOT_Sales, rowid=237684, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:56.397891 删除操作影响行数: 1
2025-05-27 09:14:56.474558 第726行 找到匹配: 表=IOT_Sales, rowid=235286, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:56.475585 删除操作影响行数: 1
2025-05-27 09:14:56.555556 第727行 找到匹配: 表=IOT_Sales, rowid=235371, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:56.557338 删除操作影响行数: 1
2025-05-27 09:14:56.632890 第728行 找到匹配: 表=IOT_Sales, rowid=235452, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:56.633893 更新操作影响行数: 1
2025-05-27 09:14:56.707453 第729行 找到匹配: 表=IOT_Sales, rowid=235349, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:56.708144 删除操作影响行数: 1
2025-05-27 09:14:56.788438 第730行 找到匹配: 表=IOT_Sales, rowid=235383, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:56.789442 删除操作影响行数: 1
2025-05-27 09:14:56.865863 第731行 找到匹配: 表=IOT_Sales, rowid=234631, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:56.866828 删除操作影响行数: 1
2025-05-27 09:14:56.943617 第732行 找到匹配: 表=IOT_Sales, rowid=235977, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:56.944575 删除操作影响行数: 1
2025-05-27 09:14:57.016838 第733行 找到匹配: 表=IOT_Sales, rowid=235730, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:57.017621 删除操作影响行数: 1
2025-05-27 09:14:57.101828 第734行 找到匹配: 表=IOT_Sales, rowid=235293, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:57.102709 删除操作影响行数: 1
2025-05-27 09:14:57.187083 第735行 找到匹配: 表=IOT_Sales, rowid=235340, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:57.188344 删除操作影响行数: 1
2025-05-27 09:14:57.280747 第736行 找到匹配: 表=IOT_Sales, rowid=234944, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:57.281740 删除操作影响行数: 1
2025-05-27 09:14:57.381979 第737行 找到匹配: 表=IOT_Sales, rowid=234919, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:57.383789 删除操作影响行数: 1
2025-05-27 09:14:57.626063 第738行 找到匹配: 表=IOT_Sales, rowid=234686, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:57.627283 删除操作影响行数: 1
2025-05-27 09:14:57.735916 第739行 找到匹配: 表=IOT_Sales, rowid=236176, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:57.736797 删除操作影响行数: 1
2025-05-27 09:14:57.854979 第740行 找到匹配: 表=IOT_Sales, rowid=234606, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:57.856433 删除操作影响行数: 1
2025-05-27 09:14:57.962376 第741行 找到匹配: 表=IOT_Sales, rowid=246006, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:57.963493 删除操作影响行数: 1
2025-05-27 09:14:58.058628 第742行 找到匹配: 表=IOT_Sales, rowid=240068, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:58.059954 删除操作影响行数: 1
2025-05-27 09:14:58.149960 第743行 找到匹配: 表=IOT_Sales, rowid=245985, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:58.151150 删除操作影响行数: 1
2025-05-27 09:14:58.245119 第744行 找到匹配: 表=IOT_Sales, rowid=245920, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:58.246927 删除操作影响行数: 1
2025-05-27 09:14:58.348436 第745行 找到匹配: 表=IOT_Sales, rowid=240104, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:14:58.349345 删除操作影响行数: 1
2025-05-27 09:14:58.434880 第746行 找到匹配: 表=IOT_Sales, rowid=242192, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:58.436394 删除操作影响行数: 1
2025-05-27 09:14:58.536987 第747行 找到匹配: 表=IOT_Sales, rowid=245418, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:58.538857 删除操作影响行数: 1
2025-05-27 09:14:58.625794 第748行 找到匹配: 表=IOT_Sales, rowid=239948, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:58.626777 删除操作影响行数: 1
2025-05-27 09:14:58.709043 第749行 找到匹配: 表=IOT_Sales, rowid=244518, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:58.710056 删除操作影响行数: 1
2025-05-27 09:14:58.789561 第750行 找到匹配: 表=IOT_Sales, rowid=244628, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:58.790536 删除操作影响行数: 1
2025-05-27 09:14:58.888556 第751行 找到匹配: 表=IOT_Sales, rowid=244334, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:58.890239 删除操作影响行数: 1
2025-05-27 09:14:58.989202 第752行 找到匹配: 表=IOT_Sales, rowid=244980, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:58.990075 更新操作影响行数: 1
2025-05-27 09:14:59.087833 第753行 找到匹配: 表=IOT_Sales, rowid=244232, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:59.088674 删除操作影响行数: 1
2025-05-27 09:14:59.193931 第754行 找到匹配: 表=IOT_Sales, rowid=244105, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:14:59.195080 删除操作影响行数: 1
2025-05-27 09:14:59.294011 第755行 找到匹配: 表=IOT_Sales, rowid=242297, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:59.295041 删除操作影响行数: 1
2025-05-27 09:14:59.380315 第756行 找到匹配: 表=IOT_Sales, rowid=241585, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:59.381302 删除操作影响行数: 1
2025-05-27 09:14:59.467473 第757行 找到匹配: 表=IOT_Sales, rowid=241339, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:14:59.468206 删除操作影响行数: 1
2025-05-27 09:14:59.566035 第758行 找到匹配: 表=IOT_Sales, rowid=242228, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:14:59.567314 删除操作影响行数: 1
2025-05-27 09:14:59.670362 第759行 找到匹配: 表=IOT_Sales, rowid=243111, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:14:59.671332 删除操作影响行数: 1
2025-05-27 09:14:59.792739 第760行 找到匹配: 表=IOT_Sales, rowid=243134, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:14:59.794658 更新操作影响行数: 1
2025-05-27 09:14:59.913212 第761行 找到匹配: 表=IOT_Sales, rowid=242438, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:14:59.914369 删除操作影响行数: 1
2025-05-27 09:15:00.001843 第762行 找到匹配: 表=IOT_Sales, rowid=240420, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:00.003084 删除操作影响行数: 1
2025-05-27 09:15:00.109518 第763行 找到匹配: 表=IOT_Sales, rowid=241853, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:15:00.110273 更新操作影响行数: 1
2025-05-27 09:15:00.223157 第764行 找到匹配: 表=IOT_Sales, rowid=240568, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:15:00.224374 删除操作影响行数: 1
2025-05-27 09:15:00.321846 第765行 找到匹配: 表=IOT_Sales, rowid=240147, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:00.322893 删除操作影响行数: 1
2025-05-27 09:15:00.426335 第766行 找到匹配: 表=IOT_Sales, rowid=241810, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:00.428209 删除操作影响行数: 1
2025-05-27 09:15:00.539069 第767行 找到匹配: 表=IOT_Sales, rowid=241285, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:00.540415 删除操作影响行数: 1
2025-05-27 09:15:00.644014 第768行 找到匹配: 表=IOT_Sales, rowid=241277, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:00.645140 删除操作影响行数: 1
2025-05-27 09:15:00.725876 第769行 找到匹配: 表=IOT_Sales, rowid=240075, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:00.726833 删除操作影响行数: 1
2025-05-27 09:15:00.812586 第770行 找到匹配: 表=IOT_Sales, rowid=240107, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:00.813565 删除操作影响行数: 1
2025-05-27 09:15:00.891561 第771行 找到匹配: 表=IOT_Sales, rowid=234599, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:15:00.892521 删除操作影响行数: 1
2025-05-27 09:15:00.966278 第772行 找到匹配: 表=IOT_Sales, rowid=234651, 原金额=5.0, 退款=15.0, 新金额=-10.0
2025-05-27 09:15:00.967279 删除操作影响行数: 1
2025-05-27 09:15:01.039006 第773行 找到匹配: 表=IOT_Sales, rowid=238430, 原金额=15.0, 退款=5.0, 新金额=10.0
2025-05-27 09:15:01.039701 更新操作影响行数: 1
2025-05-27 09:15:01.116433 第774行 找到匹配: 表=IOT_Sales, rowid=236277, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:15:01.117589 删除操作影响行数: 1
2025-05-27 09:15:01.193824 第775行 找到匹配: 表=IOT_Sales, rowid=238430, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:15:01.194941 删除操作影响行数: 1
2025-05-27 09:15:01.267284 第776行 找到匹配: 表=IOT_Sales, rowid=234587, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:15:01.268264 删除操作影响行数: 1
2025-05-27 09:15:01.344539 第777行 找到匹配: 表=IOT_Sales, rowid=243978, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:15:01.345544 删除操作影响行数: 1
2025-05-27 09:15:01.423904 第778行 找到匹配: 表=IOT_Sales, rowid=240786, 原金额=10.0, 退款=15.0, 新金额=-5.0
2025-05-27 09:15:01.424875 删除操作影响行数: 1
2025-05-27 09:15:01.499253 第779行 找到匹配: 表=IOT_Sales, rowid=240258, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:01.499920 删除操作影响行数: 1
2025-05-27 09:15:01.577609 第780行 找到匹配: 表=IOT_Sales, rowid=245119, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:15:01.578630 更新操作影响行数: 1
2025-05-27 09:15:01.673622 第781行 找到匹配: 表=IOT_Sales, rowid=240115, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:15:01.674742 删除操作影响行数: 1
2025-05-27 09:15:01.761577 第782行 找到匹配: 表=IOT_Sales, rowid=240235, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:01.762654 删除操作影响行数: 1
2025-05-27 09:15:01.858676 第783行 找到匹配: 表=IOT_Sales, rowid=239863, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:15:01.860036 删除操作影响行数: 1
2025-05-27 09:15:01.962416 第784行 找到匹配: 表=ZERO_Sales, rowid=57986, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:01.963353 删除操作影响行数: 1
2025-05-27 09:15:02.070242 第785行 找到匹配: 表=ZERO_Sales, rowid=57842, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:02.071362 删除操作影响行数: 1
2025-05-27 09:15:02.185140 第786行 找到匹配: 表=ZERO_Sales, rowid=58184, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:02.186374 删除操作影响行数: 1
2025-05-27 09:15:02.287507 第787行 找到匹配: 表=ZERO_Sales, rowid=58101, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:15:02.288215 删除操作影响行数: 1
2025-05-27 09:15:02.395151 第788行 找到匹配: 表=ZERO_Sales, rowid=59492, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:15:02.395875 删除操作影响行数: 1
2025-05-27 09:15:02.641873 第789行 找到匹配: 表=ZERO_Sales, rowid=59493, 原金额=10.0, 退款=5.0, 新金额=5.0
2025-05-27 09:15:02.642614 更新操作影响行数: 1
2025-05-27 09:15:02.755979 第790行 找到匹配: 表=ZERO_Sales, rowid=58986, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:15:02.757450 删除操作影响行数: 1
2025-05-27 09:15:02.864300 第791行 找到匹配: 表=ZERO_Sales, rowid=58887, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:15:02.865238 删除操作影响行数: 1
2025-05-27 09:15:02.959598 第792行 找到匹配: 表=ZERO_Sales, rowid=57824, 原金额=20.0, 退款=20.0, 新金额=0.0
2025-05-27 09:15:02.960607 删除操作影响行数: 1
2025-05-27 09:15:03.040544 第793行 找到匹配: 表=IOT_Sales, rowid=232414, 原金额=15.0, 退款=10.0, 新金额=5.0
2025-05-27 09:15:03.042022 更新操作影响行数: 1
2025-05-27 09:15:03.120174 第794行 找到匹配: 表=IOT_Sales, rowid=240539, 原金额=15.0, 退款=15.0, 新金额=0.0
2025-05-27 09:15:03.121125 删除操作影响行数: 1
2025-05-27 09:15:03.199007 第795行 找到匹配: 表=IOT_Sales, rowid=235322, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:15:03.199951 删除操作影响行数: 1
2025-05-27 09:15:03.283481 第796行 找到匹配: 表=IOT_Sales, rowid=235766, 原金额=10.0, 退款=10.0, 新金额=0.0
2025-05-27 09:15:03.284379 删除操作影响行数: 1
2025-05-27 09:15:03.364034 第797行 找到匹配: 表=IOT_Sales, rowid=235609, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:03.364669 删除操作影响行数: 1
2025-05-27 09:15:03.445485 第798行 找到匹配: 表=IOT_Sales, rowid=233123, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:03.446519 删除操作影响行数: 1
2025-05-27 09:15:03.534500 第799行 找到匹配: 表=IOT_Sales, rowid=229134, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:03.535488 删除操作影响行数: 1
2025-05-27 09:15:03.627089 第800行 找到匹配: 表=IOT_Sales, rowid=246711, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:03.628316 删除操作影响行数: 1
2025-05-27 09:15:03.713535 第801行 找到匹配: 表=IOT_Sales, rowid=246344, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:03.714519 删除操作影响行数: 1
2025-05-27 09:15:03.800002 第802行 找到匹配: 表=IOT_Sales, rowid=246514, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:03.800700 删除操作影响行数: 1
2025-05-27 09:15:03.882111 第803行 找到匹配: 表=IOT_Sales, rowid=246183, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:03.883414 删除操作影响行数: 1
2025-05-27 09:15:03.965955 第804行 找到匹配: 表=IOT_Sales, rowid=246188, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:03.967053 删除操作影响行数: 1
2025-05-27 09:15:04.040673 第805行 找到匹配: 表=IOT_Sales, rowid=251905, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:15:04.041502 删除操作影响行数: 1
2025-05-27 09:15:04.112100 第806行 找到匹配: 表=IOT_Sales, rowid=251898, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:15:04.113026 删除操作影响行数: 1
2025-05-27 09:15:04.185638 第807行 找到匹配: 表=IOT_Sales, rowid=251792, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:15:04.186662 删除操作影响行数: 1
2025-05-27 09:15:04.258661 第808行 找到匹配: 表=IOT_Sales, rowid=251779, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:15:04.259594 删除操作影响行数: 1
2025-05-27 09:15:04.325490 第809行 找到匹配: 表=IOT_Sales, rowid=251393, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:15:04.326724 删除操作影响行数: 1
2025-05-27 09:15:04.392642 第810行 找到匹配: 表=IOT_Sales, rowid=251391, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:15:04.393330 删除操作影响行数: 1
2025-05-27 09:15:04.457059 第811行 找到匹配: 表=IOT_Sales, rowid=251379, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:15:04.457709 删除操作影响行数: 1
2025-05-27 09:15:04.525767 第812行 找到匹配: 表=IOT_Sales, rowid=275755, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:15:04.527064 删除操作影响行数: 1
2025-05-27 09:15:04.596770 第813行 找到匹配: 表=IOT_Sales, rowid=275616, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:15:04.598053 删除操作影响行数: 1
2025-05-27 09:15:04.673731 第814行 找到匹配: 表=IOT_Sales, rowid=275589, 原金额=1.0, 退款=1.0, 新金额=0.0
2025-05-27 09:15:04.674770 删除操作影响行数: 1
2025-05-27 09:15:04.755692 第815行 找到匹配: 表=IOT_Sales, rowid=276239, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:04.757047 删除操作影响行数: 1
2025-05-27 09:15:04.830183 第816行 找到匹配: 表=IOT_Sales, rowid=276171, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:04.831218 删除操作影响行数: 1
2025-05-27 09:15:04.901659 第817行 找到匹配: 表=IOT_Sales, rowid=277524, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:04.902941 删除操作影响行数: 1
2025-05-27 09:15:04.973353 第818行 找到匹配: 表=IOT_Sales, rowid=276345, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:04.974423 删除操作影响行数: 1
2025-05-27 09:15:05.042857 第819行 找到匹配: 表=IOT_Sales, rowid=276133, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:05.043818 删除操作影响行数: 1
2025-05-27 09:15:05.112676 第820行 找到匹配: 表=IOT_Sales, rowid=277887, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:05.113731 删除操作影响行数: 1
2025-05-27 09:15:05.199529 第821行 找到匹配: 表=IOT_Sales, rowid=277888, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:05.200467 删除操作影响行数: 1
2025-05-27 09:15:05.290379 第822行 找到匹配: 表=IOT_Sales, rowid=276625, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:05.291715 删除操作影响行数: 1
2025-05-27 09:15:05.393053 第823行 找到匹配: 表=IOT_Sales, rowid=277527, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:05.394340 删除操作影响行数: 1
2025-05-27 09:15:05.469251 第824行 找到匹配: 表=IOT_Sales, rowid=276346, 原金额=5.0, 退款=10.0, 新金额=-5.0
2025-05-27 09:15:05.470181 删除操作影响行数: 1
2025-05-27 09:15:05.546021 第825行 找到匹配: 表=IOT_Sales, rowid=277861, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:05.547019 删除操作影响行数: 1
2025-05-27 09:15:05.645411 第826行 找到匹配: 表=ZERO_Sales, rowid=61775, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:05.646753 删除操作影响行数: 1
2025-05-27 09:15:05.744558 第827行 找到匹配: 表=ZERO_Sales, rowid=61784, 原金额=2.0, 退款=2.0, 新金额=0.0
2025-05-27 09:15:05.745543 删除操作影响行数: 1
2025-05-27 09:15:05.864455 第828行 找到匹配: 表=ZERO_Sales, rowid=61797, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:05.865399 删除操作影响行数: 1
2025-05-27 09:15:05.957137 第829行 找到匹配: 表=ZERO_Sales, rowid=61815, 原金额=5.0, 退款=5.0, 新金额=0.0
2025-05-27 09:15:05.958092 删除操作影响行数: 1
