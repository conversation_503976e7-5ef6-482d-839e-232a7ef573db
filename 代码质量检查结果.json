{"syntax_check": {"config.py": {"status": "PASS", "error": null}, "database_manager.py": {"status": "PASS", "error": null}, "ui_utils.py": {"status": "PASS", "error": null}, "优化示例对比.py": {"status": "PASS", "error": null}}, "import_check": {"config": {"status": "PASS", "error": null}, "database_manager": {"status": "PASS", "error": null}, "ui_utils": {"status": "PASS", "error": null}}, "functionality_check": {"config": {"status": "PASS", "error": null}, "database_manager": {"status": "PASS", "error": null}, "ui_utils": {"status": "PASS", "error": null}}, "performance_check": {"status": "PASS", "config_access_time": "0.0000秒 (1000次)", "database_query_time": "0.0069秒 (10次)", "error": null}, "summary": {"total_checks": 11, "passed_checks": 11, "success_rate": "100.0%", "status": "PASS"}}