# APP数据管理功能说明

## 功能概述

本次更新增加了以下功能：

1. **APP表结构创建**：创建与IOT和ZERO表结构一致的APP_Sales表
2. **数据库备份与恢复**：提供数据库的备份和从备份恢复的功能
3. **视图更新**：修改现有视图以包含APP销售数据
   - 更新Sales_Combined视图
   - 更新Daily_Equipment_Sales表
   - 更新Daily_Sales视图
   - 新增APP_Daily_Sales视图

## 使用步骤

### 1. 创建APP表结构

首先需要运行`创建APP表结构.py`脚本，该脚本将在数据库中创建APP_Sales表。

```
1. 双击运行 创建APP表结构.py
2. 脚本将自动检查APP_Sales表是否存在，如不存在则创建
3. 操作日志将记录在 app_table_creation.log 文件中
```

### 2. 数据库管理

运行`数据库管理工具.py`脚本，可以进行以下操作：

```
1. 备份数据库 - 将当前数据库备份到backups目录
2. 恢复数据库 - 从之前的备份中恢复数据库
3. 更新视图 - 更新所有视图以包含APP销售数据
```

#### 备份数据库

选择选项1，系统将自动创建一个带有时间戳的备份文件，存储在`database\backups`目录下。

#### 恢复数据库

选择选项2，系统将显示所有可用的备份文件，选择要恢复的备份文件编号，确认后即可恢复。

#### 更新视图

选择选项3，系统将更新所有相关视图以包含APP销售数据：

- 创建APP_Daily_Sales视图
- 更新Daily_Equipment_Sales视图
- 更新Sales_Combined视图
- 更新Daily_Sales视图

### 3. 数据导入

现有的`数据导入脚本.py`已经支持从IOT和ZERO文件中提取API order类型的订单并导入到APP_Sales表中，无需额外操作。

## 注意事项

1. 在更新视图之前，请确保已经创建了APP_Sales表
2. 建议在进行重要操作前先备份数据库
3. 所有操作日志将记录在相应的日志文件中，便于追踪问题
4. APP数据来源于IOT和ZERO文件中的'Api order'类型订单

## 数据结构说明

### 新增视图

#### APP_Daily_Sales

按设备ID和日期汇总APP销售数据：

- Chair_Serial_No: 设备ID
- Sale_Date: 销售日期
- Daily_APP_Price: 当日APP销售金额
- Daily_APP_Count: 当日APP销售次数

### 更新视图

#### Daily_Equipment_Sales

增加了以下字段：

- APP_Price: APP销售金额
- APP_Count: APP销售次数
- Total_Price: 包含APP销售金额的总金额

#### Sales_Combined

增加了以下字段：

- APP_Sales: APP销售次数
- APP_Order_Price: APP销售金额
- APP_Date: 最近APP销售日期
- Total_Order_Price: 包含APP销售金额的总金额