# -*- coding: utf-8 -*-
"""
数据库管理模块
统一管理所有数据库操作，实现连接池和事务管理
"""

import sqlite3
import threading
import logging
from contextlib import contextmanager
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from config import config


@dataclass
class QueryResult:
    """查询结果封装"""
    data: List[Tuple]
    row_count: int
    columns: List[str]
    success: bool
    error_message: Optional[str] = None


class DatabaseError(Exception):
    """数据库操作异常"""
    pass


class DatabaseManager:
    """数据库管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, db_path: str = None):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, db_path: str = None):
        if hasattr(self, '_initialized'):
            return
            
        self.db_path = db_path or config.database.DEFAULT_DB_PATH
        self.logger = logging.getLogger(__name__)
        self._local = threading.local()
        self._initialized = True
        
        # 初始化数据库
        self._initialize_database()
    
    def _initialize_database(self):
        """初始化数据库和表结构"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建Equipment_ID表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS Equipment_ID (
                        ID INTEGER PRIMARY KEY AUTOINCREMENT,
                        STATE TEXT,
                        Location TEXT,
                        Quantity INTEGER,
                        Chair_Serial_No TEXT UNIQUE,
                        Sim_Card_Model TEXT,
                        Sim_Card_No TEXT,
                        Layer TEXT,
                        Company TEXT,
                        Effective_From TEXT,
                        Effective_To TEXT,
                        Rental REAL,
                        SIMCARDID TEXT,
                        Import_Date TEXT,
                        Last_Updated TEXT,
                        CurrentFlag TEXT,
                        DATE TEXT
                    )
                """)
                
                # 创建Daily_Equipment_Sales表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS Daily_Equipment_Sales (
                        ID INTEGER PRIMARY KEY AUTOINCREMENT,
                        Chair_Serial_No TEXT,
                        Sale_Date TEXT,
                        State TEXT,
                        Location TEXT,
                        Quantity INTEGER,
                        Layer TEXT,
                        DATE TEXT,
                        Rental REAL,
                        IOT_Price REAL,
                        ZERO_Price REAL,
                        Checked INTEGER DEFAULT 0,
                        Expiry_Date TEXT
                    )
                """)
                
                # 创建Logs表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS Logs (
                        ID INTEGER PRIMARY KEY AUTOINCREMENT,
                        Timestamp TEXT,
                        Action TEXT,
                        Description TEXT,
                        Target TEXT
                    )
                """)
                
                # 创建索引以提高查询性能
                self._create_indexes(cursor)
                
                conn.commit()
                self.logger.info(f"数据库初始化完成: {self.db_path}")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise DatabaseError(f"数据库初始化失败: {e}")
    
    def _create_indexes(self, cursor):
        """创建数据库索引"""
        indexes = [
            ("idx_chair_serial_no", "Equipment_ID", "Chair_Serial_No"),
            ("idx_effective_dates", "Equipment_ID", "Effective_From, Effective_To"),
            ("idx_location", "Equipment_ID", "Location"),
            ("idx_state", "Equipment_ID", "STATE"),
            ("idx_last_updated", "Equipment_ID", "Last_Updated"),
            ("idx_sales_serial", "Daily_Equipment_Sales", "Chair_Serial_No"),
            ("idx_sales_date", "Daily_Equipment_Sales", "Sale_Date"),
            ("idx_logs_timestamp", "Logs", "Timestamp")
        ]

        for index_name, table_name, columns in indexes:
            try:
                # 先检查表是否存在且不是视图
                cursor.execute("""
                    SELECT type FROM sqlite_master
                    WHERE name = ? AND type = 'table'
                """, (table_name,))

                if cursor.fetchone():
                    index_sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({columns})"
                    cursor.execute(index_sql)
                    self.logger.debug(f"成功创建索引: {index_name}")
                else:
                    self.logger.debug(f"跳过索引创建，表不存在或为视图: {table_name}")

            except sqlite3.Error as e:
                self.logger.warning(f"创建索引 {index_name} 失败: {e}")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=config.database.CONNECTION_TIMEOUT,
                check_same_thread=False
            )
            conn.row_factory = sqlite3.Row  # 支持字典式访问
            yield conn
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            self.logger.error(f"数据库连接错误: {e}")
            raise DatabaseError(f"数据库连接错误: {e}")
        finally:
            if conn:
                conn.close()
    
    def execute_query(self, query: str, params: Tuple = None, fetch_all: bool = True) -> QueryResult:
        """执行查询语句"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params or ())
                
                if fetch_all:
                    data = cursor.fetchall()
                    # 转换Row对象为tuple以保持兼容性
                    data = [tuple(row) for row in data]
                else:
                    data = cursor.fetchone()
                    if data:
                        data = tuple(data)
                
                columns = [description[0] for description in cursor.description] if cursor.description else []
                row_count = len(data) if isinstance(data, list) else (1 if data else 0)
                
                return QueryResult(
                    data=data,
                    row_count=row_count,
                    columns=columns,
                    success=True
                )
                
        except Exception as e:
            self.logger.error(f"查询执行失败: {query}, 参数: {params}, 错误: {e}")
            return QueryResult(
                data=[],
                row_count=0,
                columns=[],
                success=False,
                error_message=str(e)
            )
    
    def execute_update(self, query: str, params: Tuple = None) -> bool:
        """执行更新语句（INSERT, UPDATE, DELETE）"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params or ())
                conn.commit()
                self.logger.debug(f"更新执行成功: {query}")
                return True
                
        except Exception as e:
            self.logger.error(f"更新执行失败: {query}, 参数: {params}, 错误: {e}")
            return False
    
    def execute_batch(self, query: str, params_list: List[Tuple]) -> Tuple[int, int]:
        """批量执行语句"""
        success_count = 0
        failed_count = 0
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("BEGIN TRANSACTION")
                
                for params in params_list:
                    try:
                        cursor.execute(query, params)
                        success_count += 1
                    except Exception as e:
                        self.logger.warning(f"批量操作单条失败: {params}, 错误: {e}")
                        failed_count += 1
                
                cursor.execute("COMMIT")
                self.logger.info(f"批量操作完成: 成功 {success_count}, 失败 {failed_count}")
                
        except Exception as e:
            self.logger.error(f"批量操作失败: {e}")
            failed_count = len(params_list)
            success_count = 0
        
        return success_count, failed_count
    
    @contextmanager
    def transaction(self):
        """事务上下文管理器"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                cursor.execute("BEGIN TRANSACTION")
                yield cursor
                cursor.execute("COMMIT")
            except Exception as e:
                cursor.execute("ROLLBACK")
                self.logger.error(f"事务回滚: {e}")
                raise
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表结构信息"""
        result = self.execute_query(f"PRAGMA table_info({table_name})")
        if result.success:
            columns = ['cid', 'name', 'type', 'notnull', 'dflt_value', 'pk']
            return [dict(zip(columns, row)) for row in result.data]
        return []
    
    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        result = self.execute_query(
            "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
            (table_name,),
            fetch_all=False
        )
        return result.success and result.data is not None
    
    def get_row_count(self, table_name: str, where_clause: str = "", params: Tuple = None) -> int:
        """获取表行数"""
        query = f"SELECT COUNT(*) FROM {table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
        
        result = self.execute_query(query, params, fetch_all=False)
        if result.success and result.data:
            return result.data[0]
        return 0
    
    def backup_database(self, backup_path: str) -> bool:
        """备份数据库"""
        try:
            with self.get_connection() as source:
                backup = sqlite3.connect(backup_path)
                source.backup(backup)
                backup.close()
            self.logger.info(f"数据库备份成功: {backup_path}")
            return True
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            return False


# 全局数据库管理器实例
db_manager = DatabaseManager()


# 便捷函数，保持向后兼容
def execute_query(query: str, params: Tuple = None) -> List[Tuple]:
    """执行查询并返回结果"""
    result = db_manager.execute_query(query, params)
    return result.data if result.success else []


def execute_update(query: str, params: Tuple = None) -> bool:
    """执行更新操作"""
    return db_manager.execute_update(query, params)


def get_connection():
    """获取数据库连接（向后兼容）"""
    return db_manager.get_connection()


class EquipmentQueries:
    """设备相关查询封装"""

    @staticmethod
    def get_valid_equipment(today: str) -> str:
        """获取有效设备的查询语句"""
        return """
            SELECT * FROM Equipment_ID
            WHERE (Effective_To IS NULL OR Effective_To = '' OR Effective_To > ?)
        """

    @staticmethod
    def get_expired_equipment(today: str) -> str:
        """获取过期设备的查询语句"""
        return """
            SELECT * FROM Equipment_ID
            WHERE Effective_To < ? AND Effective_To IS NOT NULL AND Effective_To != ''
        """

    @staticmethod
    def find_empty_values(field: str, today: str) -> str:
        """查找空值的查询语句"""
        return f"""
            SELECT * FROM Equipment_ID
            WHERE ({field} IS NULL OR {field} = '')
            AND (Effective_To IS NULL OR Effective_To = '' OR Effective_To > ?)
        """

    @staticmethod
    def find_duplicates() -> str:
        """查找重复序列号的查询语句"""
        return """
            SELECT Chair_Serial_No,
                COUNT(*) as Record_Count,
                GROUP_CONCAT(STATE || '|' || Location || '|' || Effective_From || '|' || Effective_To) AS Details
            FROM (
                SELECT e.*,
                    CASE
                        WHEN EXISTS (SELECT 1 FROM Equipment_ID e2
                                    WHERE e2.Chair_Serial_No = e.Chair_Serial_No
                                    AND e2.ID != e.ID
                                    AND (e2.Effective_From IS NULL OR date(e2.Effective_From) <= date('now'))
                                    AND (e2.Effective_To IS NULL OR date(e2.Effective_To) >= date('now')))
                        THEN 'DUPLICATE'
                        ELSE 'ACTIVE'
                    END as CurrentFlag
                FROM Equipment_ID e
            )
            WHERE CurrentFlag = 'DUPLICATE'
            GROUP BY Chair_Serial_No
            HAVING COUNT(*) > 1
        """

    @staticmethod
    def search_equipment(serial: str = None, location: str = None, state: str = None, today: str = None) -> Tuple[str, List]:
        """搜索设备的查询语句和参数"""
        params = []
        conditions = []

        # 基础条件：排除过期记录
        if today:
            conditions.append("(Effective_To IS NULL OR Effective_To = '' OR Effective_To > ?)")
            params.append(today)

        # 搜索条件
        if serial:
            conditions.append("Chair_Serial_No LIKE ?")
            params.append(f"%{serial}%")
        if location:
            conditions.append("Location LIKE ?")
            params.append(f"%{location}%")
        if state:
            conditions.append("STATE LIKE ?")
            params.append(f"%{state}%")

        where_clause = " AND ".join(conditions) if conditions else "1=1"
        query = f"SELECT * FROM Equipment_ID WHERE {where_clause}"

        return query, params


class SalesQueries:
    """销售记录相关查询封装"""

    @staticmethod
    def get_unmatched_sales() -> str:
        """获取未匹配销售记录的查询语句"""
        return """
            SELECT t.Chair_Serial_No, t.Sale_Date, t.IOT_Price, t.ZERO_Price
            FROM (
                SELECT Chair_Serial_No, Sale_Date, IOT_Price, ZERO_Price
                FROM Daily_Equipment_Sales
                WHERE State IS NULL
                AND Location IS NULL
                AND Quantity IS NULL
                AND Chair_Serial_No IS NOT NULL
                AND Layer IS NULL
                AND DATE IS NULL
                AND Rental IS NULL
                AND (IOT_Price > ? OR ZERO_Price > ?)
            ) t
            INNER JOIN (
                SELECT Chair_Serial_No, MAX(Sale_Date) AS MaxDate
                FROM Daily_Equipment_Sales
                WHERE State IS NULL
                AND Location IS NULL
                AND Quantity IS NULL
                AND Chair_Serial_No IS NOT NULL
                AND Layer IS NULL
                AND DATE IS NULL
                AND Rental IS NULL
                AND (IOT_Price > ? OR ZERO_Price > ?)
                GROUP BY Chair_Serial_No
            ) m
            ON t.Chair_Serial_No = m.Chair_Serial_No AND t.Sale_Date = m.MaxDate
        """

    @staticmethod
    def get_abnormal_sales(year: str, month: str) -> str:
        """获取异常销售记录的查询语句"""
        return """
            SELECT Chair_Serial_No, IOT_Price, Sale_Date
            FROM Daily_Equipment_Sales
            WHERE Chair_Serial_No LIKE 'PAY%'
            AND strftime('%Y', Sale_Date) = ?
            AND strftime('%m', Sale_Date) = ?
        """
