# -*- coding: utf-8 -*-
"""
UI工具模块
统一管理UI相关的工具函数和消息处理
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import datetime
import logging
from typing import Optional, Dict, Any, Callable, List, Tuple
from config import config


class MessageHandler:
    """统一消息处理类"""
    
    @staticmethod
    def show_info(message: str, title: str = None, parent=None) -> None:
        """显示信息提示"""
        title = title or config.get_title("info")
        messagebox.showinfo(title, message, parent=parent)
    
    @staticmethod
    def show_warning(message: str, title: str = None, parent=None) -> None:
        """显示警告提示"""
        title = title or config.get_title("warning")
        messagebox.showwarning(title, message, parent=parent)
    
    @staticmethod
    def show_error(message: str, title: str = None, parent=None) -> None:
        """显示错误提示"""
        title = title or config.get_title("error")
        messagebox.showerror(title, message, parent=parent)
    
    @staticmethod
    def show_success(message: str, title: str = None, parent=None) -> None:
        """显示成功提示"""
        title = title or config.get_title("success")
        messagebox.showinfo(title, message, parent=parent)
    
    @staticmethod
    def ask_yes_no(message: str, title: str = None, parent=None) -> bool:
        """询问是否确认"""
        title = title or config.get_title("confirm")
        return messagebox.askyesno(title, message, parent=parent)
    
    @staticmethod
    def ask_question(message: str, title: str = None, parent=None) -> str:
        """询问问题（返回yes/no）"""
        title = title or config.get_title("question")
        return messagebox.askquestion(title, message, parent=parent)
    
    @staticmethod
    def show_database_error(error: Exception, parent=None) -> None:
        """显示数据库错误"""
        MessageHandler.show_error(
            f"数据库操作失败: {str(error)}\n请检查数据库连接或联系管理员。",
            "数据库错误",
            parent
        )
    
    @staticmethod
    def show_validation_error(field_name: str, error_message: str, parent=None) -> None:
        """显示验证错误"""
        MessageHandler.show_error(
            f"字段 '{field_name}': {error_message}",
            "输入验证错误",
            parent
        )


class FileDialogHelper:
    """文件对话框辅助类"""
    
    @staticmethod
    def open_excel_file(title: str = "选择Excel文件", parent=None) -> Optional[str]:
        """打开Excel文件选择对话框"""
        return filedialog.askopenfilename(
            title=title,
            filetypes=config.files.SUPPORTED_FORMATS["excel"],
            parent=parent
        )
    
    @staticmethod
    def save_excel_file(title: str = "保存Excel文件", default_name: str = None, parent=None) -> Optional[str]:
        """保存Excel文件对话框"""
        return filedialog.asksaveasfilename(
            title=title,
            defaultextension=".xlsx",
            filetypes=config.files.SUPPORTED_FORMATS["excel"],
            initialvalue=default_name,
            parent=parent
        )
    
    @staticmethod
    def select_database_file(title: str = "选择数据库文件", parent=None) -> Optional[str]:
        """选择数据库文件对话框"""
        return filedialog.askopenfilename(
            title=title,
            filetypes=[("数据库文件", "*.db"), ("所有文件", "*.*")],
            parent=parent
        )


class WindowHelper:
    """窗口辅助类"""
    
    @staticmethod
    def create_dialog(parent, title: str, window_type: str = None, 
                     modal: bool = True, resizable: bool = True) -> tk.Toplevel:
        """创建标准对话框"""
        dialog = tk.Toplevel(parent)
        dialog.title(title)
        
        # 设置窗口大小
        size = config.get_window_size(window_type) if window_type else "800x600"
        dialog.geometry(size)
        
        # 设置模态
        if modal:
            dialog.transient(parent)
            dialog.grab_set()
        
        # 设置是否可调整大小
        if not resizable:
            dialog.resizable(False, False)
        
        # 居中显示
        WindowHelper.center_window(dialog)
        
        return dialog
    
    @staticmethod
    def center_window(window) -> None:
        """窗口居中显示"""
        window.update_idletasks()
        width = window.winfo_width()
        height = window.winfo_height()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry(f"+{x}+{y}")
    
    @staticmethod
    def create_labeled_frame(parent, text: str, padding: int = None) -> ttk.LabelFrame:
        """创建带标签的框架"""
        padding = padding or config.ui.PADDING["medium"]
        return ttk.LabelFrame(parent, text=text, padding=padding)
    
    @staticmethod
    def create_button_frame(parent, padding: int = None) -> ttk.Frame:
        """创建按钮框架"""
        padding = padding or config.ui.PADDING["medium"]
        frame = ttk.Frame(parent, padding=(0, padding))
        frame.pack(fill=tk.X, side=tk.BOTTOM)
        return frame


class TreeviewHelper:
    """Treeview辅助类"""
    
    @staticmethod
    def create_treeview(parent, columns: List[str], show_headers: bool = True) -> ttk.Treeview:
        """创建标准Treeview"""
        tree = ttk.Treeview(
            parent, 
            columns=columns, 
            show="headings" if show_headers else ""
        )
        
        # 设置列
        for col in columns:
            tree.heading(col, text=config.columns.COLUMN_DISPLAY_NAMES.get(col, col))
            tree.column(col, width=config.get_column_width(col), anchor="center")
        
        return tree
    
    @staticmethod
    def add_scrollbars(parent, tree: ttk.Treeview) -> Tuple[ttk.Scrollbar, ttk.Scrollbar]:
        """为Treeview添加滚动条"""
        vsb = ttk.Scrollbar(parent, orient="vertical", command=tree.yview)
        hsb = ttk.Scrollbar(parent, orient="horizontal", command=tree.xview)
        
        tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        tree.pack(fill=tk.BOTH, expand=True)
        
        return vsb, hsb
    
    @staticmethod
    def get_selected_values(tree: ttk.Treeview, column_index: int = 0) -> List[Any]:
        """获取选中行的指定列值"""
        selected = tree.selection()
        return [tree.item(item)["values"][column_index] for item in selected]
    
    @staticmethod
    def clear_and_populate(tree: ttk.Treeview, data: List[Tuple]) -> None:
        """清空并填充Treeview数据"""
        # 清空现有数据
        for item in tree.get_children():
            tree.delete(item)
        
        # 添加新数据
        for row in data:
            tree.insert("", "end", values=row)


class FormHelper:
    """表单辅助类"""
    
    @staticmethod
    def create_form_field(parent, label: str, field_type: str = "entry", 
                         row: int = 0, column: int = 0, **kwargs) -> Tuple[ttk.Label, tk.Widget]:
        """创建表单字段"""
        # 创建标签
        label_widget = ttk.Label(parent, text=f"{label}:")
        label_widget.grid(row=row, column=column, sticky="e", padx=5, pady=5)
        
        # 创建输入控件
        if field_type == "entry":
            widget = ttk.Entry(parent, **kwargs)
        elif field_type == "combobox":
            widget = ttk.Combobox(parent, **kwargs)
        elif field_type == "text":
            widget = tk.Text(parent, **kwargs)
        elif field_type == "spinbox":
            widget = ttk.Spinbox(parent, **kwargs)
        else:
            widget = ttk.Entry(parent, **kwargs)
        
        widget.grid(row=row, column=column+1, sticky="w", padx=5, pady=5)
        
        return label_widget, widget
    
    @staticmethod
    def create_button_group(parent, buttons: List[Dict[str, Any]], 
                           side: str = tk.LEFT, padx: int = 5) -> List[ttk.Button]:
        """创建按钮组"""
        button_widgets = []
        
        for btn_config in buttons:
            btn = ttk.Button(
                parent,
                text=btn_config.get("text", "按钮"),
                command=btn_config.get("command"),
                width=btn_config.get("width", 10)
            )
            btn.pack(side=side, padx=padx)
            button_widgets.append(btn)
        
        return button_widgets


class DateTimeHelper:
    """日期时间辅助类"""
    
    @staticmethod
    def get_current_date() -> str:
        """获取当前日期（YYYY-MM-DD格式）"""
        return datetime.datetime.now().strftime(config.business.DATE_FORMATS["standard"])
    
    @staticmethod
    def get_current_timestamp() -> str:
        """获取当前时间戳"""
        return datetime.datetime.now().strftime(config.business.DATE_FORMATS["timestamp"])
    
    @staticmethod
    def get_current_timestamp_ms() -> str:
        """获取当前时间戳（包含毫秒）"""
        return datetime.datetime.now().strftime(config.business.DATE_FORMATS["timestamp_ms"])[:-3]
    
    @staticmethod
    def format_date_for_display(date_str: str) -> str:
        """格式化日期用于显示"""
        if not date_str:
            return ""
        try:
            dt = datetime.datetime.strptime(date_str, config.business.DATE_FORMATS["standard"])
            return dt.strftime(config.business.DATE_FORMATS["display"])
        except ValueError:
            return date_str
    
    @staticmethod
    def is_valid_date(date_str: str) -> bool:
        """验证日期格式"""
        if not date_str:
            return True  # 空日期视为有效
        try:
            datetime.datetime.strptime(date_str, config.business.DATE_FORMATS["standard"])
            return True
        except ValueError:
            return False


class LoadingIndicator:
    """加载指示器"""
    
    def __init__(self, parent, message: str = "正在加载..."):
        self.parent = parent
        self.message = message
        self.label = None
    
    def show(self):
        """显示加载指示器"""
        if self.label is None:
            self.label = ttk.Label(
                self.parent,
                text=self.message,
                font=config.get_font("italic"),
                foreground=config.get_color("info")
            )
            self.label.pack(pady=5)
            self.parent.update()
    
    def hide(self):
        """隐藏加载指示器"""
        if self.label:
            self.label.destroy()
            self.label = None
    
    def update_message(self, message: str):
        """更新加载消息"""
        self.message = message
        if self.label:
            self.label.config(text=message)
            self.parent.update()


# 便捷的全局实例
msg = MessageHandler()
file_dialog = FileDialogHelper()
window_helper = WindowHelper()
tree_helper = TreeviewHelper()
form_helper = FormHelper()
date_helper = DateTimeHelper()
