# -*- coding: utf-8 -*-
"""
代码质量检查脚本
检查所有优化模块的代码质量和功能完整性
"""

import sys
import os
import importlib
import traceback
from typing import List, Dict, Any


class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self):
        self.results = {
            "syntax_check": {},
            "import_check": {},
            "functionality_check": {},
            "performance_check": {},
            "summary": {}
        }
    
    def check_syntax(self, file_path: str) -> bool:
        """检查语法错误"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            
            compile(code, file_path, 'exec')
            self.results["syntax_check"][file_path] = {"status": "PASS", "error": None}
            return True
            
        except SyntaxError as e:
            self.results["syntax_check"][file_path] = {
                "status": "FAIL", 
                "error": f"语法错误: {e.msg} (行 {e.lineno})"
            }
            return False
        except Exception as e:
            self.results["syntax_check"][file_path] = {
                "status": "FAIL", 
                "error": f"其他错误: {str(e)}"
            }
            return False
    
    def check_imports(self, module_name: str) -> bool:
        """检查模块导入"""
        try:
            module = importlib.import_module(module_name)
            self.results["import_check"][module_name] = {"status": "PASS", "error": None}
            return True
            
        except ImportError as e:
            self.results["import_check"][module_name] = {
                "status": "FAIL", 
                "error": f"导入错误: {str(e)}"
            }
            return False
        except Exception as e:
            self.results["import_check"][module_name] = {
                "status": "FAIL", 
                "error": f"其他错误: {str(e)}"
            }
            return False
    
    def check_config_functionality(self) -> bool:
        """检查配置模块功能"""
        try:
            from config import config, PAGE_SIZE, HISTORY_LIMIT, COLUMN_WIDTHS
            
            # 检查基本配置访问
            assert config.get_window_size("main") == "1400x800"
            assert config.get_column_width("ID") == 50
            assert config.get_font("default") == ("Arial", 10)
            assert config.get_color("primary") == "#0071e3"
            assert config.get_message("no_selection") == "请先选择要操作的记录"
            assert config.get_title("error") == "错误"
            
            # 检查向后兼容性
            assert PAGE_SIZE == 50
            assert HISTORY_LIMIT == 50
            assert isinstance(COLUMN_WIDTHS, dict)
            
            self.results["functionality_check"]["config"] = {"status": "PASS", "error": None}
            return True
            
        except Exception as e:
            self.results["functionality_check"]["config"] = {
                "status": "FAIL", 
                "error": f"功能测试失败: {str(e)}"
            }
            return False
    
    def check_database_functionality(self) -> bool:
        """检查数据库模块功能"""
        try:
            from database_manager import db_manager, EquipmentQueries, SalesQueries
            
            # 检查数据库管理器
            assert hasattr(db_manager, 'execute_query')
            assert hasattr(db_manager, 'execute_update')
            assert hasattr(db_manager, 'get_connection')
            
            # 检查查询类
            today = "2024-01-01"
            valid_query = EquipmentQueries.get_valid_equipment(today)
            assert "SELECT * FROM Equipment_ID" in valid_query
            assert "Effective_To" in valid_query
            
            expired_query = EquipmentQueries.get_expired_equipment(today)
            assert "SELECT * FROM Equipment_ID" in expired_query
            assert "Effective_To <" in expired_query
            
            empty_query = EquipmentQueries.find_empty_values("Layer", today)
            assert "Layer IS NULL" in empty_query
            
            duplicate_query = EquipmentQueries.find_duplicates()
            assert "GROUP_CONCAT" in duplicate_query
            
            search_query, params = EquipmentQueries.search_equipment("test", "location", "state", today)
            assert "Chair_Serial_No LIKE" in search_query
            assert len(params) == 4
            
            # 检查销售查询
            unmatched_query = SalesQueries.get_unmatched_sales()
            assert "Daily_Equipment_Sales" in unmatched_query
            
            abnormal_query = SalesQueries.get_abnormal_sales("2024", "01")
            assert "PAY%" in abnormal_query
            
            self.results["functionality_check"]["database_manager"] = {"status": "PASS", "error": None}
            return True
            
        except Exception as e:
            self.results["functionality_check"]["database_manager"] = {
                "status": "FAIL", 
                "error": f"功能测试失败: {str(e)}\n{traceback.format_exc()}"
            }
            return False
    
    def check_ui_utils_functionality(self) -> bool:
        """检查UI工具模块功能"""
        try:
            from ui_utils import (
                msg, file_dialog, window_helper, tree_helper, 
                form_helper, date_helper, MessageHandler, 
                FileDialogHelper, WindowHelper, TreeviewHelper,
                FormHelper, DateTimeHelper, LoadingIndicator
            )
            
            # 检查消息处理器
            assert hasattr(msg, 'show_info')
            assert hasattr(msg, 'show_error')
            assert hasattr(msg, 'ask_yes_no')
            
            # 检查文件对话框
            assert hasattr(file_dialog, 'open_excel_file')
            assert hasattr(file_dialog, 'save_excel_file')
            
            # 检查窗口辅助类
            assert hasattr(window_helper, 'create_dialog')
            assert hasattr(window_helper, 'center_window')
            
            # 检查表格辅助类
            assert hasattr(tree_helper, 'create_treeview')
            assert hasattr(tree_helper, 'clear_and_populate')
            
            # 检查表单辅助类
            assert hasattr(form_helper, 'create_form_field')
            assert hasattr(form_helper, 'create_button_group')
            
            # 检查日期辅助类
            current_date = date_helper.get_current_date()
            assert len(current_date) == 10  # YYYY-MM-DD
            assert "-" in current_date
            
            current_timestamp = date_helper.get_current_timestamp()
            assert len(current_timestamp) >= 19  # YYYY-MM-DD HH:MM:SS
            
            assert date_helper.is_valid_date("2024-01-01") == True
            assert date_helper.is_valid_date("invalid") == False
            assert date_helper.is_valid_date("") == True  # 空日期视为有效
            
            self.results["functionality_check"]["ui_utils"] = {"status": "PASS", "error": None}
            return True
            
        except Exception as e:
            self.results["functionality_check"]["ui_utils"] = {
                "status": "FAIL", 
                "error": f"功能测试失败: {str(e)}\n{traceback.format_exc()}"
            }
            return False
    
    def check_performance(self) -> bool:
        """检查性能相关功能"""
        try:
            import time
            from database_manager import db_manager
            from config import config
            
            # 测试配置访问性能
            start_time = time.time()
            for i in range(1000):
                width = config.get_column_width("ID")
            config_time = time.time() - start_time
            
            # 测试数据库连接性能
            start_time = time.time()
            for i in range(10):
                result = db_manager.execute_query("SELECT 1")
            db_time = time.time() - start_time
            
            self.results["performance_check"] = {
                "status": "PASS",
                "config_access_time": f"{config_time:.4f}秒 (1000次)",
                "database_query_time": f"{db_time:.4f}秒 (10次)",
                "error": None
            }
            return True
            
        except Exception as e:
            self.results["performance_check"] = {
                "status": "FAIL", 
                "error": f"性能测试失败: {str(e)}"
            }
            return False
    
    def run_all_checks(self) -> Dict[str, Any]:
        """运行所有检查"""
        print("🔍 开始代码质量检查...")
        
        # 语法检查
        print("\n1. 语法检查...")
        files_to_check = ["config.py", "database_manager.py", "ui_utils.py", "优化示例对比.py"]
        syntax_passed = 0
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                if self.check_syntax(file_path):
                    print(f"   ✅ {file_path}: 语法正确")
                    syntax_passed += 1
                else:
                    print(f"   ❌ {file_path}: {self.results['syntax_check'][file_path]['error']}")
            else:
                print(f"   ⚠️  {file_path}: 文件不存在")
        
        # 导入检查
        print("\n2. 导入检查...")
        modules_to_check = ["config", "database_manager", "ui_utils"]
        import_passed = 0
        
        for module in modules_to_check:
            if self.check_imports(module):
                print(f"   ✅ {module}: 导入成功")
                import_passed += 1
            else:
                print(f"   ❌ {module}: {self.results['import_check'][module]['error']}")
        
        # 功能检查
        print("\n3. 功能检查...")
        functionality_checks = [
            ("config", self.check_config_functionality),
            ("database_manager", self.check_database_functionality),
            ("ui_utils", self.check_ui_utils_functionality)
        ]
        
        functionality_passed = 0
        for name, check_func in functionality_checks:
            if check_func():
                print(f"   ✅ {name}: 功能正常")
                functionality_passed += 1
            else:
                print(f"   ❌ {name}: {self.results['functionality_check'][name]['error']}")
        
        # 性能检查
        print("\n4. 性能检查...")
        if self.check_performance():
            print(f"   ✅ 性能测试通过")
            print(f"      - 配置访问: {self.results['performance_check']['config_access_time']}")
            print(f"      - 数据库查询: {self.results['performance_check']['database_query_time']}")
        else:
            print(f"   ❌ 性能测试失败: {self.results['performance_check']['error']}")
        
        # 生成总结
        total_checks = len(files_to_check) + len(modules_to_check) + len(functionality_checks) + 1
        passed_checks = syntax_passed + import_passed + functionality_passed + (1 if self.results['performance_check']['status'] == 'PASS' else 0)
        
        self.results["summary"] = {
            "total_checks": total_checks,
            "passed_checks": passed_checks,
            "success_rate": f"{(passed_checks/total_checks)*100:.1f}%",
            "status": "PASS" if passed_checks == total_checks else "PARTIAL"
        }
        
        print(f"\n📊 检查总结:")
        print(f"   总检查项: {total_checks}")
        print(f"   通过项目: {passed_checks}")
        print(f"   成功率: {self.results['summary']['success_rate']}")
        
        if passed_checks == total_checks:
            print("   🎉 所有检查都通过了！代码质量优秀！")
        else:
            print("   ⚠️  部分检查未通过，请查看上述详细信息")
        
        return self.results


def main():
    """主函数"""
    checker = CodeQualityChecker()
    results = checker.run_all_checks()
    
    # 保存检查结果
    import json
    with open("代码质量检查结果.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细结果已保存到: 代码质量检查结果.json")
    
    return results["summary"]["status"] == "PASS"


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
