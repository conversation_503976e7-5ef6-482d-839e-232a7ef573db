import sqlite3
import pandas as pd
import datetime
import re
import os

# 数据库文件路径
DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"

def connect_db():
    """连接到数据库"""
    if not os.path.exists(DB_PATH):
        raise FileNotFoundError(f"数据库文件不存在: {DB_PATH}")
    return sqlite3.connect(DB_PATH)

def log_date_fix(platform, method, before_count, after_count, error=None):
    """记录日期修复日志"""
    conn = connect_db()
    cursor = conn.cursor()
    
    # 确保日志表存在
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS Date_Fix_Logs (
        ID INTEGER PRIMARY KEY AUTOINCREMENT,
        Platform TEXT,
        Method TEXT,
        Fix_Date TEXT,
        Before_Count INTEGER,
        After_Count INTEGER,
        Error TEXT
    )
    ''')
    
    fix_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    cursor.execute('''
    INSERT INTO Date_Fix_Logs (Platform, Method, Fix_Date, Before_Count, After_Count, Error)
    VALUES (?, ?, ?, ?, ?, ?)
    ''', (platform, method, fix_date, before_count, after_count, error))
    
    conn.commit()
    conn.close()

def get_problematic_dates(platform):
    """获取需要修复的日期格式"""
    conn = connect_db()
    
    # 查询不符合标准日期格式的记录
    query = f"""
    SELECT DISTINCT Order_time 
    FROM {platform}_Sales 
    WHERE Order_time IS NOT NULL
    ORDER BY Order_time
    """
    
    df = pd.read_sql_query(query, conn)
    conn.close()
    
    return df['Order_time'].tolist()

def method1_fix_standard_formats(platform):
    """方法1: 修复标准日期格式 (YYYY-MM-DD HH:MM:SS)"""
    print(f"\n执行方法1: 修复标准日期格式 ({platform})")
    conn = connect_db()
    cursor = conn.cursor()
    
    # 查找符合标准格式但可能包含多余时间信息的日期
    query = f"""
    SELECT COUNT(*) FROM {platform}_Sales
    WHERE Order_time LIKE '____-__-__ %'
    """
    
    cursor.execute(query)
    before_count = cursor.fetchone()[0]
    print(f"找到 {before_count} 条需要处理的记录")
    
    if before_count > 0:
        # 更新日期格式，只保留日期部分
        update_query = f"""
        UPDATE {platform}_Sales
        SET Order_time = SUBSTR(Order_time, 1, 10)
        WHERE Order_time LIKE '____-__-__ %'
        """
        
        try:
            cursor.execute(update_query)
            conn.commit()
            
            # 验证更新结果
            cursor.execute(query)
            after_count = cursor.fetchone()[0]
            fixed_count = before_count - after_count
            
            print(f"成功修复 {fixed_count} 条记录")
            log_date_fix(platform, "方法1-标准日期格式", before_count, after_count)
            
        except Exception as e:
            error_msg = str(e)
            print(f"错误: {error_msg}")
            log_date_fix(platform, "方法1-标准日期格式", before_count, before_count, error_msg)
    else:
        print("没有找到需要处理的记录")
    
    conn.close()
    return before_count > 0

def method2_fix_slash_formats(platform):
    """方法2: 修复斜杠分隔的日期格式 (MM/DD/YYYY)"""
    print(f"\n执行方法2: 修复斜杠分隔的日期格式 ({platform})")
    conn = connect_db()
    cursor = conn.cursor()
    
    # 查找斜杠分隔的日期格式
    query = f"""
    SELECT rowid, Order_time FROM {platform}_Sales
    WHERE Order_time LIKE '%/%/%'
    """
    
    cursor.execute(query)
    rows = cursor.fetchall()
    before_count = len(rows)
    print(f"找到 {before_count} 条需要处理的记录")
    
    if before_count > 0:
        fixed_count = 0
        
        for row_id, date_str in rows:
            try:
                # 尝试解析日期格式
                if re.match(r'\d{1,2}/\d{1,2}/\d{4}', date_str):
                    # MM/DD/YYYY 格式
                    date_parts = date_str.split('/')
                    if len(date_parts) >= 3:
                        month, day, year = int(date_parts[0]), int(date_parts[1]), int(date_parts[2].split()[0])
                        new_date = f"{year:04d}-{month:02d}-{day:02d}"
                        
                        # 更新记录
                        cursor.execute(f"""
                        UPDATE {platform}_Sales
                        SET Order_time = ?
                        WHERE rowid = ?
                        """, (new_date, row_id))
                        
                        fixed_count += 1
            except Exception as e:
                print(f"处理记录 {row_id} 时出错: {str(e)}")
        
        conn.commit()
        print(f"成功修复 {fixed_count} 条记录")
        log_date_fix(platform, "方法2-斜杠分隔日期", before_count, before_count - fixed_count)
    else:
        print("没有找到需要处理的记录")
    
    conn.close()
    return before_count > 0

def method3_fix_chinese_formats(platform):
    """方法3: 修复中文日期格式 (YYYY年MM月DD日)"""
    print(f"\n执行方法3: 修复中文日期格式 ({platform})")
    conn = connect_db()
    cursor = conn.cursor()
    
    # 查找中文日期格式
    query = f"""
    SELECT rowid, Order_time FROM {platform}_Sales
    WHERE Order_time LIKE '%年%月%日%'
    """
    
    cursor.execute(query)
    rows = cursor.fetchall()
    before_count = len(rows)
    print(f"找到 {before_count} 条需要处理的记录")
    
    if before_count > 0:
        fixed_count = 0
        
        for row_id, date_str in rows:
            try:
                # 提取年月日
                match = re.search(r'(\d{4})年(\d{1,2})月(\d{1,2})日', date_str)
                if match:
                    year, month, day = int(match.group(1)), int(match.group(2)), int(match.group(3))
                    new_date = f"{year:04d}-{month:02d}-{day:02d}"
                    
                    # 更新记录
                    cursor.execute(f"""
                    UPDATE {platform}_Sales
                    SET Order_time = ?
                    WHERE rowid = ?
                    """, (new_date, row_id))
                    
                    fixed_count += 1
            except Exception as e:
                print(f"处理记录 {row_id} 时出错: {str(e)}")
        
        conn.commit()
        print(f"成功修复 {fixed_count} 条记录")
        log_date_fix(platform, "方法3-中文日期格式", before_count, before_count - fixed_count)
    else:
        print("没有找到需要处理的记录")
    
    conn.close()
    return before_count > 0

def method4_fix_timestamp_formats(platform):
    """方法4: 修复时间戳格式"""
    print(f"\n执行方法4: 修复时间戳格式 ({platform})")
    conn = connect_db()
    cursor = conn.cursor()
    
    # 查找可能是时间戳的记录 (纯数字且长度大于8)
    query = f"""
    SELECT rowid, Order_time FROM {platform}_Sales
    WHERE Order_time GLOB '[0-9]*' AND length(Order_time) > 8
    """
    
    cursor.execute(query)
    rows = cursor.fetchall()
    before_count = len(rows)
    print(f"找到 {before_count} 条需要处理的记录")
    
    if before_count > 0:
        fixed_count = 0
        
        for row_id, date_str in rows:
            try:
                # 尝试将时间戳转换为日期
                if date_str.isdigit() and len(date_str) >= 10:
                    # 假设是Unix时间戳 (秒)
                    timestamp = int(date_str[:10])
                    date_obj = datetime.datetime.fromtimestamp(timestamp)
                    new_date = date_obj.strftime("%Y-%m-%d")
                    
                    # 更新记录
                    cursor.execute(f"""
                    UPDATE {platform}_Sales
                    SET Order_time = ?
                    WHERE rowid = ?
                    """, (new_date, row_id))
                    
                    fixed_count += 1
            except Exception as e:
                print(f"处理记录 {row_id} 时出错: {str(e)}")
        
        conn.commit()
        print(f"成功修复 {fixed_count} 条记录")
        log_date_fix(platform, "方法4-时间戳格式", before_count, before_count - fixed_count)
    else:
        print("没有找到需要处理的记录")
    
    conn.close()
    return before_count > 0

def method5_fix_custom_formats(platform):
    """方法5: 修复自定义日期格式"""
    print(f"\n执行方法5: 修复自定义日期格式 ({platform})")
    conn = connect_db()
    cursor = conn.cursor()
    
    # 获取所有不符合YYYY-MM-DD格式的日期
    query = f"""
    SELECT rowid, Order_time FROM {platform}_Sales
    WHERE Order_time IS NOT NULL 
    AND Order_time NOT LIKE '____-__-__'
    """
    
    cursor.execute(query)
    rows = cursor.fetchall()
    before_count = len(rows)
    print(f"找到 {before_count} 条需要处理的记录")
    
    if before_count > 0:
        fixed_count = 0
        
        # 定义常见的日期格式模式
        date_patterns = [
            # DD-MM-YYYY
            (r'(\d{1,2})-(\d{1,2})-(\d{4})', lambda m: f"{m.group(3)}-{int(m.group(2)):02d}-{int(m.group(1)):02d}"),
            # YYYY.MM.DD
            (r'(\d{4})\.(\d{1,2})\.(\d{1,2})', lambda m: f"{m.group(1)}-{int(m.group(2)):02d}-{int(m.group(3)):02d}"),
            # DD.MM.YYYY
            (r'(\d{1,2})\.(\d{1,2})\.(\d{4})', lambda m: f"{m.group(3)}-{int(m.group(2)):02d}-{int(m.group(1)):02d}"),
            # YYYYMMDD
            (r'^(\d{4})(\d{2})(\d{2})$', lambda m: f"{m.group(1)}-{m.group(2)}-{m.group(3)}"),
            # DDMMYYYY
            (r'^(\d{2})(\d{2})(\d{4})$', lambda m: f"{m.group(3)}-{m.group(2)}-{m.group(1)}")
        ]
        
        for row_id, date_str in rows:
            try:
                fixed = False
                
                # 尝试各种模式
                for pattern, formatter in date_patterns:
                    match = re.search(pattern, date_str)
                    if match:
                        new_date = formatter(match)
                        
                        # 验证日期有效性
                        try:
                            datetime.datetime.strptime(new_date, "%Y-%m-%d")
                            
                            # 更新记录
                            cursor.execute(f"""
                            UPDATE {platform}_Sales
                            SET Order_time = ?
                            WHERE rowid = ?
                            """, (new_date, row_id))
                            
                            fixed_count += 1
                            fixed = True
                            break
                        except ValueError:
                            continue
                
                if not fixed:
                    print(f"无法识别的日期格式: {date_str}")
            except Exception as e:
                print(f"处理记录 {row_id} 时出错: {str(e)}")
        
        conn.commit()
        print(f"成功修复 {fixed_count} 条记录")
        log_date_fix(platform, "方法5-自定义日期格式", before_count, before_count - fixed_count)
    else:
        print("没有找到需要处理的记录")
    
    conn.close()
    return before_count > 0

def method6_fix_excel_serial_dates(platform):
    """方法6: 修复Excel日期序列号格式"""
    print(f"\n执行方法6: 修复Excel日期序列号格式 ({platform})")
    conn = connect_db()
    cursor = conn.cursor()
    
    # 查找可能是Excel日期序列号的记录 (纯数字且长度为5位)
    query = f"""
    SELECT rowid, Order_time FROM {platform}_Sales
    WHERE Order_time GLOB '[0-9]*' 
    AND length(Order_time) = 5
    AND Order_time NOT LIKE '____-__-__'
    """
    
    cursor.execute(query)
    rows = cursor.fetchall()
    before_count = len(rows)
    print(f"找到 {before_count} 条需要处理的记录")
    
    if before_count > 0:
        fixed_count = 0
        
        for row_id, date_str in rows:
            try:
                # 尝试将Excel日期序列号转换为日期
                # Excel日期序列号从1900-01-01开始计算，序列号1对应1900-01-01
                # 但Python中的datetime不能直接处理，需要转换
                if date_str.isdigit():
                    excel_serial = int(date_str)
                    
                    # Excel和Python日期计算有差异，需要调整
                    # Excel错误地认为1900是闰年，所以对于大于等于60的序列号，需要减1
                    if excel_serial >= 60:
                        excel_serial -= 1
                    
                    # 计算日期：从1899-12-30开始加上序列号天数
                    base_date = datetime.datetime(1899, 12, 30)
                    date_obj = base_date + datetime.timedelta(days=excel_serial)
                    new_date = date_obj.strftime("%Y-%m-%d")
                    
                    # 验证日期有效性（排除过早或过晚的日期）
                    year = int(new_date.split('-')[0])
                    if 1950 <= year <= 2100:  # 设置合理的年份范围
                        # 更新记录
                        cursor.execute(f"""
                        UPDATE {platform}_Sales
                        SET Order_time = ?
                        WHERE rowid = ?
                        """, (new_date, row_id))
                        
                        fixed_count += 1
                    else:
                        print(f"日期超出合理范围: {date_str} -> {new_date}")
            except Exception as e:
                print(f"处理记录 {row_id} 时出错: {str(e)}")
        
        conn.commit()
        print(f"成功修复 {fixed_count} 条记录")
        log_date_fix(platform, "方法6-Excel日期序列号", before_count, before_count - fixed_count)
    else:
        print("没有找到需要处理的记录")
    
    conn.close()
    return before_count > 0

def method7_force_fix_remaining(platform):
    """方法7: 强制修复剩余的日期格式问题"""
    print(f"\n执行方法7: 强制修复剩余的日期格式问题 ({platform})")
    conn = connect_db()
    cursor = conn.cursor()
    
    # 获取所有不符合YYYY-MM-DD格式的日期
    query = f"""
    SELECT Order_time, COUNT(*) as count
    FROM {platform}_Sales
    WHERE Order_time IS NOT NULL 
    AND Order_time NOT LIKE '____-__-__'
    GROUP BY Order_time
    ORDER BY count DESC
    """
    
    df = pd.read_sql_query(query, conn)
    before_count = df['count'].sum() if not df.empty else 0
    print(f"找到 {before_count} 条需要处理的记录")
    
    if before_count > 0:
        fixed_count = 0
        
        # 获取最近的有效日期作为默认值
        cursor.execute(f"""
        SELECT Order_time FROM {platform}_Sales
        WHERE Order_time LIKE '____-__-__'
        ORDER BY Order_time DESC
        LIMIT 1
        """)
        
        default_date_row = cursor.fetchone()
        default_date = default_date_row[0] if default_date_row else "2025-01-01"
        
        print(f"将使用默认日期: {default_date}")
        
        # 更新所有不符合格式的日期
        update_query = f"""
        UPDATE {platform}_Sales
        SET Order_time = ?
        WHERE Order_time IS NOT NULL 
        AND Order_time NOT LIKE '____-__-__'
        """
        
        try:
            cursor.execute(update_query, (default_date,))
            conn.commit()
            
            # 验证更新结果
            cursor.execute(query)
            df_after = pd.read_sql_query(query, conn)
            after_count = df_after['count'].sum() if not df_after.empty else 0
            fixed_count = before_count - after_count
            
            print(f"成功修复 {fixed_count} 条记录")
            log_date_fix(platform, "方法7-强制修复", before_count, after_count)
            
        except Exception as e:
            error_msg = str(e)
            print(f"错误: {error_msg}")
            log_date_fix(platform, "方法7-强制修复", before_count, before_count, error_msg)
    else:
        print("没有找到需要处理的记录")
    
    conn.close()
    return before_count > 0

def method8_fix_by_comparing_dates(platform):
    """方法8: 通过对比Payment_date和Import_Date来修复Order_time"""
    print(f"\n执行方法8: 通过对比其他日期字段修复Order_time ({platform})")
    conn = connect_db()
    cursor = conn.cursor()
    
    # 查询所有记录的Order_time, Payment_date和Import_Date
    query = f"""
    SELECT rowid, Order_time, Payment_date, Import_Date 
    FROM {platform}_Sales
    """
    
    cursor.execute(query)
    rows = cursor.fetchall()
    total_count = len(rows)
    print(f"找到 {total_count} 条记录进行检查")
    
    if total_count > 0:
        fixed_count = 0
        
        for row in rows:
            row_id, order_time, payment_date, import_date = row
            
            # 提取日期部分（不含时间）
            order_date = order_time.split()[0] if order_time and ' ' in order_time else order_time
            payment_short_date = payment_date.split()[0] if payment_date and ' ' in payment_date else payment_date
            import_short_date = import_date.split()[0] if import_date and ' ' in import_date else import_date
            
            # 检查是否需要修复
            need_fix = False
            
            # 检查Order_time是否为2025年的数据
            if order_date and order_date.startswith("2025-"):
                need_fix = True
            
            # 检查Import_Date是否有效且与Order_time不同
            valid_import_date = import_short_date and re.match(r'\d{4}-\d{2}-\d{2}', import_short_date)
            dates_different = order_date != import_short_date
            
            if need_fix and valid_import_date and dates_different:
                try:
                    # 验证日期有效性
                    datetime.datetime.strptime(import_short_date, "%Y-%m-%d")
                    
                    # 更新记录
                    cursor.execute(f"""
                    UPDATE {platform}_Sales
                    SET Order_time = ?
                    WHERE rowid = ?
                    """, (import_short_date, row_id))
                    
                    fixed_count += 1
                    print(f"修复记录 {row_id}: {order_date} -> {import_short_date}")
                except ValueError:
                    print(f"无效的Import_Date格式: {import_date}")
            elif need_fix and payment_short_date and re.match(r'\d{4}-\d{2}-\d{2}', payment_short_date) and payment_short_date != order_date:
                # 如果Import_Date无效或相同但Payment_date有效且不同，使用Payment_date
                try:
                    # 验证日期有效性
                    datetime.datetime.strptime(payment_short_date, "%Y-%m-%d")
                    
                    # 更新记录
                    cursor.execute(f"""
                    UPDATE {platform}_Sales
                    SET Order_time = ?
                    WHERE rowid = ?
                    """, (payment_short_date, row_id))
                    
                    fixed_count += 1
                    print(f"修复记录 {row_id}: {order_date} -> {payment_short_date} (使用Payment_date)")
                except ValueError:
                    print(f"无效的Payment_date格式: {payment_date}")
        
        conn.commit()
        print(f"成功修复 {fixed_count} 条记录")
        log_date_fix(platform, "方法8-对比日期字段", total_count, total_count - fixed_count)
    else:
        print("没有找到需要处理的记录")
    
    conn.close()
    return fixed_count > 0

def method9_fix_2025_dates(platform):
    """方法9: 修复2025年的错误日期数据"""
    print(f"\n执行方法9: 修复2025年的错误日期数据 ({platform})")
    conn = connect_db()
    cursor = conn.cursor()
    
    # 查询所有2025年的记录
    query = f"""
    SELECT rowid, Order_time, Payment_date, Import_Date 
    FROM {platform}_Sales
    WHERE Order_time LIKE '2025-%'
    """
    
    cursor.execute(query)
    rows = cursor.fetchall()
    before_count = len(rows)
    print(f"找到 {before_count} 条2025年的记录")
    
    if before_count > 0:
        fixed_count = 0
        
        for row in rows:
            row_id, order_time, payment_date, import_date = row
            
            # 尝试使用Import_Date修复
            if import_date and import_date.strip():
                import_short_date = import_date.split()[0] if ' ' in import_date else import_date
                
                try:
                    # 验证日期有效性
                    datetime.datetime.strptime(import_short_date, "%Y-%m-%d")
                    
                    # 更新记录
                    cursor.execute(f"""
                    UPDATE {platform}_Sales
                    SET Order_time = ?
                    WHERE rowid = ?
                    """, (import_short_date, row_id))
                    
                    fixed_count += 1
                    print(f"修复2025年记录 {row_id}: {order_time} -> {import_short_date}")
                    continue
                except (ValueError, IndexError):
                    print(f"无效的Import_Date格式: {import_date}")
            
            # 如果Import_Date无效，尝试使用Payment_date
            if payment_date and payment_date.strip():
                payment_short_date = payment_date.split()[0] if ' ' in payment_date else payment_date
                
                try:
                    # 验证日期有效性
                    datetime.datetime.strptime(payment_short_date, "%Y-%m-%d")
                    
                    # 更新记录
                    cursor.execute(f"""
                    UPDATE {platform}_Sales
                    SET Order_time = ?
                    WHERE rowid = ?
                    """, (payment_short_date, row_id))
                    
                    fixed_count += 1
                    print(f"修复2025年记录 {row_id}: {order_time} -> {payment_short_date} (使用Payment_date)")
                    continue
                except (ValueError, IndexError):
                    print(f"无效的Payment_date格式: {payment_date}")
            
            # 如果都无效，使用默认日期
            # 查找最近的非2025年的有效日期
            cursor.execute(f"""
            SELECT Order_time FROM {platform}_Sales
            WHERE Order_time NOT LIKE '2025-%'
            AND Order_time LIKE '____-__-__'
            ORDER BY Order_time DESC
            LIMIT 1
            """)
            
            default_date_row = cursor.fetchone()
            
            if default_date_row:
                default_date = default_date_row[0]
                
                # 更新记录
                cursor.execute(f"""
                UPDATE {platform}_Sales
                SET Order_time = ?
                WHERE rowid = ?
                """, (default_date, row_id))
                
                fixed_count += 1
                print(f"修复2025年记录 {row_id}: {order_time} -> {default_date} (使用默认日期)")
        
        conn.commit()
        
        # 验证更新结果
        cursor.execute(f"""
        SELECT COUNT(*) FROM {platform}_Sales
        WHERE Order_time LIKE '2025-%'
        """)
        after_count = cursor.fetchone()[0]
        
        print(f"成功修复 {fixed_count} 条记录，剩余 {after_count} 条2025年记录")
        log_date_fix(platform, "方法9-修复2025年日期", before_count, after_count)
    else:
        print("没有找到需要处理的记录")
    
    conn.close()
    return before_count > 0

def sort_database_by_date(platform):
    """按日期对数据库进行排序"""
    print(f"\n对 {platform} 平台的数据按日期排序")
    conn = connect_db()
    cursor = conn.cursor()
    
    try:
        # 创建临时表
        cursor.execute(f"""
        CREATE TABLE temp_{platform}_Sales AS
        SELECT * FROM {platform}_Sales
        ORDER BY Order_time
        """)
        
        # 删除原表
        cursor.execute(f"DROP TABLE {platform}_Sales")
        
        # 重命名临时表
        cursor.execute(f"ALTER TABLE temp_{platform}_Sales RENAME TO {platform}_Sales")
        
        conn.commit()
        print(f"{platform} 平台的数据已按日期排序")
        return True
    except Exception as e:
        error_msg = str(e)
        print(f"排序数据时出错: {error_msg}")
        conn.rollback()
        return False
    finally:
        conn.close()

def check_remaining_issues(platform):
    """检查剩余的日期格式问题"""
    print(f"\n检查 {platform} 平台剩余的日期格式问题")
    conn = connect_db()
    
    # 查询不符合YYYY-MM-DD格式的记录
    query = f"""
    SELECT Order_time, COUNT(*) as count
    FROM {platform}_Sales
    WHERE Order_time IS NOT NULL 
    AND Order_time NOT LIKE '____-__-__'
    GROUP BY Order_time
    ORDER BY count DESC
    """
    
    df = pd.read_sql_query(query, conn)
    
    # 查询2025年的记录
    query_2025 = f"""
    SELECT COUNT(*) as count
    FROM {platform}_Sales
    WHERE Order_time LIKE '2025-%'
    """
    
    df_2025 = pd.read_sql_query(query_2025, conn)
    count_2025 = df_2025['count'].iloc[0] if not df_2025.empty else 0
    
    conn.close()
    
    if df.empty and count_2025 == 0:
        print(f"恭喜！{platform} 平台所有日期已统一为标准格式，且没有2025年的错误数据")
        return True
    else:
        if not df.empty:
            print(f"仍有 {df['count'].sum()} 条记录的日期格式不标准:")
            for _, row in df.iterrows():
                print(f"  - {row['Order_time']}: {row['count']} 条记录")
        
        if count_2025 > 0:
            print(f"仍有 {count_2025} 条记录是2025年的错误数据")
        
        return False

def main():
    print("===== 开始统一日期格式 =====")
    
    # 检查数据库是否存在
    if not os.path.exists(DB_PATH):
        print(f"错误: 数据库文件不存在: {DB_PATH}")
        return
    
    platforms = ["IOT", "ZERO"]
    
    for platform in platforms:
        print(f"\n处理 {platform} 平台...")
        
        # 获取处理前的问题日期样本
        problem_dates = get_problematic_dates(platform)
        if problem_dates:
            print(f"处理前的日期格式样本: {problem_dates[:10]}")
        
        # 设置最大尝试次数
        max_attempts = 3
        attempt = 1
        all_fixed = False
        
        while not all_fixed and attempt <= max_attempts:
            print(f"\n第 {attempt} 次尝试修复 {platform} 平台的日期...")
            
            # 应用基本方法修复日期格式
            method1_fixed = method1_fix_standard_formats(platform)
            method2_fixed = method2_fix_slash_formats(platform)
            method3_fixed = method3_fix_chinese_formats(platform)
            method4_fixed = method4_fix_timestamp_formats(platform)
            method5_fixed = method5_fix_custom_formats(platform)
            method6_fixed = method6_fix_excel_serial_dates(platform)
            
            # 应用新方法：对比其他日期字段
            method8_fixed = method8_fix_by_comparing_dates(platform)
            
            # 应用新方法：修复2025年的错误日期
            method9_fixed = method9_fix_2025_dates(platform)
            
            # 检查是否还有问题
            all_fixed = check_remaining_issues(platform)
            
            if all_fixed:
                print(f"{platform} 平台日期格式统一完成！")
                # 按日期排序
                sort_database_by_date(platform)
            else:
                print(f"第 {attempt} 次尝试后，{platform} 平台仍有日期格式问题。")
                if attempt < max_attempts:
                    print("将进行下一次尝试...")
                else:
                    print("已达到最大尝试次数，将使用强制修复方法。")
                    # 使用强制修复方法
                    method7_force_fix_remaining(platform)
                    # 最后一次尝试对比其他日期字段
                    method8_fix_by_comparing_dates(platform)
                    # 最后一次尝试修复2025年的错误日期
                    method9_fix_2025_dates(platform)
                    # 按日期排序
                    sort_database_by_date(platform)
                    
                    all_fixed = check_remaining_issues(platform)
                    if all_fixed:
                        print(f"{platform} 平台日期格式统一完成！")
                    else:
                        print(f"强制修复后，{platform} 平台仍有日期格式问题，请手动检查。")
            
            attempt += 1
    
    print("\n===== 日期格式统一处理完成 =====")

if __name__ == "__main__":
    main()