#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试Excel文件，用于测试导入功能
"""

import pandas as pd

# 根据您的数据创建测试Excel
data = {
    'STATE': ['PENANG'],
    'Location': ['PRANGIN MALL'],
    'Quantity': [''],  # 空值
    'Chair_Serial_No': ['*********'],
    'Sim_Card_Model': ['Celcom'],
    'SIMCARDID': ['896019210381540476'],
    'Sim_Card_No': ['015-92270267'],
    'Layer': ['2'],
    'Company': [''],  # 空值
    'DATE': ['30/05/2024   01/08/2024 13/06/2030'],
    'Rental': ['280.00']
}

df = pd.DataFrame(data)

# 保存为Excel文件
excel_path = "C:/Users/<USER>/Desktop/Day Report/test_import.xlsx"
df.to_excel(excel_path, index=False)

print(f"✅ 测试Excel文件已创建: {excel_path}")
print(f"📊 数据内容:")
print(df)
print(f"\n📋 列名: {list(df.columns)}")
print(f"🔢 数据形状: {df.shape}")

# 验证文件
try:
    test_df = pd.read_excel(excel_path)
    print(f"\n✅ 文件验证成功，读取到 {len(test_df)} 行数据")
    print("详细数据:")
    for i, (_, row) in enumerate(test_df.iterrows()):
        print(f"  行{i+1}:")
        for col, val in row.items():
            print(f"    {col}: '{val}' (类型: {type(val).__name__})")
except Exception as e:
    print(f"❌ 文件验证失败: {e}")
