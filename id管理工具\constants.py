# -*- coding: utf-8 -*-
"""
常量定义文件
集中管理所有常量，避免硬编码，提高代码可维护性
"""

import os
from typing import Dict, List, Tuple


class DatabaseConstants:
    """数据库相关常量"""
    
    # 默认数据库路径
    DEFAULT_DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
    
    # 数据库配置
    CONNECTION_TIMEOUT = 30.0
    CONNECTION_POOL_SIZE = 5
    QUERY_TIMEOUT = 60.0
    
    # 表名
    EQUIPMENT_TABLE = "Equipment_ID"
    LOGS_TABLE = "Logs"
    SALES_TABLE = "Daily_Equipment_Sales"
    
    # 数据库字段
    EQUIPMENT_COLUMNS = [
        "ID", "STATE", "Location", "Quantity", "Chair_Serial_No",
        "Sim_Card_Model", "Sim_Card_No", "Layer", "Company",
        "Effective_From", "Effective_To", "Rental", "SIMCARDID",
        "Import_Date", "Last_Updated", "CurrentFlag", "DATE"
    ]
    
    # 必填字段
    REQUIRED_FIELDS = ["STATE", "Chair_Serial_No"]
    
    # 唯一字段
    UNIQUE_FIELDS = ["Chair_Serial_No"]
    
    # 日期字段
    DATE_FIELDS = ["Effective_From", "Effective_To", "Import_Date", "Last_Updated", "DATE"]


class UIConstants:
    """界面相关常量"""
    
    # 窗口尺寸
    MAIN_WINDOW_SIZE = "1400x800"
    EDIT_WINDOW_SIZE = "500x650"
    IMPORT_WINDOW_SIZE = "600x400"
    SEARCH_WINDOW_SIZE = "800x600"
    
    # 分页设置
    DEFAULT_PAGE_SIZE = 50
    MAX_PAGE_SIZE = 200
    MIN_PAGE_SIZE = 10
    
    # 历史记录限制
    OPERATION_HISTORY_LIMIT = 50
    SEARCH_HISTORY_LIMIT = 20
    
    # 列宽设置
    COLUMN_WIDTHS = {
        "ID": 50,
        "STATE": 80,
        "Location": 120,
        "Quantity": 60,
        "Chair_Serial_No": 150,
        "Sim_Card_Model": 120,
        "Sim_Card_No": 120,
        "Layer": 80,
        "Company": 150,
        "Effective_From": 100,
        "Effective_To": 100,
        "Rental": 80,
        "SIMCARDID": 120,
        "Import_Date": 100,
        "Last_Updated": 120,
        "CurrentFlag": 80,
        "DATE": 100
    }
    
    # 友好的列标题
    COLUMN_HEADERS = {
        "ID": "ID",
        "STATE": "状态",
        "Location": "位置",
        "Quantity": "数量",
        "Chair_Serial_No": "椅子序列号",
        "Sim_Card_Model": "SIM卡型号",
        "Sim_Card_No": "SIM卡号",
        "Layer": "层级",
        "Company": "公司",
        "Effective_From": "生效日期",
        "Effective_To": "失效日期",
        "Rental": "租金",
        "SIMCARDID": "SIM卡ID",
        "Import_Date": "导入日期",
        "Last_Updated": "最后更新",
        "CurrentFlag": "当前标志",
        "DATE": "日期"
    }
    
    # 颜色主题
    COLORS = {
        'primary': '#2E86AB',
        'secondary': '#A23B72',
        'success': '#F18F01',
        'danger': '#C73E1D',
        'warning': '#F4A261',
        'info': '#264653',
        'light': '#F8F9FA',
        'dark': '#212529',
        'surface': '#FFFFFF',
        'background': '#F5F5F5',
        'text': '#333333',
        'border': '#DEE2E6'
    }


class FileConstants:
    """文件相关常量"""
    
    # 配置文件
    CONFIG_FILE = "db_config.ini"
    
    # 支持的文件格式
    EXCEL_EXTENSIONS = [".xlsx", ".xls"]
    CSV_EXTENSIONS = [".csv"]
    
    # 文件类型过滤器
    EXCEL_FILE_TYPES = [("Excel文件", "*.xlsx *.xls")]
    CSV_FILE_TYPES = [("CSV文件", "*.csv")]
    ALL_FILE_TYPES = [("所有文件", "*.*")]
    
    # 导入导出设置
    MAX_IMPORT_ROWS = 10000
    BATCH_SIZE = 1000
    
    # Excel必需列
    REQUIRED_EXCEL_COLUMNS = {"STATE", "Chair_Serial_No"}


class ValidationConstants:
    """验证相关常量"""
    
    # 日期格式
    DATE_FORMAT = "%Y-%m-%d"
    DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
    
    # 日期格式模式
    DATE_PATTERNS = [
        r"^\d{4}-\d{1,2}-\d{1,2}$",  # YYYY-MM-DD
        r"^\d{4}/\d{1,2}/\d{1,2}$",  # YYYY/MM/DD
        r"^\d{1,2}/\d{1,2}/\d{4}$",  # DD/MM/YYYY
        r"^\d{1,2}-\d{1,2}-\d{4}$",  # DD-MM-YYYY
    ]
    
    # 字段长度限制
    FIELD_MAX_LENGTHS = {
        "STATE": 50,
        "Location": 100,
        "Chair_Serial_No": 50,
        "Sim_Card_Model": 50,
        "Sim_Card_No": 50,
        "Layer": 20,
        "Company": 100,
        "SIMCARDID": 50,
        "DATE": 50
    }
    
    # 数值字段范围
    NUMERIC_RANGES = {
        "Quantity": (0, 9999),
        "Rental": (0, 999999.99)
    }


class MessageConstants:
    """消息常量"""
    
    # 成功消息
    SUCCESS_MESSAGES = {
        "save": "数据保存成功",
        "delete": "删除成功",
        "import": "导入成功",
        "export": "导出成功",
        "update": "更新成功"
    }
    
    # 错误消息
    ERROR_MESSAGES = {
        "db_connection": "数据库连接失败，请检查数据库文件是否存在",
        "file_not_found": "文件不存在或无法访问",
        "invalid_format": "文件格式不正确",
        "duplicate_data": "数据重复，请检查序列号是否已存在",
        "missing_required": "缺少必填字段",
        "invalid_date": "日期格式不正确，应为YYYY-MM-DD",
        "permission_denied": "权限不足，无法执行此操作"
    }
    
    # 确认消息
    CONFIRM_MESSAGES = {
        "delete": "确定要删除选中的记录吗？此操作可以撤销。",
        "batch_delete": "确定要批量删除选中的 {count} 条记录吗？此操作可以撤销。",
        "overwrite": "文件已存在，是否覆盖？",
        "clear_data": "确定要清空所有数据吗？此操作不可撤销。"
    }


class OperationConstants:
    """操作相关常量"""
    
    # 操作类型
    OPERATION_TYPES = {
        "ADD": "添加",
        "EDIT": "编辑", 
        "DELETE": "删除",
        "BATCH_EDIT": "批量编辑",
        "BATCH_DELETE": "批量删除",
        "IMPORT": "导入",
        "EXPORT": "导出"
    }
    
    # 快捷键
    SHORTCUTS = {
        "save": "Ctrl+S",
        "undo": "Ctrl+Z",
        "batch_edit": "Ctrl+E",
        "search": "Ctrl+F",
        "refresh": "F5",
        "new": "Ctrl+N",
        "delete": "Delete",
        "copy": "Ctrl+C",
        "paste": "Ctrl+V"
    }
    
    # 异步操作阈值
    ASYNC_THRESHOLD = 100  # 超过100条记录使用异步处理
    
    # 缓存设置
    CACHE_TIMEOUT = 300  # 5分钟缓存超时
    MAX_CACHE_SIZE = 1000  # 最大缓存条目数


class LogConstants:
    """日志相关常量"""
    
    # 日志级别
    LOG_LEVELS = {
        "DEBUG": 10,
        "INFO": 20,
        "WARNING": 30,
        "ERROR": 40,
        "CRITICAL": 50
    }
    
    # 日志格式
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
    
    # 日志文件设置
    LOG_FILE = "equipment_manager.log"
    MAX_LOG_SIZE = 10 * 1024 * 1024  # 10MB
    BACKUP_COUNT = 5


# 便捷访问类
class Config:
    """配置访问类"""
    
    DB = DatabaseConstants
    UI = UIConstants
    FILE = FileConstants
    VALIDATION = ValidationConstants
    MESSAGE = MessageConstants
    OPERATION = OperationConstants
    LOG = LogConstants
    
    @classmethod
    def get_db_path(cls) -> str:
        """获取数据库路径"""
        return cls.DB.DEFAULT_DB_PATH
    
    @classmethod
    def get_window_size(cls, window_type: str = "main") -> str:
        """获取窗口尺寸"""
        size_map = {
            "main": cls.UI.MAIN_WINDOW_SIZE,
            "edit": cls.UI.EDIT_WINDOW_SIZE,
            "import": cls.UI.IMPORT_WINDOW_SIZE,
            "search": cls.UI.SEARCH_WINDOW_SIZE
        }
        return size_map.get(window_type, cls.UI.MAIN_WINDOW_SIZE)
    
    @classmethod
    def get_column_width(cls, column: str) -> int:
        """获取列宽"""
        return cls.UI.COLUMN_WIDTHS.get(column, 100)
    
    @classmethod
    def get_column_header(cls, column: str) -> str:
        """获取列标题"""
        return cls.UI.COLUMN_HEADERS.get(column, column)
    
    @classmethod
    def is_required_field(cls, field: str) -> bool:
        """检查是否为必填字段"""
        return field in cls.DB.REQUIRED_FIELDS
    
    @classmethod
    def is_date_field(cls, field: str) -> bool:
        """检查是否为日期字段"""
        return field in cls.DB.DATE_FIELDS
