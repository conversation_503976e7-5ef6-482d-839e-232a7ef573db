# -*- coding: utf-8 -*-
"""
数据库连接管理器
统一管理数据库连接，避免重复创建连接，提高性能
"""

import sqlite3
import threading
import time
import os
from typing import Optional, Tuple, List, Any
from contextlib import contextmanager


class DatabaseConnectionManager:
    """数据库连接管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, db_path: str = None):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, db_path: str = None):
        if hasattr(self, '_initialized'):
            return
        
        self.db_path = db_path
        self._connection = None
        self._lock = threading.Lock()
        self._last_activity = time.time()
        self._timeout = 300  # 5分钟超时
        self._initialized = True
    
    def set_db_path(self, db_path: str):
        """设置数据库路径"""
        with self._lock:
            if self.db_path != db_path:
                self.close_connection()
                self.db_path = db_path
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        with self._lock:
            current_time = time.time()
            
            # 检查连接是否超时
            if (self._connection and 
                current_time - self._last_activity > self._timeout):
                self.close_connection()
            
            # 创建新连接或重用现有连接
            if self._connection is None:
                if not self.db_path:
                    raise ValueError("数据库路径未设置")
                
                print(f"🔄 正在连接数据库: {self.db_path}")
                self._connection = sqlite3.connect(
                    self.db_path,
                    check_same_thread=False,
                    timeout=5.0  # 减少超时时间
                )
                print("✅ 数据库连接已建立")

                # 启用外键约束（快速操作）
                try:
                    self._connection.execute("PRAGMA foreign_keys = ON")
                    print("✅ 外键约束已启用")
                except Exception as e:
                    print(f"⚠️ 外键约束设置失败: {e}")

                # 跳过WAL模式设置，避免可能的阻塞
                # self._connection.execute("PRAGMA journal_mode = WAL")
                print(f"✅ 数据库初始化完成: {self.db_path}")
            
            self._last_activity = current_time
            return self._connection
    
    @contextmanager
    def get_cursor(self):
        """获取游标的上下文管理器"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            yield cursor
        finally:
            cursor.close()
    
    def execute_query(self, query: str, params: Tuple = None, fetch_all: bool = True) -> List[Any]:
        """执行查询并返回结果"""
        with self.get_cursor() as cursor:
            cursor.execute(query, params or ())
            if fetch_all:
                return cursor.fetchall()
            else:
                return cursor.fetchone()
    
    def execute_update(self, query: str, params: Tuple = None) -> int:
        """执行更新操作并返回影响的行数"""
        conn = self.get_connection()
        with self.get_cursor() as cursor:
            cursor.execute(query, params or ())
            conn.commit()
            return cursor.rowcount
    
    def execute_batch(self, queries: List[Tuple[str, Tuple]]) -> bool:
        """批量执行操作"""
        conn = self.get_connection()
        try:
            with self.get_cursor() as cursor:
                cursor.execute("BEGIN TRANSACTION")
                for query, params in queries:
                    cursor.execute(query, params or ())
                conn.commit()
                return True
        except Exception as e:
            conn.rollback()
            print(f"❌ 批量操作失败: {e}")
            raise
    
    def execute_with_transaction(self, operation_func, *args, **kwargs):
        """在事务中执行操作"""
        conn = self.get_connection()
        try:
            conn.execute("BEGIN TRANSACTION")
            result = operation_func(conn, *args, **kwargs)
            conn.commit()
            return result
        except Exception as e:
            conn.rollback()
            print(f"❌ 事务执行失败: {e}")
            raise
    
    def close_connection(self):
        """关闭数据库连接"""
        with self._lock:
            if self._connection:
                try:
                    self._connection.close()
                    print("🔒 数据库连接已关闭")
                except Exception as e:
                    print(f"⚠️ 关闭数据库连接时出错: {e}")
                finally:
                    self._connection = None
    
    def test_connection(self, timeout: float = 3.0) -> bool:
        """测试数据库连接（简化版本）"""
        try:
            print("🔄 测试数据库连接...")

            # 简单的连接测试，不使用复杂的超时机制
            conn = sqlite3.connect(self.db_path, timeout=timeout)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            conn.close()

            print(f"✅ 数据库连接测试成功: {result}")
            return True

        except Exception as e:
            print(f"❌ 数据库连接测试失败: {e}")
            return False
    
    def get_table_info(self, table_name: str) -> List[Tuple]:
        """获取表结构信息"""
        return self.execute_query(f"PRAGMA table_info({table_name})")
    
    def get_table_count(self, table_name: str, where_clause: str = "") -> int:
        """获取表记录数"""
        query = f"SELECT COUNT(*) FROM {table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
        result = self.execute_query(query, fetch_all=False)
        return result[0] if result else 0
    
    def __del__(self):
        """析构函数，确保连接被关闭"""
        self.close_connection()


# 创建全局数据库管理器实例
db_manager = DatabaseConnectionManager()


class DatabaseError(Exception):
    """数据库操作异常"""
    pass


class DatabaseValidator:
    """数据库验证器"""
    
    @staticmethod
    def validate_table_exists(table_name: str) -> bool:
        """验证表是否存在"""
        try:
            query = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
            result = db_manager.execute_query(query, (table_name,), fetch_all=False)
            return result is not None
        except Exception:
            return False
    
    @staticmethod
    def validate_column_exists(table_name: str, column_name: str) -> bool:
        """验证列是否存在"""
        try:
            table_info = db_manager.get_table_info(table_name)
            column_names = [col[1] for col in table_info]
            return column_name in column_names
        except Exception:
            return False
    
    @staticmethod
    def validate_data_integrity(table_name: str) -> dict:
        """验证数据完整性"""
        try:
            # 检查空值
            null_counts = {}
            table_info = db_manager.get_table_info(table_name)
            
            for col_info in table_info:
                col_name = col_info[1]
                query = f"SELECT COUNT(*) FROM {table_name} WHERE {col_name} IS NULL"
                count = db_manager.execute_query(query, fetch_all=False)[0]
                if count > 0:
                    null_counts[col_name] = count
            
            return {
                'null_counts': null_counts,
                'total_records': db_manager.get_table_count(table_name)
            }
        except Exception as e:
            return {'error': str(e)}


# 便捷函数
def init_database_manager(db_path: str, timeout: float = 3.0):
    """初始化数据库管理器（非阻塞版本）"""
    try:
        print(f"🔄 正在初始化数据库管理器: {db_path}")

        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            print(f"⚠️ 数据库文件不存在: {db_path}")
            print("   将在首次使用时创建数据库")

        # 设置数据库路径
        db_manager.set_db_path(db_path)
        print("✅ 数据库路径设置完成")

        # 简单测试连接（不阻塞）
        try:
            if db_manager.test_connection(timeout):
                print(f"✅ 数据库管理器初始化成功: {db_path}")
            else:
                print(f"⚠️ 数据库连接测试失败，但管理器已初始化")
                print("   程序将继续运行，数据库操作时会重新尝试连接")
        except Exception as test_error:
            print(f"⚠️ 数据库连接测试异常: {test_error}")
            print("   程序将继续运行，数据库操作时会重新尝试连接")

    except Exception as e:
        print(f"❌ 数据库管理器初始化失败: {e}")
        print("   程序将继续运行，使用原始数据库操作方式")
        # 不抛出异常，让程序继续运行
        # raise DatabaseError(f"数据库管理器初始化失败: {e}")


def get_db_manager() -> DatabaseConnectionManager:
    """获取数据库管理器实例"""
    return db_manager


def close_database():
    """关闭数据库连接"""
    db_manager.close_connection()
