import sqlite3
import os

db_path = r"C:\\Users\\<USER>\\Desktop\\Day Report\\database\\sales_reports.db"  # 修改为你的数据库路径
if not os.path.isfile(db_path):
    print(f"数据库文件不存在: {db_path}")
    exit(1)

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

print("\n【所有表和视图列表】")
cursor.execute("SELECT name, type FROM sqlite_master WHERE type IN ('table','view') AND name NOT LIKE 'sqlite_%' ORDER BY type, name;")
for name, t in cursor.fetchall():
    print(f"{t}: {name}")

print("\n【表结构详情】")
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';")
tables = [row[0] for row in cursor.fetchall()]
for table in tables:
    print(f"\n表: {table}")
    cursor.execute(f"PRAGMA table_info('{table}')")
    print("字段名\t类型\t主键\t可空")
    for cid, name, ctype, notnull, dflt, pk in cursor.fetchall():
        print(f"{name}\t{ctype}\t{'是' if pk else ''}\t{'否' if notnull else '是'}")
    # 检查索引
    cursor.execute(f"PRAGMA index_list('{table}')")
    idxs = cursor.fetchall()
    if idxs:
        print("索引:")
        for idx in idxs:
            print(f"  {idx[1]} (唯一: {'是' if idx[2] else '否'})")

print("\n【视图定义详情】")
cursor.execute("SELECT name, sql FROM sqlite_master WHERE type='view';")
for name, sql in cursor.fetchall():
    print(f"视图: {name}\n定义: {sql}\n")

print("\n【分析建议】")
for table in tables:
    cursor.execute(f"PRAGMA table_info('{table}')")
    columns = cursor.fetchall()
    pk_count = sum(1 for col in columns if col[-1])
    if pk_count == 0:
        print(f"表 {table} 没有主键，建议添加主键字段。")
    for cid, name, ctype, notnull, dflt, pk in columns:
        if ctype.upper() == 'TEXT' and name.lower().endswith('id') and not pk:
            print(f"表 {table} 字段 {name} 是文本类型且疑似ID，建议考虑是否需要唯一约束或主键。")
        if ctype.upper() == 'REAL' and name.lower() in ('price','amount','rental'):
            print(f"表 {table} 字段 {name} 是 REAL 类型，注意金额精度问题，建议用 NUMERIC 或 TEXT 存储特殊格式。")
        if ctype.upper() == 'INTEGER' and name.lower().startswith('is_'):
            print(f"表 {table} 字段 {name} 是 INTEGER，建议用 0/1 表示布尔值。")

conn.close()
print("\n分析完成。请将上述输出内容发给我，我会进一步优化建议和生成 Rental 字段类型更换脚本。")