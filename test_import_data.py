#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入数据的脚本
"""

import pandas as pd
import sqlite3
import os

# 测试数据
test_data = "PENANG	PRANGIN MALL		*********	Celcom	896019210381540476	015-92270267	2		30/05/2024   01/08/2024 13/06/2030	280.00"

print("🔍 分析您的导入数据...")
print(f"原始数据: {repr(test_data)}")

# 按制表符分割
fields = test_data.split('\t')
print(f"\n📊 分割后的字段数量: {len(fields)}")
for i, field in enumerate(fields):
    print(f"  字段 {i+1}: '{field}'")

# 数据库字段结构
db_columns = [
    "STATE", "Location", "Quantity", "Chair_Serial_No", "Sim_Card_Model", 
    "Sim_Card_No", "Layer", "Company", "Effective_From", "Effective_To", 
    "Rental", "SIMCARDID", "Import_Date", "Last_Updated", "CurrentFlag", "DATE"
]

print(f"\n🗄️ 数据库字段数量: {len(db_columns)}")
for i, col in enumerate(db_columns):
    print(f"  {i+1}. {col}")

# 尝试映射
print(f"\n🔗 字段映射分析:")
if len(fields) >= 10:
    mapping = {
        "STATE": fields[0] if len(fields) > 0 else None,
        "Location": fields[1] if len(fields) > 1 else None,
        "Quantity": fields[2] if len(fields) > 2 else None,
        "Chair_Serial_No": fields[3] if len(fields) > 3 else None,
        "Sim_Card_Model": fields[4] if len(fields) > 4 else None,
        "SIMCARDID": fields[5] if len(fields) > 5 else None,
        "Sim_Card_No": fields[6] if len(fields) > 6 else None,
        "Layer": fields[7] if len(fields) > 7 else None,
        "Company": fields[8] if len(fields) > 8 else None,
        "DATE": fields[9] if len(fields) > 9 else None,
        "Rental": fields[10] if len(fields) > 10 else None,
    }
    
    for key, value in mapping.items():
        print(f"  {key}: '{value}'")
else:
    print("  ❌ 字段数量不足，无法完成映射")

# 检查数据库连接
db_path = "C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db"
if os.path.exists(db_path):
    print(f"\n✅ 数据库文件存在: {db_path}")
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(Equipment_ID)")
            table_info = cursor.fetchall()
            print(f"📋 数据库表结构:")
            for info in table_info:
                print(f"  {info[1]} ({info[2]}) - {'NOT NULL' if info[3] else 'NULL'}")
    except Exception as e:
        print(f"❌ 数据库连接错误: {e}")
else:
    print(f"❌ 数据库文件不存在: {db_path}")

print("\n🎯 建议的导入格式:")
print("请确保您的Excel文件包含以下列标题:")
print("STATE, Location, Quantity, Chair_Serial_No, Sim_Card_Model, SIMCARDID, Sim_Card_No, Layer, Company, DATE, Rental")
