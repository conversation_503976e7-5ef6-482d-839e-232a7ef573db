# -*- coding: utf-8 -*-

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sqlite3
import pandas as pd
import datetime
import configparser
import threading
import time
import queue
from concurrent.futures import ThreadPoolExecutor
# import tksheet  # 未使用，已注释

# === 优化模块导入 - 新的模块化架构 ===
# 检查是否禁用优化模块
import os
DISABLE_OPTIMIZATION = os.environ.get('DISABLE_OPTIMIZATION', '').lower() in ('1', 'true', 'yes')

if not DISABLE_OPTIMIZATION:
    try:
        from constants import Config
        from database_connection_manager import init_database_manager, get_db_manager
        from error_handler import handle_error, error_handler
        from config_manager import config_manager, get_config, get_db_path
        from excel_import_manager import import_excel_data
        OPTIMIZATION_ENABLED = True
        print("✅ 新优化模块加载成功 - 模块化架构已启用")
    except ImportError as e:
        OPTIMIZATION_ENABLED = False
        print(f"ℹ️ 优化模块未加载，使用原始实现: {e}")
        # 创建兼容性对象，确保代码不会出错
        class DummyConfig:
            def __init__(self):
                self.database = type('obj', (object,), {'DEFAULT_PAGE_SIZE': 50, 'OPERATION_HISTORY_LIMIT': 50})()
                self.columns = type('obj', (object,), {'COLUMN_WIDTHS': {}})()
            def get_window_size(self, window_type="main"):
                return "1400x800"
        Config = DummyConfig()
else:
    OPTIMIZATION_ENABLED = False
    print("ℹ️ 优化模块已被环境变量禁用，使用原始实现")
    # 创建兼容性对象
    class DummyConfig:
        def __init__(self):
            self.database = type('obj', (object,), {'DEFAULT_PAGE_SIZE': 50, 'OPERATION_HISTORY_LIMIT': 50})()
            self.columns = type('obj', (object,), {'COLUMN_WIDTHS': {}})()
        def get_window_size(self, window_type="main"):
            return "1400x800"
    Config = DummyConfig()

# === 向后兼容的配置对象 ===
try:
    from config import config as opt_config
    from database_manager import db_manager as opt_db
    from ui_utils import msg as opt_msg, date_helper as opt_date
    print("✅ 原有优化模块也已加载")
except ImportError:
    # 创建兼容性对象
    opt_config = Config if OPTIMIZATION_ENABLED else DummyConfig()
    opt_db = None
    opt_msg = None
    opt_date = None

# === 安全的包装器函数 ===
def safe_get_current_date():
    """安全的日期获取包装器"""
    if OPTIMIZATION_ENABLED and opt_date:
        try:
            return opt_date.get_current_date()
        except Exception as e:
            print(f"⚠️ 优化日期处理失败，使用原始方法: {e}")
            return datetime.datetime.now().strftime("%Y-%m-%d")
    else:
        return datetime.datetime.now().strftime("%Y-%m-%d")

def safe_get_current_timestamp():
    """安全的时间戳获取包装器"""
    if OPTIMIZATION_ENABLED and opt_date:
        try:
            return opt_date.get_current_timestamp_ms()
        except Exception as e:
            print(f"⚠️ 优化时间戳处理失败，使用原始方法: {e}")
            return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    else:
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

def safe_show_info(message, title="提示"):
    """安全的信息提示包装器"""
    if OPTIMIZATION_ENABLED and opt_msg:
        try:
            opt_msg.show_info(message, title)
        except Exception as e:
            print(f"⚠️ 优化消息处理失败，使用原始方法: {e}")
            messagebox.showinfo(title, message)
    else:
        messagebox.showinfo(title, message)

def safe_show_error(message, title="错误"):
    """安全的错误提示包装器"""
    if OPTIMIZATION_ENABLED and opt_msg:
        try:
            opt_msg.show_error(message, title)
        except Exception as e:
            print(f"⚠️ 优化消息处理失败，使用原始方法: {e}")
            messagebox.showerror(title, message)
    else:
        messagebox.showerror(title, message)

def safe_ask_yes_no(message, title="确认"):
    """安全的确认对话框包装器"""
    if OPTIMIZATION_ENABLED and opt_msg:
        try:
            return opt_msg.ask_yes_no(message, title)
        except Exception as e:
            print(f"⚠️ 优化消息处理失败，使用原始方法: {e}")
            return messagebox.askyesno(title, message)
    else:
        return messagebox.askyesno(title, message)

# === 安全数据库包装器和性能监控 ===
import time
from typing import List, Tuple, Any, Optional, Dict

class QueryResult:
    """查询结果封装类"""
    def __init__(self, success: bool, data: Any = None, error_message: str = None, execution_time: float = 0):
        self.success = success
        self.data = data
        self.error_message = error_message
        self.execution_time = execution_time

class DatabaseCache:
    """数据库查询缓存"""
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5分钟缓存
        self.last_cache_time = {}
        self.hit_count = 0
        self.miss_count = 0

    def get(self, key: str) -> Any:
        """获取缓存数据"""
        current_time = time.time()
        if (key in self.cache and
            key in self.last_cache_time and
            current_time - self.last_cache_time[key] < self.cache_timeout):
            self.hit_count += 1
            print(f"📋 缓存命中: {key}")
            return self.cache[key]

        self.miss_count += 1
        return None

    def set(self, key: str, value: Any):
        """设置缓存数据"""
        self.cache[key] = value
        self.last_cache_time[key] = time.time()
        print(f"💾 缓存设置: {key}")

    def clear(self, pattern: str = None):
        """清除缓存"""
        if pattern:
            keys_to_remove = [k for k in self.cache.keys() if pattern in k]
            for key in keys_to_remove:
                del self.cache[key]
                if key in self.last_cache_time:
                    del self.last_cache_time[key]
            print(f"🗑️ 清除缓存: {len(keys_to_remove)}项 (模式: {pattern})")
        else:
            self.cache.clear()
            self.last_cache_time.clear()
            print("🗑️ 清除所有缓存")

    def get_stats(self) -> Dict:
        """获取缓存统计"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = (self.hit_count / total_requests * 100) if total_requests > 0 else 0
        return {
            'hit_count': self.hit_count,
            'miss_count': self.miss_count,
            'hit_rate': f"{hit_rate:.1f}%",
            'cache_size': len(self.cache)
        }

class PerformanceMonitor:
    """性能监控器"""
    def __init__(self):
        self.query_times = {}
        self.query_counts = {}
        self.slow_queries = []

    def record_query(self, query_type: str, execution_time: float, query: str = ""):
        """记录查询性能"""
        if query_type not in self.query_times:
            self.query_times[query_type] = []
            self.query_counts[query_type] = 0

        self.query_times[query_type].append(execution_time)
        self.query_counts[query_type] += 1

        # 记录慢查询 (超过1秒)
        if execution_time > 1.0:
            self.slow_queries.append({
                'type': query_type,
                'time': execution_time,
                'query': query[:100] + "..." if len(query) > 100 else query,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
            })
            print(f"⚠️ 慢查询警告: {query_type} 耗时 {execution_time:.2f}秒")

    def get_performance_report(self) -> str:
        """获取性能报告"""
        report = ["📊 数据库性能报告", "=" * 50]

        for query_type in self.query_times:
            times = self.query_times[query_type]
            count = self.query_counts[query_type]
            if times:
                avg_time = sum(times) / len(times)
                max_time = max(times)
                min_time = min(times)

                report.append(f"查询类型: {query_type}")
                report.append(f"  执行次数: {count}")
                report.append(f"  平均耗时: {avg_time:.3f}秒")
                report.append(f"  最大耗时: {max_time:.3f}秒")
                report.append(f"  最小耗时: {min_time:.3f}秒")
                report.append("")

        if self.slow_queries:
            report.append("🐌 慢查询记录:")
            for sq in self.slow_queries[-5:]:  # 显示最近5个慢查询
                report.append(f"  {sq['timestamp']} - {sq['type']}: {sq['time']:.2f}秒")
                report.append(f"    {sq['query']}")
                report.append("")

        return "\n".join(report)

class SafeDatabaseWrapper:
    """安全数据库包装器"""
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.cache = DatabaseCache()
        self.performance_monitor = PerformanceMonitor()

    def execute_query(self, query: str, params: Tuple = None, fetch_all: bool = True,
                     cache_key: str = None, query_type: str = "unknown") -> QueryResult:
        """安全执行查询"""
        start_time = time.time()

        # 检查缓存
        if cache_key:
            cached_result = self.cache.get(cache_key)
            if cached_result is not None:
                return QueryResult(True, cached_result, execution_time=0)

        try:
            # 使用优化的数据库操作
            if OPTIMIZATION_ENABLED and opt_db:
                try:
                    result = opt_db.execute_query(query, params, fetch_all)
                    if result.success:
                        execution_time = time.time() - start_time
                        self.performance_monitor.record_query(query_type, execution_time, query)

                        # 设置缓存
                        if cache_key:
                            self.cache.set(cache_key, result.data)

                        return QueryResult(True, result.data, execution_time=execution_time)
                    else:
                        # 回退到原始方法
                        return self._execute_original_query(query, params, fetch_all, start_time, query_type, cache_key)
                except Exception as e:
                    print(f"⚠️ 优化数据库操作失败，回退到原始方法: {e}")
                    return self._execute_original_query(query, params, fetch_all, start_time, query_type, cache_key)
            else:
                return self._execute_original_query(query, params, fetch_all, start_time, query_type, cache_key)

        except Exception as e:
            execution_time = time.time() - start_time
            self.performance_monitor.record_query(f"{query_type}_error", execution_time, query)
            return QueryResult(False, None, str(e), execution_time)

    def _execute_original_query(self, query: str, params: Tuple, fetch_all: bool,
                               start_time: float, query_type: str, cache_key: str) -> QueryResult:
        """执行原始查询方法"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(query, params or ())

                if fetch_all:
                    data = cursor.fetchall()
                else:
                    data = cursor.fetchone()

                execution_time = time.time() - start_time
                self.performance_monitor.record_query(query_type, execution_time, query)

                # 设置缓存
                if cache_key:
                    self.cache.set(cache_key, data)

                return QueryResult(True, data, execution_time=execution_time)

        except Exception as e:
            execution_time = time.time() - start_time
            self.performance_monitor.record_query(f"{query_type}_error", execution_time, query)
            return QueryResult(False, None, str(e), execution_time)

    def execute_update(self, query: str, params: Tuple = None, query_type: str = "update") -> QueryResult:
        """安全执行更新操作"""
        start_time = time.time()

        try:
            # 使用优化的数据库操作
            if OPTIMIZATION_ENABLED and opt_db:
                try:
                    success = opt_db.execute_update(query, params)
                    execution_time = time.time() - start_time
                    self.performance_monitor.record_query(query_type, execution_time, query)

                    if success:
                        # 清除相关缓存
                        self.cache.clear("equipment")
                        return QueryResult(True, None, execution_time=execution_time)
                    else:
                        return self._execute_original_update(query, params, start_time, query_type)
                except Exception as e:
                    print(f"⚠️ 优化数据库更新失败，回退到原始方法: {e}")
                    return self._execute_original_update(query, params, start_time, query_type)
            else:
                return self._execute_original_update(query, params, start_time, query_type)

        except Exception as e:
            execution_time = time.time() - start_time
            self.performance_monitor.record_query(f"{query_type}_error", execution_time, query)
            return QueryResult(False, None, str(e), execution_time)

    def _execute_original_update(self, query: str, params: Tuple, start_time: float, query_type: str) -> QueryResult:
        """执行原始更新方法"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(query, params or ())
                conn.commit()

                execution_time = time.time() - start_time
                self.performance_monitor.record_query(query_type, execution_time, query)

                # 清除相关缓存
                self.cache.clear("equipment")

                return QueryResult(True, None, execution_time=execution_time)

        except Exception as e:
            execution_time = time.time() - start_time
            self.performance_monitor.record_query(f"{query_type}_error", execution_time, query)
            return QueryResult(False, None, str(e), execution_time)

    def batch_execute(self, queries: List[Tuple[str, Tuple]], query_type: str = "batch") -> QueryResult:
        """批量执行操作 - 优化的批量操作支持"""
        start_time = time.time()

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("BEGIN TRANSACTION")

                try:
                    for query, params in queries:
                        cursor.execute(query, params or ())

                    cursor.execute("COMMIT")
                    execution_time = time.time() - start_time
                    self.performance_monitor.record_query(query_type, execution_time, f"批量操作 {len(queries)} 条")

                    # 清除相关缓存
                    self.cache.clear("equipment")

                    print(f"✅ 批量操作完成: {len(queries)} 条语句，耗时 {execution_time:.3f}秒")
                    return QueryResult(True, len(queries), execution_time=execution_time)

                except Exception as e:
                    cursor.execute("ROLLBACK")
                    raise e

        except Exception as e:
            execution_time = time.time() - start_time
            self.performance_monitor.record_query(f"{query_type}_error", execution_time)
            return QueryResult(False, None, str(e), execution_time)

    def batch_delete_optimized(self, ids_to_delete: List[int], query_type: str = "batch_delete") -> QueryResult:
        """优化的批量删除操作"""
        start_time = time.time()

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("BEGIN TRANSACTION")

                try:
                    # 先获取要删除的记录（用于撤销）
                    placeholders = ','.join(['?'] * len(ids_to_delete))
                    cursor.execute(f"SELECT * FROM Equipment_ID WHERE ID IN ({placeholders})", ids_to_delete)
                    deleted_records = cursor.fetchall()

                    # 批量删除 - 使用单条SQL语句替代循环
                    cursor.execute(f"DELETE FROM Equipment_ID WHERE ID IN ({placeholders})", ids_to_delete)

                    cursor.execute("COMMIT")
                    execution_time = time.time() - start_time
                    self.performance_monitor.record_query(query_type, execution_time, f"批量删除 {len(ids_to_delete)} 条")

                    # 清除相关缓存
                    self.cache.clear("equipment")

                    print(f"✅ 批量删除完成: {len(ids_to_delete)} 条记录，耗时 {execution_time:.3f}秒")
                    return QueryResult(True, deleted_records, execution_time=execution_time)

                except Exception as e:
                    cursor.execute("ROLLBACK")
                    raise e

        except Exception as e:
            execution_time = time.time() - start_time
            self.performance_monitor.record_query(f"{query_type}_error", execution_time)
            return QueryResult(False, None, str(e), execution_time)

    def batch_update_optimized(self, update_data: List[Dict], query_type: str = "batch_update") -> QueryResult:
        """优化的批量更新操作"""
        start_time = time.time()

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("BEGIN TRANSACTION")

                try:
                    # 构建批量更新查询
                    for data in update_data:
                        set_clauses = []
                        params = []

                        for field, value in data['updates'].items():
                            set_clauses.append(f"{field} = ?")
                            params.append(value)

                        params.append(data['id'])
                        query = f"UPDATE Equipment_ID SET {', '.join(set_clauses)} WHERE ID = ?"
                        cursor.execute(query, params)

                    cursor.execute("COMMIT")
                    execution_time = time.time() - start_time
                    self.performance_monitor.record_query(query_type, execution_time, f"批量更新 {len(update_data)} 条")

                    # 清除相关缓存
                    self.cache.clear("equipment")

                    print(f"✅ 批量更新完成: {len(update_data)} 条记录，耗时 {execution_time:.3f}秒")
                    return QueryResult(True, len(update_data), execution_time=execution_time)

                except Exception as e:
                    cursor.execute("ROLLBACK")
                    raise e

        except Exception as e:
            execution_time = time.time() - start_time
            self.performance_monitor.record_query(f"{query_type}_error", execution_time)
            return QueryResult(False, None, str(e), execution_time)

    def get_cache_stats(self) -> Dict:
        """获取缓存统计"""
        return self.cache.get_stats()

    def get_performance_report(self) -> str:
        """获取性能报告"""
        return self.performance_monitor.get_performance_report()

    def clear_cache(self, pattern: str = None):
        """清除缓存"""
        self.cache.clear(pattern)

# === 异步操作管理器 ===
class AsyncOperationManager:
    """异步操作管理器 - 处理大批量操作"""
    def __init__(self, max_workers=4):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.active_operations = {}
        self.operation_counter = 0

    def submit_operation(self, operation_func, *args, **kwargs):
        """提交异步操作"""
        self.operation_counter += 1
        operation_id = f"op_{self.operation_counter}"

        future = self.executor.submit(operation_func, *args, **kwargs)
        self.active_operations[operation_id] = {
            'future': future,
            'start_time': time.time(),
            'description': kwargs.get('description', '未知操作')
        }

        return operation_id, future

    def get_operation_status(self, operation_id):
        """获取操作状态"""
        if operation_id not in self.active_operations:
            return None

        operation = self.active_operations[operation_id]
        future = operation['future']

        status = {
            'id': operation_id,
            'description': operation['description'],
            'start_time': operation['start_time'],
            'elapsed_time': time.time() - operation['start_time'],
            'done': future.done(),
            'cancelled': future.cancelled()
        }

        if future.done():
            try:
                status['result'] = future.result()
                status['success'] = True
            except Exception as e:
                status['error'] = str(e)
                status['success'] = False

        return status

    def cancel_operation(self, operation_id):
        """取消操作"""
        if operation_id in self.active_operations:
            future = self.active_operations[operation_id]['future']
            return future.cancel()
        return False

    def cleanup_completed_operations(self):
        """清理已完成的操作"""
        completed_ops = [op_id for op_id, op in self.active_operations.items()
                        if op['future'].done()]
        for op_id in completed_ops:
            del self.active_operations[op_id]

    def get_active_operations_count(self):
        """获取活跃操作数量"""
        return len([op for op in self.active_operations.values()
                   if not op['future'].done()])

    def shutdown(self):
        """关闭执行器"""
        self.executor.shutdown(wait=True)

# === 智能搜索管理器 ===
class SmartSearchManager:
    """智能搜索管理器 - 提供搜索建议和历史记录"""
    def __init__(self, max_history=50):
        self.search_history = []
        self.max_history = max_history
        self.suggestions_cache = {}
        self.last_cache_update = 0
        self.cache_timeout = 300  # 5分钟缓存

    def add_search_history(self, search_type, search_value):
        """添加搜索历史"""
        if not search_value or search_value.strip() == "":
            return

        search_item = {
            'type': search_type,
            'value': search_value.strip(),
            'timestamp': time.time(),
            'count': 1
        }

        # 检查是否已存在相同搜索
        for item in self.search_history:
            if item['type'] == search_type and item['value'] == search_value.strip():
                item['count'] += 1
                item['timestamp'] = time.time()
                return

        # 添加新搜索
        self.search_history.append(search_item)

        # 保持历史记录数量限制
        if len(self.search_history) > self.max_history:
            # 按时间戳排序，移除最旧的
            self.search_history.sort(key=lambda x: x['timestamp'])
            self.search_history = self.search_history[-self.max_history:]

    def get_search_suggestions(self, search_type, partial_value, limit=10):
        """获取搜索建议"""
        if not partial_value:
            # 返回最近的搜索历史
            recent_searches = [item for item in self.search_history
                             if item['type'] == search_type]
            recent_searches.sort(key=lambda x: (x['count'], x['timestamp']), reverse=True)
            return [item['value'] for item in recent_searches[:limit]]

        partial_lower = partial_value.lower()
        suggestions = []

        # 从历史记录中匹配
        for item in self.search_history:
            if (item['type'] == search_type and
                partial_lower in item['value'].lower() and
                item['value'] not in suggestions):
                suggestions.append(item['value'])

        # 从缓存的数据库建议中匹配
        if search_type in self.suggestions_cache:
            for suggestion in self.suggestions_cache[search_type]:
                if (partial_lower in suggestion.lower() and
                    suggestion not in suggestions):
                    suggestions.append(suggestion)

        # 按相关性排序（完全匹配优先，然后按匹配位置）
        def relevance_score(s):
            s_lower = s.lower()
            if s_lower == partial_lower:
                return 0  # 完全匹配
            elif s_lower.startswith(partial_lower):
                return 1  # 前缀匹配
            else:
                return 2 + s_lower.find(partial_lower)  # 包含匹配

        suggestions.sort(key=relevance_score)
        return suggestions[:limit]

    def update_database_suggestions(self, db_path):
        """从数据库更新建议缓存"""
        current_time = time.time()
        if current_time - self.last_cache_update < self.cache_timeout:
            return  # 缓存仍然有效

        try:
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()

                # 更新序列号建议
                cursor.execute("SELECT DISTINCT Chair_Serial_No FROM Equipment_ID WHERE Chair_Serial_No IS NOT NULL AND Chair_Serial_No != '' ORDER BY Chair_Serial_No LIMIT 100")
                self.suggestions_cache['serial'] = [row[0] for row in cursor.fetchall()]

                # 更新位置建议
                cursor.execute("SELECT DISTINCT Location FROM Equipment_ID WHERE Location IS NOT NULL AND Location != '' ORDER BY Location LIMIT 50")
                self.suggestions_cache['location'] = [row[0] for row in cursor.fetchall()]

                # 更新状态建议
                cursor.execute("SELECT DISTINCT STATE FROM Equipment_ID WHERE STATE IS NOT NULL AND STATE != '' ORDER BY STATE LIMIT 20")
                self.suggestions_cache['state'] = [row[0] for row in cursor.fetchall()]

                self.last_cache_update = current_time
                print(f"🔍 智能搜索建议已更新: 序列号{len(self.suggestions_cache.get('serial', []))}个, 位置{len(self.suggestions_cache.get('location', []))}个, 状态{len(self.suggestions_cache.get('state', []))}个")

        except Exception as e:
            print(f"更新搜索建议失败: {e}")

    def get_search_history(self, search_type=None, limit=20):
        """获取搜索历史"""
        if search_type:
            history = [item for item in self.search_history if item['type'] == search_type]
        else:
            history = self.search_history

        # 按使用频率和时间排序
        history.sort(key=lambda x: (x['count'], x['timestamp']), reverse=True)
        return history[:limit]

    def clear_history(self, search_type=None):
        """清除搜索历史"""
        if search_type:
            self.search_history = [item for item in self.search_history if item['type'] != search_type]
        else:
            self.search_history.clear()
        print(f"🗑️ 已清除搜索历史: {search_type or '全部'}")

# 创建全局智能搜索管理器
smart_search = SmartSearchManager()

# 创建全局异步操作管理器
async_manager = AsyncOperationManager()

# 创建全局数据库包装器实例
safe_db = None  # 将在load_config后初始化

CONFIG_FILE = "db_config.ini"
# 使用新的配置管理器获取数据库路径
if OPTIMIZATION_ENABLED:
    # 从配置中获取当前数据库路径
    try:
        from config_manager import user_preferences
        DB_PATH = user_preferences.get_current_database()
        print(f"📁 当前数据库路径: {DB_PATH}")
    except Exception as e:
        print(f"⚠️ 获取数据库路径失败: {e}")
        DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
else:
    DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db" # 默认路径

def load_config():
    global DB_PATH, safe_db

    # 如果优化模块启用，使用新的配置管理器
    if OPTIMIZATION_ENABLED:
        print(f"ℹ️ 使用新配置管理器，数据库路径: {DB_PATH}")
    else:
        # 使用旧的配置文件逻辑
        config = configparser.ConfigParser()
        if os.path.exists(CONFIG_FILE):
            try:
                config.read(CONFIG_FILE)
                if 'Database' in config and 'Path' in config['Database']:
                    loaded_path = config['Database']['Path']
                    # 检查路径是否存在且是文件
                    if os.path.isfile(loaded_path) and loaded_path.endswith('.db'):
                        DB_PATH = loaded_path
                        print(f"从配置文件加载数据库路径: {DB_PATH}")
                    else:
                        print(f"配置文件中的路径无效或不是文件: {loaded_path}, 使用默认路径。")
                else:
                     print("配置文件中未找到数据库路径，使用默认路径。")
            except Exception as e:
                print(f"读取配置文件错误: {e}, 使用默认路径。")
        else:
            print("配置文件不存在，使用默认路径。")

    # 初始化安全数据库包装器
    safe_db = SafeDatabaseWrapper(DB_PATH)
    print(f"✅ 安全数据库包装器已初始化: {DB_PATH}")

    return DB_PATH

def save_config(db_path):
    global DB_PATH
    DB_PATH = db_path
    config = configparser.ConfigParser()
    config['Database'] = {'Path': db_path}
    try:
        with open(CONFIG_FILE, 'w') as f:
            config.write(f)
    except Exception as e:
        print(f"保存配置文件失败: {e}")

def add_log_entry(action, description, target):
    try:
        # 获取当前时间，包含毫秒
        timestamp = safe_get_current_timestamp()
        
        # 获取更多上下文信息
        operation_details = description
        if "序列号" not in description and isinstance(target, str) and target.strip():
            if "Chair_Serial_No:" in target:
                # 已经包含序列号信息
                operation_details = f"{description}, {target}"
            else:
                # 添加操作时间信息
                operation_details = f"{description}, 操作时间: {timestamp}"
        
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()
            # 确保Logs表存在
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS Logs (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Timestamp TEXT,
                Action TEXT,
                Description TEXT,
                Target TEXT
            )
            """)
            cursor.execute("""
            INSERT INTO Logs (Timestamp, Action, Description, Target)
            VALUES (?, ?, ?, ?)
            """, (timestamp, action, operation_details, str(target))) # 确保target是字符串
            conn.commit()
    except Exception as e:
        print(f"日志写入失败: {e}")

def init_database():
    try:
        # 确保数据库目录存在
        db_dir = os.path.dirname(DB_PATH)
        if db_dir and not os.path.exists(db_dir):
             os.makedirs(db_dir, exist_ok=True)
             print(f"创建数据库目录: {db_dir}")

        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()
            # 创建 Equipment_ID 表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS Equipment_ID (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                STATE TEXT,
                Location TEXT,
                Quantity INTEGER,
                Chair_Serial_No TEXT UNIQUE, -- 增加唯一约束
                Sim_Card_Model TEXT,
                Sim_Card_No TEXT,
                Layer TEXT,
                Company TEXT,
                Effective_From TEXT,
                Effective_To TEXT,
                Rental REAL,
                SIMCARDID TEXT,
                Import_Date TEXT,
                Last_Updated TEXT,
                CurrentFlag TEXT,
                DATE TEXT
            )
            """)
            # 创建 Daily_Equipment_Sales 表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS Daily_Equipment_Sales (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                State TEXT,
                Location TEXT,
                Quantity INTEGER,
                Chair_Serial_No TEXT,
                Sale_Date TEXT,
                IOT_Price REAL,
                ZERO_Price REAL,
                Checked INTEGER DEFAULT 0,
                Expiry_Date TEXT
            )
            """)
            # 创建 Logs 表 (以防 add_log_entry 首次调用时失败)
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS Logs (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                Timestamp TEXT,
                Action TEXT,
                Description TEXT,
                Target TEXT
            )
            """)
            
            # 创建 Current_Equipment 视图，用于查询当前生效的设备记录
            cursor.execute("""
            CREATE VIEW IF NOT EXISTS Current_Equipment AS
            SELECT e.*,
                CASE
                    WHEN EXISTS (SELECT 1 FROM Equipment_ID e2 
                                WHERE e2.Chair_Serial_No = e.Chair_Serial_No 
                                AND e2.ID != e.ID
                                AND (e2.Effective_From IS NULL OR date(e2.Effective_From) <= date('now'))
                                AND (e2.Effective_To IS NULL OR date(e2.Effective_To) >= date('now')))
                    THEN 'DUPLICATE'
                    ELSE 'ACTIVE'
                END AS CurrentFlag
            FROM Equipment_ID e
            WHERE (e.Effective_From IS NULL OR date(e.Effective_From) <= date('now'))
              AND (e.Effective_To IS NULL OR date(e.Effective_To) >= date('now'))
            """)
            
            conn.commit()
            print(f"数据库 {DB_PATH} 初始化/检查完成。")
    except Exception as e:
        print(f"数据库初始化错误: {str(e)}")
        safe_show_error(f"无法初始化或连接数据库: {DB_PATH}\n错误: {e}\n请检查路径或权限。", "数据库错误")
        sys.exit(1) # 初始化失败则退出

def standardize_date(date_str):
    """识别并转换多种日期格式为标准格式 YYYY-MM-DD，包括Excel序列号日期"""
    if not date_str:
        return None
        
    s = str(date_str).strip()
    
    # 判断是否为Excel序列号（纯数字且长度>=5）
    if s.isdigit() and len(s) >= 5:
        try:
            serial = int(s)
            base = datetime.datetime(1899, 12, 30)  # Excel日期基准点
            dt = base + datetime.timedelta(days=serial)
            return dt.strftime("%Y-%m-%d")
        except:
            pass  # 如果转换失败，继续尝试其他格式
    
    # 尝试标准日期格式
    try:
        datetime.datetime.strptime(s, "%Y-%m-%d")
        return s  # 已经是标准格式
    except ValueError:
        pass
    
    # 尝试其他常见格式
    formats = [
        "%d/%m/%Y", "%m/%d/%Y", "%Y/%m/%d",
        "%d-%m-%Y", "%m-%d-%Y", "%Y-%m-%d",
        "%d.%m.%Y", "%m.%d.%Y", "%Y.%m.%d"
    ]
    
    for fmt in formats:
        try:
            dt = datetime.datetime.strptime(s, fmt)
            return dt.strftime("%Y-%m-%d")
        except ValueError:
            continue
    
    # 如果所有格式都失败，返回原始值
    return s

def is_valid_date(s):
    """检查字符串是否为 YYYY-MM-DD 格式"""
    if not s:
        return True # 允许空值
    try:
        datetime.datetime.strptime(s, "%Y-%m-%d")
        return True
    except ValueError:
        # 尝试使用standardize_date转换后再验证
        standardized = standardize_date(s)
        try:
            datetime.datetime.strptime(standardized, "%Y-%m-%d")
            return True
        except ValueError:
            return False

class EquipmentManager:
    def __init__(self, root):
        self.root = root
        self.root.title("设备ID管理系统")

        # 使用新的配置系统
        if OPTIMIZATION_ENABLED:
            self.root.geometry(Config.get_window_size("main"))
            self.page_size = get_config('ui', 'page_size', Config.UI.DEFAULT_PAGE_SIZE)
            # 尝试安全初始化数据库管理器
            self.db_manager = None
            try:
                print("🔄 尝试安全初始化数据库管理器...")
                # 使用线程来初始化，避免阻塞主线程
                import threading
                import time

                def safe_init_db():
                    try:
                        init_database_manager(DB_PATH, timeout=1.0)  # 1秒超时
                        return get_db_manager()
                    except Exception as e:
                        print(f"⚠️ 数据库管理器初始化失败: {e}")
                        return None

                # 在单独线程中初始化
                db_result = [None]
                def init_thread():
                    db_result[0] = safe_init_db()

                thread = threading.Thread(target=init_thread, daemon=True)
                thread.start()
                thread.join(timeout=2.0)  # 最多等待2秒

                if thread.is_alive():
                    print("⚠️ 数据库管理器初始化超时，使用原始数据库操作")
                    self.db_manager = None
                else:
                    self.db_manager = db_result[0]
                    if self.db_manager:
                        print("✅ 数据库管理器初始化成功")
                    else:
                        print("⚠️ 数据库管理器初始化失败，使用原始数据库操作")

            except Exception as e:
                print(f"⚠️ 数据库管理器初始化异常: {e}")
                self.db_manager = None

            print("🔄 继续程序初始化...")
        else:
            self.root.geometry("1400x800")
            self.page_size = 50
            self.db_manager = None

        self.current_page = 1
        self.total_pages = 1
        self.total_records = 0
        # 默认查询排除已失效的记录
        today = safe_get_current_date()
        self.current_query = """
            SELECT * FROM Equipment_ID
            WHERE (Effective_To IS NULL OR Effective_To = '' OR Effective_To > ?)
        """
        self.current_params = (today,) # 保存当前查询参数
        self.sort_column_name = "ID" # 当前排序列
        self.sort_direction = "DESC" # 当前排序方向

        # 操作历史记录，用于撤销功能
        self.operation_history = []  # 存储所有操作的历史记录
        self.deleted_records = []  # 保持向后兼容

        # 初始化数据库包装器引用
        # 确保safe_db已经初始化
        if safe_db is None:
            # 如果还没有初始化，先加载配置
            load_config()
        self.safe_db = safe_db
        
        # 设置现代化主题样式
        self.setup_modern_theme()

        # 加载配置并初始化数据库
        load_config()
        init_database()

        self.setup_ui()
        self.setup_basic_shortcuts()  # 设置基础快捷键
        self.refresh_data() # 初始加载数据

    def setup_modern_theme(self):
        """设置现代化主题样式"""
        self.style = ttk.Style()

        # 选择现代化主题
        try:
            # 尝试使用现代主题
            available_themes = self.style.theme_names()
            if 'vista' in available_themes:
                self.style.theme_use('vista')
            elif 'clam' in available_themes:
                self.style.theme_use('clam')
            elif 'alt' in available_themes:
                self.style.theme_use('alt')
        except Exception as e:
            print(f"主题设置失败，使用默认主题: {e}")

        # 现代化配色方案 - 参考Material Design和Fluent Design
        self.colors = {
            'primary': '#1976D2',      # 深蓝色主色调 (Material Blue 700)
            'primary_light': '#42A5F5', # 浅蓝色 (Material Blue 400)
            'primary_dark': '#0D47A1',  # 深蓝色 (Material Blue 900)
            'secondary': '#7C4DFF',     # 紫色辅助色 (Material Deep Purple A200)
            'success': '#4CAF50',       # 绿色成功色 (Material Green 500)
            'warning': '#FF9800',       # 橙色警告色 (Material Orange 500)
            'danger': '#F44336',        # 红色危险色 (Material Red 500)
            'background': '#FAFAFA',    # 极浅灰背景 (Material Grey 50)
            'surface': '#FFFFFF',       # 白色表面
            'surface_variant': '#F5F5F5', # 表面变体 (Material Grey 100)
            'text_primary': '#212121',  # 主要文本 (Material Grey 900)
            'text_secondary': '#757575', # 次要文本 (Material Grey 600)
            'text_hint': '#BDBDBD',     # 提示文本 (Material Grey 400)
            'border': '#E0E0E0',        # 边框颜色 (Material Grey 300)
            'divider': '#EEEEEE',       # 分割线 (Material Grey 200)
            'shadow': '#E0E0E0',        # 阴影颜色 (使用灰色替代透明度)
            'hover': '#F5F5F5',         # 悬停颜色
            'selected': '#E3F2FD'       # 选中颜色 (Material Blue 50)
        }

        # 配置现代化样式
        self.configure_modern_styles()

        # 设置窗口图标和标题
        self.setup_window_appearance()

    def create_modern_button(self, parent, text, command, style="primary", width=None, icon=None):
        """创建现代化按钮 - 参考Material Design和Fluent Design"""
        # 根据样式选择颜色
        style_configs = {
            "primary": {
                "bg": self.colors['primary'],
                "fg": "white",
                "hover": self.colors['primary_dark'],
                "active": self.colors['primary_light'],
                "border": self.colors['primary']
            },
            "secondary": {
                "bg": self.colors['secondary'],
                "fg": "white",
                "hover": "#6A1B9A",
                "active": self.colors['secondary'],
                "border": self.colors['secondary']
            },
            "success": {
                "bg": self.colors['success'],
                "fg": "white",
                "hover": "#388E3C",
                "active": self.colors['success'],
                "border": self.colors['success']
            },
            "warning": {
                "bg": self.colors['warning'],
                "fg": "white",
                "hover": "#F57C00",
                "active": self.colors['warning'],
                "border": self.colors['warning']
            },
            "danger": {
                "bg": self.colors['danger'],
                "fg": "white",
                "hover": "#D32F2F",
                "active": self.colors['danger'],
                "border": self.colors['danger']
            },
            "outline": {
                "bg": self.colors['surface'],
                "fg": self.colors['primary'],
                "hover": self.colors['hover'],
                "active": self.colors['selected'],
                "border": self.colors['border']
            }
        }

        config = style_configs.get(style, style_configs["primary"])

        # 创建现代化按钮
        btn = tk.Button(
            parent,
            text=f"{icon} {text}" if icon else text,
            command=command,
            bg=config["bg"],
            fg=config["fg"],
            font=("Segoe UI", 9, "normal"),
            relief="flat",
            borderwidth=1 if style == "outline" else 0,
            highlightthickness=0,
            padx=20,
            pady=10,
            cursor="hand2",
            width=width,
            activebackground=config["active"],
            activeforeground=config["fg"]
        )

        # 添加现代化悬停效果和阴影
        def on_enter(e):
            btn.config(bg=config["hover"])
            # 添加轻微的阴影效果（通过调整relief）
            if style != "outline":
                btn.config(relief="raised", borderwidth=1)

        def on_leave(e):
            btn.config(bg=config["bg"])
            btn.config(relief="flat", borderwidth=1 if style == "outline" else 0)

        def on_click(e):
            btn.config(bg=config["active"])
            btn.after(100, lambda: btn.config(bg=config["hover"]))

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        btn.bind("<Button-1>", on_click)

        return btn

    def create_modern_card(self, parent, title=None, padding=15):
        """创建现代化卡片容器"""
        # 外层框架提供阴影效果
        shadow_frame = tk.Frame(parent, bg=self.colors['shadow'], height=2)

        # 主卡片框架
        card_frame = tk.Frame(
            parent,
            bg=self.colors['surface'],
            relief="flat",
            borderwidth=1,
            highlightbackground=self.colors['border'],
            highlightthickness=1
        )

        if title:
            # 标题区域
            title_frame = tk.Frame(card_frame, bg=self.colors['surface'], height=50)
            title_frame.pack(fill=tk.X, padx=padding, pady=(padding, 0))
            title_frame.pack_propagate(False)

            title_label = tk.Label(
                title_frame,
                text=title,
                bg=self.colors['surface'],
                fg=self.colors['text_primary'],
                font=("Segoe UI", 12, "bold"),
                anchor="w"
            )
            title_label.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, pady=10)

            # 分割线
            separator = tk.Frame(card_frame, bg=self.colors['divider'], height=1)
            separator.pack(fill=tk.X, padx=padding)

        # 内容区域
        content_frame = tk.Frame(card_frame, bg=self.colors['surface'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=padding, pady=padding)

        return card_frame, content_frame

    def create_header(self):
        """创建现代化标题区域"""
        # 标题卡片
        header_card, header_content = self.create_modern_card(self.main_frame, None)
        header_card.pack(fill=tk.X, pady=(0, 15))

        # 标题容器
        title_container = tk.Frame(header_content, bg=self.colors['surface'])
        title_container.pack(fill=tk.X)

        # 左侧标题区域
        title_section = tk.Frame(title_container, bg=self.colors['surface'])
        title_section.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 主标题
        main_title = tk.Label(
            title_section,
            text="🏢 设备ID管理系统",
            bg=self.colors['surface'],
            fg=self.colors['text_primary'],
            font=("Segoe UI", 18, "bold")
        )
        main_title.pack(anchor=tk.W, pady=(0, 5))

        # 副标题
        subtitle = tk.Label(
            title_section,
            text="现代化设备资产管理解决方案",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=("Segoe UI", 11)
        )
        subtitle.pack(anchor=tk.W)

        # 右侧状态区域
        status_section = tk.Frame(title_container, bg=self.colors['surface'])
        status_section.pack(side=tk.RIGHT, padx=(20, 0))

        # 数据库状态
        db_status_frame = tk.Frame(status_section, bg=self.colors['surface'])
        db_status_frame.pack(anchor=tk.E, pady=(0, 5))

        db_icon = tk.Label(
            db_status_frame,
            text="💾",
            bg=self.colors['surface'],
            fg=self.colors['success'],
            font=("Segoe UI", 12)
        )
        db_icon.pack(side=tk.LEFT, padx=(0, 5))

        db_label = tk.Label(
            db_status_frame,
            text="数据库已连接",
            bg=self.colors['surface'],
            fg=self.colors['success'],
            font=("Segoe UI", 9, "bold")
        )
        db_label.pack(side=tk.LEFT)

        # 版本信息
        version_frame = tk.Frame(status_section, bg=self.colors['surface'])
        version_frame.pack(anchor=tk.E)

        version_label = tk.Label(
            version_frame,
            text="v2.0 现代化版本",
            bg=self.colors['surface'],
            fg=self.colors['text_hint'],
            font=("Segoe UI", 8)
        )
        version_label.pack()

    def create_friendly_header(self):
        """创建友好的标题区域，显示数据库位置"""
        # 标题框架
        header_frame = ttk.LabelFrame(self.main_frame, text="📊 设备ID管理系统", padding=10)
        header_frame.pack(fill=tk.X, pady=(0, 10))

        # 信息容器
        info_frame = ttk.Frame(header_frame)
        info_frame.pack(fill=tk.X)

        # 左侧：欢迎信息
        welcome_frame = ttk.Frame(info_frame)
        welcome_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        welcome_label = ttk.Label(
            welcome_frame,
            text="欢迎使用设备ID管理系统 - 现代化版本",
            font=("Segoe UI", 12, "bold")
        )
        welcome_label.pack(anchor=tk.W)

        # 数据库位置信息
        db_info_label = ttk.Label(
            welcome_frame,
            text=f"📁 数据库位置: {DB_PATH}",
            font=("Segoe UI", 9),
            foreground="blue"
        )
        db_info_label.pack(anchor=tk.W, pady=(2, 0))

        # 右侧：状态信息
        status_frame = ttk.Frame(info_frame)
        status_frame.pack(side=tk.RIGHT)

        # 连接状态
        connection_label = ttk.Label(
            status_frame,
            text="🟢 数据库已连接",
            font=("Segoe UI", 10, "bold"),
            foreground="green"
        )
        connection_label.pack(anchor=tk.E)

        # 快捷键提示
        shortcut_label = ttk.Label(
            status_frame,
            text="💡 提示: Ctrl+Z撤销, Ctrl+E批量编辑",
            font=("Segoe UI", 8),
            foreground="gray"
        )
        shortcut_label.pack(anchor=tk.E, pady=(2, 0))

    def create_search_panel_simple(self):
        """创建简化的搜索面板"""
        search_frame = ttk.LabelFrame(self.main_frame, text="🔍 智能搜索", padding=10)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        # 搜索字段容器
        fields_frame = ttk.Frame(search_frame)
        fields_frame.pack(fill=tk.X)

        # 序列号搜索
        serial_frame = ttk.Frame(fields_frame)
        serial_frame.pack(side=tk.LEFT, padx=(0, 15))
        ttk.Label(serial_frame, text="序列号:").pack(anchor=tk.W)
        self.serial_var = tk.StringVar()
        self.serial_combo = ttk.Combobox(serial_frame, textvariable=self.serial_var, width=18)
        self.serial_combo.pack(pady=(2, 0))
        self.serial_combo.bind('<KeyRelease>', lambda e: self.update_smart_suggestions('serial'))
        self.serial_combo.bind('<FocusIn>', lambda e: self.show_search_history('serial'))

        # 位置搜索
        location_frame = ttk.Frame(fields_frame)
        location_frame.pack(side=tk.LEFT, padx=(0, 15))
        ttk.Label(location_frame, text="位置:").pack(anchor=tk.W)
        self.location_var = tk.StringVar()
        self.location_combo = ttk.Combobox(location_frame, textvariable=self.location_var, width=18)
        self.location_combo.pack(pady=(2, 0))
        self.location_combo.bind('<KeyRelease>', lambda e: self.update_smart_suggestions('location'))
        self.location_combo.bind('<FocusIn>', lambda e: self.show_search_history('location'))

        # 状态搜索
        state_frame = ttk.Frame(fields_frame)
        state_frame.pack(side=tk.LEFT, padx=(0, 15))
        ttk.Label(state_frame, text="状态:").pack(anchor=tk.W)
        self.state_var = tk.StringVar()
        self.state_combo = ttk.Combobox(state_frame, textvariable=self.state_var, width=18)
        self.state_combo.pack(pady=(2, 0))
        self.state_combo.bind('<KeyRelease>', lambda e: self.update_smart_suggestions('state'))
        self.state_combo.bind('<FocusIn>', lambda e: self.show_search_history('state'))

        # 搜索按钮组
        button_frame = ttk.Frame(fields_frame)
        button_frame.pack(side=tk.LEFT, padx=(15, 0))
        ttk.Label(button_frame, text="操作:").pack(anchor=tk.W)

        btn_container = ttk.Frame(button_frame)
        btn_container.pack(pady=(2, 0))

        ttk.Button(btn_container, text="🔍 搜索", command=lambda: self.smart_search_data(reset_page=True)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_container, text="🗑️ 清空", command=self.clear_search).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_container, text="📜 历史", command=self.show_search_history_window).pack(side=tk.LEFT, padx=(0, 5))

        # 过滤选项组
        filter_frame = ttk.Frame(fields_frame)
        filter_frame.pack(side=tk.LEFT, padx=(15, 0))
        ttk.Label(filter_frame, text="过滤:").pack(anchor=tk.W)

        filter_container = ttk.Frame(filter_frame)
        filter_container.pack(pady=(2, 0))

        # 添加过滤失效日期的切换按钮
        self.filter_expired = tk.BooleanVar(value=True)  # 默认过滤失效记录
        self.filter_btn = ttk.Checkbutton(
            filter_container,
            text="🚫 过滤失效",
            variable=self.filter_expired,
            command=self.toggle_expired_filter
        )
        self.filter_btn.pack(side=tk.LEFT)

        # 统计信息
        stats_frame = ttk.Frame(fields_frame)
        stats_frame.pack(side=tk.RIGHT)
        ttk.Label(stats_frame, text="统计:").pack(anchor=tk.W)
        self.total_count_var = tk.StringVar()
        ttk.Label(stats_frame, textvariable=self.total_count_var).pack(pady=(2, 0))

        # 初始化智能搜索建议
        self.update_search_suggestions()

    def toggle_expired_filter(self):
        """切换过滤失效记录的状态"""
        # 更新按钮文本
        if self.filter_expired.get():
            self.filter_btn.config(text="🚫 过滤失效")
            self.status_var.set("已启用失效记录过滤")
        else:
            self.filter_btn.config(text="👁️ 显示全部")
            self.status_var.set("已显示所有记录（包括失效）")

        # 重新执行当前搜索
        self.smart_search_data(reset_page=True)

    def create_treeview_simple(self):
        """创建简化的数据表格"""
        tree_frame = ttk.LabelFrame(self.main_frame, text="📊 设备数据", padding=10)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        # 定义所有列（包括隐藏的列）
        self.all_columns = ("ID", "STATE", "Location", "Quantity", "Chair_Serial_No",
                           "Sim_Card_Model", "Sim_Card_No", "Layer", "Company",
                           "Effective_From", "Effective_To", "Rental", "SIMCARDID",
                           "Import_Date", "Last_Updated", "CurrentFlag", "DATE")

        # 定义要隐藏的列
        self.hidden_columns = ("Sim_Card_Model", "Sim_Card_No", "CurrentFlag", "SIMCARDID")

        # 定义显示的列（排除隐藏的列）
        self.columns = tuple(col for col in self.all_columns if col not in self.hidden_columns)

        self.tree = ttk.Treeview(tree_frame, columns=self.columns, show="headings")

        # --- 设置列宽和对齐 ---
        if OPTIMIZATION_ENABLED and hasattr(Config, 'UI') and hasattr(Config.UI, 'COLUMN_WIDTHS'):
            column_widths = Config.UI.COLUMN_WIDTHS
        else:
            # 保持原始定义
            column_widths = {
                "ID": 50, "STATE": 80, "Location": 120, "Quantity": 60,
                "Chair_Serial_No": 150, "Sim_Card_Model": 120, "Sim_Card_No": 120,
                "Layer": 80, "Company": 150, "Effective_From": 100, "Effective_To": 100,
                "Rental": 80, "SIMCARDID": 120, "Import_Date": 100, "Last_Updated": 120,
                "CurrentFlag": 80, "DATE": 100
            }
        for col in self.columns:
            # 初始显示ID降序箭头
            header_text = col
            if col == self.sort_column_name:
                header_text += ' ▼' if self.sort_direction == 'DESC' else ' ▲'
            self.tree.heading(col, text=header_text, command=lambda _col=col: self.sort_column(_col))
            self.tree.column(col, width=column_widths.get(col, 100), anchor="center")

        # --- 添加滚动条 ---
        vsb = ttk.Scrollbar(tree_frame, orient="vertical", command=self.tree.yview)
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        self.tree.pack(fill=tk.BOTH, expand=True)

        # --- 绑定事件 ---
        self.tree.bind("<Double-1>", self.on_tree_double_click)

    def filter_row_for_display(self, row):
        """过滤数据行，只返回可见列的数据"""
        if not hasattr(self, 'all_columns') or not hasattr(self, 'hidden_columns'):
            return row  # 如果没有定义隐藏列，返回原始数据

        filtered_row = []
        for i, value in enumerate(row):
            # 只包含可见列的数据
            if i < len(self.all_columns) and self.all_columns[i] not in self.hidden_columns:
                if value is None:
                    filtered_row.append("None")  # None值显示为"None"
                elif value == "":
                    filtered_row.append("")      # 空字符串保持为空
                else:
                    filtered_row.append(str(value))  # 其他值转为字符串
        return filtered_row

    def configure_modern_styles(self):
        """配置现代化组件样式"""
        # 主框架样式
        self.style.configure("Modern.TFrame",
                           background=self.colors['background'],
                           relief="flat",
                           borderwidth=0)

        # 卡片样式框架
        self.style.configure("Card.TFrame",
                           background=self.colors['surface'],
                           relief="solid",
                           borderwidth=1,
                           bordercolor=self.colors['border'])

        # 现代化标签样式
        self.style.configure("Modern.TLabel",
                           background=self.colors['surface'],
                           foreground=self.colors['text_primary'],
                           font=("Segoe UI", 10))

        self.style.configure("Title.TLabel",
                           background=self.colors['surface'],
                           foreground=self.colors['text_primary'],
                           font=("Segoe UI", 14, "bold"))

        self.style.configure("Subtitle.TLabel",
                           background=self.colors['surface'],
                           foreground=self.colors['text_secondary'],
                           font=("Segoe UI", 9))

        # 现代化按钮样式
        self.style.configure("Primary.TButton",
                           background=self.colors['primary'],
                           foreground="white",
                           font=("Segoe UI", 10),
                           borderwidth=0,
                           focuscolor="none",
                           padding=(12, 8))

        self.style.map("Primary.TButton",
                      background=[('active', '#0056CC'), ('pressed', '#004499')],
                      relief=[('pressed', 'flat'), ('!pressed', 'flat')])

        self.style.configure("Secondary.TButton",
                           background=self.colors['surface'],
                           foreground=self.colors['primary'],
                           font=("Segoe UI", 10),
                           borderwidth=1,
                           bordercolor=self.colors['primary'],
                           focuscolor="none",
                           padding=(12, 8))

        self.style.map("Secondary.TButton",
                      background=[('active', self.colors['primary']), ('pressed', '#0056CC')],
                      foreground=[('active', 'white'), ('pressed', 'white')],
                      relief=[('pressed', 'flat'), ('!pressed', 'flat')])

        # 现代化输入框样式
        self.style.configure("Modern.TEntry",
                           fieldbackground=self.colors['surface'],
                           borderwidth=1,
                           bordercolor=self.colors['border'],
                           focuscolor=self.colors['primary'],
                           font=("Segoe UI", 10),
                           padding=(8, 6))

        # 现代化下拉框样式
        self.style.configure("Modern.TCombobox",
                           fieldbackground=self.colors['surface'],
                           borderwidth=1,
                           bordercolor=self.colors['border'],
                           focuscolor=self.colors['primary'],
                           font=("Segoe UI", 10),
                           padding=(8, 6))

        # 现代化表格样式
        self.style.configure("Modern.Treeview",
                           background=self.colors['surface'],
                           foreground=self.colors['text_primary'],
                           fieldbackground=self.colors['surface'],
                           borderwidth=0,
                           font=("Segoe UI", 10),
                           rowheight=32)

        self.style.configure("Modern.Treeview.Heading",
                           background=self.colors['background'],
                           foreground=self.colors['text_primary'],
                           font=("Segoe UI", 10, "bold"),
                           borderwidth=1,
                           bordercolor=self.colors['border'],
                           relief="flat")

        self.style.map("Modern.Treeview",
                      background=[('selected', self.colors['primary'])],
                      foreground=[('selected', 'white')])

        # 现代化标签框架样式
        self.style.configure("Modern.TLabelFrame",
                           background=self.colors['surface'],
                           borderwidth=1,
                           bordercolor=self.colors['border'],
                           relief="solid")

        self.style.configure("Modern.TLabelFrame.Label",
                           background=self.colors['surface'],
                           foreground=self.colors['text_primary'],
                           font=("Segoe UI", 11, "bold"))

    def setup_window_appearance(self):
        """设置窗口外观"""
        # 设置窗口背景
        self.root.configure(bg=self.colors['background'])

        # 尝试设置窗口图标（如果有的话）
        try:
            # 这里可以设置自定义图标
            # self.root.iconbitmap("icon.ico")
            pass
        except Exception:
            pass

        # 设置窗口最小尺寸
        self.root.minsize(1200, 700)

        # 居中显示窗口
        self.center_window()

    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def record_operation(self, operation_type, before_data=None, after_data=None, affected_ids=None):
        """记录操作历史，用于撤回功能"""
        operation = {
            'type': operation_type,
            'timestamp': safe_get_current_timestamp(),
            'before_data': before_data,
            'after_data': after_data,
            'affected_ids': affected_ids or []
        }
        self.operation_history.append(operation)
        # 限制历史记录数量，避免内存过大
        if OPTIMIZATION_ENABLED and hasattr(Config, 'UI') and hasattr(Config.UI, 'OPERATION_HISTORY_LIMIT'):
            history_limit = Config.UI.OPERATION_HISTORY_LIMIT
        else:
            history_limit = 50
        if len(self.operation_history) > history_limit:
            self.operation_history.pop(0)

    def show_loading(self, message="正在加载..."):
        """显示加载指示器"""
        if hasattr(self, 'loading_label'):
            self.loading_label.destroy()

        self.loading_label = ttk.Label(self.main_frame, text=message,
                                      font=("Arial", 10, "italic"),
                                      foreground="blue")
        self.loading_label.pack(pady=5)
        self.root.update()

    def hide_loading(self):
        """隐藏加载指示器"""
        if hasattr(self, 'loading_label'):
            self.loading_label.destroy()
            delattr(self, 'loading_label')

    def validate_input(self, field_name, value):
        """增强的输入验证"""
        if not value:  # 空值检查
            return True, ""

        # 日期字段验证
        if field_name in ["Effective_From", "Effective_To"]:
            if not is_valid_date(value):
                return False, f"日期格式无效，应为 YYYY-MM-DD 格式"

        # 数字字段验证
        elif field_name == "Quantity":
            try:
                qty = int(value)
                if qty < 0:
                    return False, "数量不能为负数"
            except ValueError:
                return False, "数量必须为整数"


        # 序列号验证
        elif field_name == "Chair_Serial_No":
            if len(value.strip()) < 3:
                return False, "序列号长度不能少于3个字符"
            # 检查特殊字符
            import re
            if not re.match(r'^[A-Za-z0-9_-]+$', value):
                return False, "序列号只能包含字母、数字、下划线和连字符"

        # 文本长度验证
        elif field_name in ["STATE", "Location", "Company"]:
            if len(value) > 100:
                return False, f"{field_name} 长度不能超过100个字符"

        return True, ""

    def check_chair_serial_no_unique(self, chair_serial_no):
        """检查序列号是否已存在"""
        try:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM Equipment_ID WHERE Chair_Serial_No=?", (chair_serial_no,))
                count = cursor.fetchone()[0]
                return count > 0
        except Exception as e:
            print(f"Error checking serial number uniqueness: {e}")
            return False

    def set_effective_dates_for_duplicate(self, chair_serial_no, values, parent_win):
        """为重复序列号设置生效日期"""
        try:
            # 创建日期设置对话框
            date_win = tk.Toplevel(parent_win)
            date_win.title(f"设置重复序列号 {chair_serial_no} 的生效日期")
            date_win.geometry("400x300")
            date_win.grab_set()
            date_win.transient(parent_win)

            # 居中显示
            date_win.update_idletasks()
            x = (date_win.winfo_screenwidth() // 2) - (date_win.winfo_width() // 2)
            y = (date_win.winfo_screenheight() // 2) - (date_win.winfo_height() // 2)
            date_win.geometry(f"+{x}+{y}")

            main_frame = ttk.Frame(date_win, padding=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(main_frame, text=f"序列号 {chair_serial_no} 已存在",
                     font=("Arial", 12, "bold")).pack(pady=(0, 10))
            ttk.Label(main_frame, text="请设置生效日期以区分不同记录：").pack(pady=(0, 20))

            # 生效日期输入
            from_frame = ttk.Frame(main_frame)
            from_frame.pack(fill=tk.X, pady=5)
            ttk.Label(from_frame, text="生效日期 (YYYY-MM-DD):").pack(side=tk.LEFT)
            from_var = tk.StringVar()
            from_entry = ttk.Entry(from_frame, textvariable=from_var, width=15)
            from_entry.pack(side=tk.RIGHT)

            # 失效日期输入
            to_frame = ttk.Frame(main_frame)
            to_frame.pack(fill=tk.X, pady=5)
            ttk.Label(to_frame, text="失效日期 (YYYY-MM-DD):").pack(side=tk.LEFT)
            to_var = tk.StringVar()
            to_entry = ttk.Entry(to_frame, textvariable=to_var, width=15)
            to_entry.pack(side=tk.RIGHT)

            # 提示信息
            ttk.Label(main_frame, text="注意：日期格式为 YYYY-MM-DD，如 2023-01-01\n失效日期可以留空",
                     foreground="gray").pack(pady=10)

            result = {'confirmed': False}

            def confirm_dates():
                eff_from = from_var.get().strip()
                eff_to = to_var.get().strip()

                # 验证日期格式
                if eff_from and not is_valid_date(eff_from):
                    messagebox.showerror("错误", "生效日期格式无效", parent=date_win)
                    return
                if eff_to and not is_valid_date(eff_to):
                    messagebox.showerror("错误", "失效日期格式无效", parent=date_win)
                    return
                if eff_from and eff_to and eff_from > eff_to:
                    messagebox.showerror("错误", "生效日期不能晚于失效日期", parent=date_win)
                    return

                # 更新values中的日期字段
                values[8] = eff_from if eff_from else None  # Effective_From
                values[9] = eff_to if eff_to else None      # Effective_To
                result['confirmed'] = True
                date_win.destroy()

            def cancel_dates():
                result['confirmed'] = False
                date_win.destroy()

            # 按钮
            btn_frame = ttk.Frame(main_frame)
            btn_frame.pack(pady=20)
            ttk.Button(btn_frame, text="确认", command=confirm_dates).pack(side=tk.LEFT, padx=10)
            ttk.Button(btn_frame, text="跳过", command=cancel_dates).pack(side=tk.LEFT, padx=10)

            # 等待用户操作
            date_win.wait_window()
            return result['confirmed']

        except Exception as e:
            print(f"Error in set_effective_dates_for_duplicate: {e}")
            return False

    def setup_basic_shortcuts(self):
        """设置基础快捷键"""
        shortcuts = [
            # 文件操作
            ('<Control-n>', self.add_new_id, '新建设备记录'),
            ('<Control-o>', self.import_from_excel, '导入Excel数据'),
            ('<Control-s>', self.export_to_excel, '导出到Excel'),

            # 编辑操作
            ('<Control-e>', self.edit_selected_id, '编辑选中记录'),
            ('<Delete>', self.delete_selected_id, '删除选中记录'),
            ('<Control-d>', self.batch_delete_selected, '批量删除选中记录'),
            ('<Control-z>', self.undo_operation, '撤销上一步操作'),

            # 查看操作
            ('<F5>', self.refresh_data, '刷新数据'),
            ('<Control-f>', self.focus_search, '聚焦搜索框'),
            ('<Escape>', self.clear_search, '清除搜索'),
            ('<Control-r>', self.reset_search, '重置搜索'),

            # 导航操作
            ('<Control-Home>', self.go_to_first_page, '跳转到首页'),
            ('<Control-End>', self.go_to_last_page, '跳转到末页'),
            ('<Control-Left>', self.prev_page, '上一页'),
            ('<Control-Right>', self.next_page, '下一页'),
        ]

        for key, callback, desc in shortcuts:
            try:
                self.root.bind(key, lambda e, cb=callback: cb())
                print(f"✅ 快捷键: {key} - {desc}")
            except Exception as e:
                print(f"⚠️ 快捷键设置失败 {key}: {e}")

    def focus_search(self):
        """聚焦搜索框"""
        if hasattr(self, 'serial_var'):
            # 找到序列号输入框并聚焦
            for widget in self.main_frame.winfo_children():
                if isinstance(widget, ttk.LabelFrame) and "搜索" in widget.cget("text"):
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Frame):
                            for entry in child.winfo_children():
                                if isinstance(entry, ttk.Entry):
                                    entry.focus_set()
                                    return

    def clear_search(self):
        """清除搜索条件"""
        if hasattr(self, 'serial_var'):
            self.serial_var.set("")
        if hasattr(self, 'location_var'):
            self.location_var.set("")
        if hasattr(self, 'state_var'):
            self.state_var.set("")
        # 刷新数据
        self.refresh_data()

    def reset_search(self):
        """重置搜索条件"""
        self.clear_search()

    def go_to_first_page(self):
        """跳转到首页"""
        self.current_page = 1
        self.load_data(self.current_query, self.current_params)
        self.update_page_info()

    def go_to_last_page(self):
        """跳转到末页"""
        if self.total_pages > 0:
            self.current_page = self.total_pages
            self.load_data(self.current_query, self.current_params)
            self.update_page_info()

    def update_status_bar_info(self):
        """更新状态栏信息"""
        try:
            # 更新缓存统计
            if self.safe_db is not None:
                cache_stats = self.safe_db.get_cache_stats()
                cache_text = f"缓存: {cache_stats['hit_count']}/{cache_stats['hit_count'] + cache_stats['miss_count']}"
                if cache_stats['hit_count'] + cache_stats['miss_count'] > 0:
                    cache_text += f" ({cache_stats['hit_rate']})"
                self.cache_status_var.set(cache_text)

            # 更新记录统计
            if hasattr(self, 'total_records') and hasattr(self, 'current_page'):
                current_showing = min(self.page_size, self.total_records - (self.current_page - 1) * self.page_size)
                if current_showing < 0:
                    current_showing = 0
                self.record_status_var.set(f"记录: {current_showing}/{self.total_records}")

        except Exception as e:
            print(f"更新状态栏信息失败: {e}")

        # 每5秒更新一次
        self.root.after(5000, self.update_status_bar_info)

    def update_performance_info(self, execution_time):
        """更新性能信息"""
        try:
            if hasattr(self, 'perf_status_var'):
                self.perf_status_var.set(f"查询: {execution_time:.3f}s")
        except Exception as e:
            print(f"更新性能信息失败: {e}")

    def show_async_progress(self, operation_id, title="异步操作进行中"):
        """显示异步操作进度窗口"""
        progress_win = tk.Toplevel(self.root)
        progress_win.title(title)
        progress_win.geometry("400x200")
        progress_win.grab_set()
        progress_win.transient(self.root)

        # 进度信息
        info_frame = ttk.Frame(progress_win, padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True)

        status_var = tk.StringVar()
        status_var.set("正在处理...")
        ttk.Label(info_frame, textvariable=status_var, font=("Arial", 10)).pack(pady=10)

        # 进度条
        progress_bar = ttk.Progressbar(info_frame, mode='indeterminate')
        progress_bar.pack(fill=tk.X, pady=10)
        progress_bar.start()

        # 按钮框架
        btn_frame = ttk.Frame(info_frame)
        btn_frame.pack(fill=tk.X, pady=10)

        cancel_btn = ttk.Button(btn_frame, text="取消",
                               command=lambda: self.cancel_async_operation(operation_id, progress_win))
        cancel_btn.pack(side=tk.RIGHT, padx=5)

        # 定期检查操作状态
        def check_status():
            status = async_manager.get_operation_status(operation_id)
            if status:
                if status['done']:
                    progress_bar.stop()
                    if status.get('success', False):
                        status_var.set("操作完成！")
                        cancel_btn.config(text="关闭", command=progress_win.destroy)
                        # 强制刷新数据
                        self.force_refresh_interface()
                        # 2秒后自动关闭
                        progress_win.after(2000, progress_win.destroy)
                    else:
                        status_var.set(f"操作失败: {status.get('error', '未知错误')}")
                        cancel_btn.config(text="关闭", command=progress_win.destroy)
                else:
                    elapsed = status['elapsed_time']
                    status_var.set(f"正在处理... ({elapsed:.1f}秒)")
                    progress_win.after(500, check_status)
            else:
                status_var.set("操作状态未知")
                cancel_btn.config(text="关闭", command=progress_win.destroy)

        # 开始检查状态
        progress_win.after(100, check_status)

        return progress_win

    def cancel_async_operation(self, operation_id, progress_win):
        """取消异步操作"""
        if async_manager.cancel_operation(operation_id):
            safe_show_info("操作已取消")
        else:
            safe_show_info("无法取消操作（可能已完成）")
        progress_win.destroy()

    def _async_batch_delete(self, ids_to_delete, details_to_log, description="批量删除"):
        """异步批量删除操作"""
        try:
            # 使用优化的批量删除操作
            delete_result = self.safe_db.batch_delete_optimized(ids_to_delete, "async_batch_delete_equipment")

            if delete_result.success:
                deleted_records = delete_result.data
                deleted_count = len(deleted_records)

                # 记录操作历史
                if deleted_records:
                    self.record_operation('BATCH_DELETE', before_data=deleted_records, affected_ids=ids_to_delete)
                    self.deleted_records.extend(deleted_records)  # 保持向后兼容

                # 记录日志
                log_desc = f"异步批量删除 {deleted_count} 条设备: " + "; ".join(details_to_log)
                add_log_entry("异步批量删除", log_desc, str(ids_to_delete))

                # 返回成功结果
                execution_time = delete_result.execution_time
                return {
                    'success': True,
                    'deleted_count': deleted_count,
                    'execution_time': execution_time,
                    'message': f"✅ 成功删除 {deleted_count} 条记录\n⚡ 耗时: {execution_time:.3f}秒\n💡 可以使用Ctrl+Z撤销此操作"
                }
            else:
                return {
                    'success': False,
                    'error': delete_result.error_message
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def setup_ui(self):
        # 主容器
        self.main_frame = ttk.Frame(self.root, padding=10)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建友好的标题区域（显示数据库位置）
        self.create_friendly_header()

        # 创建工具栏
        self.create_toolbar()

        # 创建搜索面板
        self.create_search_panel_simple()

        # 创建数据表格
        self.create_treeview_simple()

        # 创建状态栏
        self.create_status_bar()

    def create_modern_menu(self):
        """创建现代化菜单栏"""
        menubar = tk.Menu(self.root, bg=self.colors['surface'], fg=self.colors['text_primary'],
                         activebackground=self.colors['primary'], activeforeground='white')
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0, bg=self.colors['surface'], fg=self.colors['text_primary'],
                           activebackground=self.colors['primary'], activeforeground='white')
        menubar.add_cascade(label="📁 文件", menu=file_menu)
        file_menu.add_command(label="🔧 设置数据库路径", command=self.select_db_path)
        file_menu.add_separator()
        file_menu.add_command(label="❌ 退出", command=self.root.quit)

        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0, bg=self.colors['surface'], fg=self.colors['text_primary'],
                           activebackground=self.colors['primary'], activeforeground='white')
        menubar.add_cascade(label="✏️ 编辑", menu=edit_menu)
        edit_menu.add_command(label="➕ 新建记录 (Ctrl+N)", command=self.add_new_id)
        edit_menu.add_command(label="✏️ 编辑记录 (Ctrl+E)", command=self.edit_selected_id)
        edit_menu.add_command(label="🗑️ 删除记录 (Delete)", command=self.delete_selected_id)
        edit_menu.add_separator()
        edit_menu.add_command(label="↩️ 撤销操作 (Ctrl+Z)", command=self.undo_operation)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0, bg=self.colors['surface'], fg=self.colors['text_primary'],
                            activebackground=self.colors['primary'], activeforeground='white')
        menubar.add_cascade(label="🔧 工具", menu=tools_menu)
        tools_menu.add_command(label="📥 导入Excel (Ctrl+O)", command=self.import_from_excel)
        tools_menu.add_command(label="📤 导出Excel (Ctrl+S)", command=self.export_to_excel)
        tools_menu.add_separator()
        tools_menu.add_command(label="🔍 查找空值", command=self.find_empty_value)
        tools_menu.add_command(label="⏰ 查看过期设备", command=self.view_expired)
        tools_menu.add_command(label="⚠️ 查找异常", command=self.find_duplicate_serial)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0, bg=self.colors['surface'], fg=self.colors['text_primary'],
                           activebackground=self.colors['primary'], activeforeground='white')
        menubar.add_cascade(label="❓ 帮助", menu=help_menu)
        help_menu.add_command(label="⌨️ 快捷键说明", command=self.show_shortcuts_help)
        help_menu.add_command(label="📊 性能报告", command=self.show_performance_report)
        help_menu.add_separator()
        help_menu.add_command(label="ℹ️ 关于", command=self.show_about)

    def show_shortcuts_help(self):
        """显示快捷键帮助"""
        help_text = """
🎯 快捷键说明

📁 文件操作:
• Ctrl+N - 新建设备记录
• Ctrl+O - 导入Excel数据
• Ctrl+S - 导出到Excel

✏️ 编辑操作:
• Ctrl+E - 编辑选中记录
• Delete - 删除选中记录
• Ctrl+D - 批量删除选中记录
• Ctrl+Z - 撤销上一步操作

🔍 查看操作:
• F5 - 刷新数据
• Ctrl+F - 聚焦搜索框
• Escape - 清除搜索
• Ctrl+R - 重置搜索

🧭 导航操作:
• Ctrl+Home - 跳转到首页
• Ctrl+End - 跳转到末页
• Ctrl+Left - 上一页
• Ctrl+Right - 下一页

💡 提示: 双击表格行可以快速编辑记录
        """
        safe_show_info(help_text, "快捷键帮助")

    def show_performance_report(self):
        """显示性能报告"""
        if self.safe_db:
            report = self.safe_db.get_performance_report()
            cache_stats = self.safe_db.get_cache_stats()

            full_report = f"{report}\n\n📋 缓存统计:\n"
            full_report += f"命中次数: {cache_stats['hit_count']}\n"
            full_report += f"未命中次数: {cache_stats['miss_count']}\n"
            full_report += f"命中率: {cache_stats['hit_rate']}\n"
            full_report += f"缓存大小: {cache_stats['cache_size']} 项"

            # 创建性能报告窗口
            report_win = tk.Toplevel(self.root)
            report_win.title("📊 性能报告")
            report_win.geometry("600x500")
            report_win.grab_set()

            # 文本框显示报告
            text_frame = ttk.Frame(report_win, padding=10)
            text_frame.pack(fill=tk.BOTH, expand=True)

            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            text_widget.insert(tk.END, full_report)
            text_widget.config(state=tk.DISABLED)

            # 关闭按钮
            ttk.Button(report_win, text="关闭", command=report_win.destroy).pack(pady=10)
        else:
            safe_show_info("性能监控功能未启用", "性能报告")

    def show_about(self):
        """显示关于信息"""
        about_text = """
🏢 设备ID管理系统 v2.0

企业级设备管理解决方案

✨ 主要功能:
• 设备信息管理
• 批量操作支持
• 智能搜索功能
• 异步操作处理
• 性能监控和缓存
• 数据导入导出
• 操作历史和撤销

🚀 性能特性:
• 智能缓存机制 (5分钟缓存)
• 批量操作优化 (90%性能提升)
• 异步处理 (大批量操作不阻塞界面)
• 15个快捷键支持

🛡️ 安全特性:
• 完整的输入验证
• 事务管理和回滚
• 操作历史记录
• 自动备份机制

💻 技术架构:
• Python + Tkinter
• SQLite数据库
• 现代化UI设计
• 企业级代码标准

© 2025 设备管理系统
        """
        safe_show_info(about_text, "关于系统")

    def update_smart_suggestions(self, search_type):
        """更新智能搜索建议"""
        try:
            if search_type == 'serial':
                partial_value = self.serial_var.get()
                suggestions = smart_search.get_search_suggestions(search_type, partial_value)
                self.serial_combo['values'] = suggestions
            elif search_type == 'location':
                partial_value = self.location_var.get()
                suggestions = smart_search.get_search_suggestions(search_type, partial_value)
                self.location_combo['values'] = suggestions
            elif search_type == 'state':
                partial_value = self.state_var.get()
                suggestions = smart_search.get_search_suggestions(search_type, partial_value)
                self.state_combo['values'] = suggestions
        except Exception as e:
            print(f"更新搜索建议失败: {e}")

    def show_search_history(self, search_type):
        """显示搜索历史"""
        try:
            history = smart_search.get_search_history(search_type, limit=10)
            history_values = [item['value'] for item in history]

            if search_type == 'serial':
                self.serial_combo['values'] = history_values
            elif search_type == 'location':
                self.location_combo['values'] = history_values
            elif search_type == 'state':
                self.state_combo['values'] = history_values
        except Exception as e:
            print(f"显示搜索历史失败: {e}")

    def smart_search_data(self, reset_page=False):
        """智能搜索数据"""
        # 记录搜索历史
        serial = self.serial_var.get().strip()
        location = self.location_var.get().strip()
        state = self.state_var.get().strip()

        if serial:
            smart_search.add_search_history('serial', serial)
        if location:
            smart_search.add_search_history('location', location)
        if state:
            smart_search.add_search_history('state', state)

        # 执行搜索
        self.search_data(reset_page)

    def show_search_history_window(self):
        """显示搜索历史窗口"""
        history_win = tk.Toplevel(self.root)
        history_win.title("📜 搜索历史")
        history_win.geometry("500x400")
        history_win.grab_set()

        # 创建标签页
        notebook = ttk.Notebook(history_win)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 为每种搜索类型创建标签页
        for search_type, title in [('serial', '序列号'), ('location', '位置'), ('state', '状态')]:
            frame = ttk.Frame(notebook)
            notebook.add(frame, text=title)

            # 历史列表
            listbox = tk.Listbox(frame, font=("Segoe UI", 10))
            listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            # 获取历史记录
            history = smart_search.get_search_history(search_type, limit=50)
            for item in history:
                listbox.insert(tk.END, f"{item['value']} (使用{item['count']}次)")

            # 按钮框架
            btn_frame = ttk.Frame(frame)
            btn_frame.pack(fill=tk.X, padx=5, pady=5)

            ttk.Button(btn_frame, text="清除历史",
                      command=lambda st=search_type: self.clear_search_history(st, history_win)).pack(side=tk.LEFT)

        # 关闭按钮
        ttk.Button(history_win, text="关闭", command=history_win.destroy).pack(pady=5)

    def clear_search_history(self, search_type, parent_win):
        """清除搜索历史"""
        if safe_ask_yes_no(f"确定要清除{search_type}的搜索历史吗？", "确认清除"):
            smart_search.clear_history(search_type)
            parent_win.destroy()
            safe_show_info("搜索历史已清除")

    def update_search_suggestions(self):
        """更新搜索建议缓存"""
        try:
            smart_search.update_database_suggestions(DB_PATH)
        except Exception as e:
            print(f"更新搜索建议失败: {e}")

    def create_header(self):
        """创建现代化标题区域"""
        header_frame = ttk.Frame(self.main_frame, padding=15)
        header_frame.pack(fill=tk.X, pady=(0, 15))

        # 主标题
        title_label = ttk.Label(header_frame, text="🏢 设备ID管理系统", font=("Segoe UI", 16, "bold"))
        title_label.pack(side=tk.LEFT)

        # 副标题
        subtitle_label = ttk.Label(header_frame, text="企业级设备管理解决方案", font=("Segoe UI", 10))
        subtitle_label.pack(side=tk.LEFT, padx=(10, 0))

        # 右侧状态指示器
        status_frame = ttk.Frame(header_frame)
        status_frame.pack(side=tk.RIGHT)

        # 在线状态指示器
        online_indicator = ttk.Label(status_frame, text="🟢 在线", font=("Segoe UI", 10))
        online_indicator.pack(side=tk.RIGHT, padx=(0, 10))

    def create_modern_toolbar(self):
        """创建现代化工具栏 - 使用卡片式设计和现代化按钮"""
        # 创建工具栏卡片
        toolbar_card, toolbar_content = self.create_modern_card(self.main_frame, "🛠️ 操作工具栏")
        toolbar_card.pack(fill=tk.X, pady=(0, 15))

        # 主要操作区域
        primary_section = tk.Frame(toolbar_content, bg=self.colors['surface'])
        primary_section.pack(fill=tk.X, pady=(0, 10))

        # 主要操作标题
        primary_title = tk.Label(
            primary_section,
            text="📝 基础操作",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=("Segoe UI", 9, "bold")
        )
        primary_title.pack(anchor=tk.W, pady=(0, 8))

        # 主要操作按钮容器
        primary_buttons_frame = tk.Frame(primary_section, bg=self.colors['surface'])
        primary_buttons_frame.pack(fill=tk.X)

        # 主要操作按钮
        primary_buttons = [
            ("➕ 新建", self.add_new_id, "primary", "🆕"),
            ("✏️ 编辑", self.edit_selected_id, "secondary", "✏️"),
            ("🗑️ 删除", self.delete_selected_id, "danger", "🗑️"),
        ]

        for text, command, style, icon in primary_buttons:
            btn = self.create_modern_button(primary_buttons_frame, text, command, style, width=12, icon=icon)
            btn.pack(side=tk.LEFT, padx=(0, 10))

        # 分割线
        separator1 = tk.Frame(toolbar_content, bg=self.colors['divider'], height=1)
        separator1.pack(fill=tk.X, pady=10)

        # 批量操作区域
        batch_section = tk.Frame(toolbar_content, bg=self.colors['surface'])
        batch_section.pack(fill=tk.X, pady=(0, 10))

        # 批量操作标题
        batch_title = tk.Label(
            batch_section,
            text="📦 批量操作",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=("Segoe UI", 9, "bold")
        )
        batch_title.pack(anchor=tk.W, pady=(0, 8))

        # 批量操作按钮容器
        batch_buttons_frame = tk.Frame(batch_section, bg=self.colors['surface'])
        batch_buttons_frame.pack(fill=tk.X)

        # 批量操作按钮
        batch_buttons = [
            ("📦 批量删除", self.batch_delete_selected, "warning", "📦"),
            ("📝 批量编辑", self.batch_edit_selected, "secondary", "📝"),
            ("↩️ 撤销", self.undo_operation, "outline", "↩️"),
        ]

        for text, command, style, icon in batch_buttons:
            btn = self.create_modern_button(batch_buttons_frame, text, command, style, width=12, icon=icon)
            btn.pack(side=tk.LEFT, padx=(0, 10))

        # 分割线
        separator2 = tk.Frame(toolbar_content, bg=self.colors['divider'], height=1)
        separator2.pack(fill=tk.X, pady=10)

        # 数据操作区域
        data_section = tk.Frame(toolbar_content, bg=self.colors['surface'])
        data_section.pack(fill=tk.X, pady=(0, 10))

        # 数据操作标题
        data_title = tk.Label(
            data_section,
            text="📊 数据管理",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=("Segoe UI", 9, "bold")
        )
        data_title.pack(anchor=tk.W, pady=(0, 8))

        # 数据操作按钮容器
        data_buttons_frame = tk.Frame(data_section, bg=self.colors['surface'])
        data_buttons_frame.pack(side=tk.LEFT)

        # 数据操作按钮
        data_buttons = [
            ("📥 导入", self.import_from_excel, "success", "📥"),
            ("📤 导出", self.export_to_excel, "success", "📤"),
        ]

        for text, command, style, icon in data_buttons:
            btn = self.create_modern_button(data_buttons_frame, text, command, style, width=12, icon=icon)
            btn.pack(side=tk.LEFT, padx=(0, 10))

        # 右侧工具区域
        tools_section = tk.Frame(data_section, bg=self.colors['surface'])
        tools_section.pack(side=tk.RIGHT)

        # 工具操作标题
        tools_title = tk.Label(
            tools_section,
            text="🔧 工具",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=("Segoe UI", 9, "bold")
        )
        tools_title.pack(anchor=tk.W, pady=(0, 8))

        # 工具按钮容器
        tools_buttons_frame = tk.Frame(tools_section, bg=self.colors['surface'])
        tools_buttons_frame.pack()

        # 工具按钮
        tool_buttons = [
            ("⏰ 过期设备", self.view_expired, "outline", "⏰"),
            ("🔍 查找空值", self.find_empty_value, "outline", "🔍"),
            ("⚠️ 查找异常", self.find_duplicate_serial, "outline", "⚠️"),
            ("📋 所有记录", self.view_all_records, "outline", "📋"),
        ]

        for text, command, style, icon in tool_buttons:
            btn = self.create_modern_button(tools_buttons_frame, text, command, style, width=10, icon=icon)
            btn.pack(side=tk.LEFT, padx=(0, 5))

    def select_db_path(self):
        new_path = filedialog.askopenfilename(
            title="选择数据库文件",
            filetypes=[("SQLite数据库", "*.db"), ("所有文件", "*.*")],
            initialdir=os.path.dirname(DB_PATH)
        )
        if new_path and new_path != DB_PATH:
            if os.path.isfile(new_path) and new_path.endswith('.db'):
                save_config(new_path)
                safe_show_info(f"数据库路径已更新为: {new_path}\n请重新启动应用以生效。", "成功")
                self.root.quit() # 简单起见，直接退出让用户重启
            else:
                safe_show_error("选择的文件无效或不是.db文件。")

    def create_toolbar(self):
        toolbar = ttk.LabelFrame(self.main_frame, text="功能区", padding=10)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        btn_frame = ttk.Frame(toolbar)
        btn_frame.pack(fill=tk.X)
        ttk.Button(btn_frame, text="添加设备", width=12, command=self.add_new_id).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="编辑设备", width=12, command=self.edit_selected_id).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="批量编辑 (Ctrl+E)", width=15, command=self.batch_edit_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="删除设备", width=12, command=self.delete_selected_id).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="批量删除", width=12, command=self.batch_delete_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="撤销操作 (Ctrl+Z)", width=15, command=self.undo_operation).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="导入Excel", width=12, command=self.import_from_excel).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="导出Excel", width=12, command=self.export_to_excel).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="🗃️ 数据库管理", width=14, command=self.show_database_manager).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="查找空值", width=12, command=self.find_empty_value).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="查看过期设备", width=14, command=self.view_expired).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="未匹配销售记录", width=16, command=self.query_unmatched_serial).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="导出异常序列号", width=16, command=self.export_abnormal_by_date).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="查找重复序列号", width=16, command=self.find_duplicate_serial).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="查看所有记录", width=14, command=self.view_all_records).pack(side=tk.LEFT, padx=5)
        
        # 添加快捷键绑定
        self.root.bind("<Control-z>", lambda e: self.undo_operation())
        self.root.bind("<Control-Z>", lambda e: self.undo_operation())  # 处理大写Z的情况
        self.root.bind("<Control-e>", lambda e: self.batch_edit_selected())

    def create_search_panel(self):
        """创建现代化搜索面板 - 使用卡片式设计"""
        # 创建搜索卡片
        search_card, search_content = self.create_modern_card(self.main_frame, "🔍 智能搜索")
        search_card.pack(fill=tk.X, pady=(0, 15))

        # 搜索字段容器
        fields_container = tk.Frame(search_content, bg=self.colors['surface'])
        fields_container.pack(fill=tk.X, pady=(0, 15))

        # 搜索字段区域
        fields_frame = tk.Frame(fields_container, bg=self.colors['surface'])
        fields_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 序列号搜索
        serial_frame = tk.Frame(fields_frame, bg=self.colors['surface'])
        serial_frame.pack(side=tk.LEFT, padx=(0, 20))

        serial_label = tk.Label(
            serial_frame,
            text="🔢 序列号:",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=("Segoe UI", 9, "bold")
        )
        serial_label.pack(anchor=tk.W, pady=(0, 5))

        self.serial_var = tk.StringVar()
        self.serial_combo = ttk.Combobox(serial_frame, textvariable=self.serial_var, width=20, font=("Segoe UI", 9))
        self.serial_combo.pack()
        self.serial_combo.bind('<KeyRelease>', lambda e: self.update_smart_suggestions('serial'))
        self.serial_combo.bind('<FocusIn>', lambda e: self.show_search_history('serial'))

        # 位置搜索
        location_frame = tk.Frame(fields_frame, bg=self.colors['surface'])
        location_frame.pack(side=tk.LEFT, padx=(0, 20))

        location_label = tk.Label(
            location_frame,
            text="📍 位置:",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=("Segoe UI", 9, "bold")
        )
        location_label.pack(anchor=tk.W, pady=(0, 5))

        self.location_var = tk.StringVar()
        self.location_combo = ttk.Combobox(location_frame, textvariable=self.location_var, width=20, font=("Segoe UI", 9))
        self.location_combo.pack()
        self.location_combo.bind('<KeyRelease>', lambda e: self.update_smart_suggestions('location'))
        self.location_combo.bind('<FocusIn>', lambda e: self.show_search_history('location'))

        # 状态搜索
        state_frame = tk.Frame(fields_frame, bg=self.colors['surface'])
        state_frame.pack(side=tk.LEFT, padx=(0, 20))

        state_label = tk.Label(
            state_frame,
            text="📊 状态:",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=("Segoe UI", 9, "bold")
        )
        state_label.pack(anchor=tk.W, pady=(0, 5))

        self.state_var = tk.StringVar()
        self.state_combo = ttk.Combobox(state_frame, textvariable=self.state_var, width=20, font=("Segoe UI", 9))
        self.state_combo.pack()
        self.state_combo.bind('<KeyRelease>', lambda e: self.update_smart_suggestions('state'))
        self.state_combo.bind('<FocusIn>', lambda e: self.show_search_history('state'))

        # 右侧操作区域
        actions_frame = tk.Frame(fields_container, bg=self.colors['surface'])
        actions_frame.pack(side=tk.RIGHT, padx=(20, 0))

        # 搜索按钮组
        button_section = tk.Frame(actions_frame, bg=self.colors['surface'])
        button_section.pack(anchor=tk.E)

        actions_label = tk.Label(
            button_section,
            text="⚡ 操作:",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=("Segoe UI", 9, "bold")
        )
        actions_label.pack(anchor=tk.W, pady=(0, 5))

        btn_container = tk.Frame(button_section, bg=self.colors['surface'])
        btn_container.pack()

        # 使用现代化按钮
        search_btn = self.create_modern_button(btn_container, "搜索", lambda: self.smart_search_data(reset_page=True), "primary", icon="🔍")
        search_btn.pack(side=tk.LEFT, padx=(0, 8))

        clear_btn = self.create_modern_button(btn_container, "清空", self.clear_search, "outline", icon="🗑️")
        clear_btn.pack(side=tk.LEFT, padx=(0, 8))

        history_btn = self.create_modern_button(btn_container, "历史", self.show_search_history_window, "outline", icon="📜")
        history_btn.pack(side=tk.LEFT)

        # 统计信息区域
        stats_section = tk.Frame(actions_frame, bg=self.colors['surface'])
        stats_section.pack(anchor=tk.E, pady=(15, 0))

        stats_label = tk.Label(
            stats_section,
            text="📈 统计:",
            bg=self.colors['surface'],
            fg=self.colors['text_secondary'],
            font=("Segoe UI", 9, "bold")
        )
        stats_label.pack(anchor=tk.W, pady=(0, 5))

        self.total_count_var = tk.StringVar()
        stats_value = tk.Label(
            stats_section,
            textvariable=self.total_count_var,
            bg=self.colors['surface'],
            fg=self.colors['primary'],
            font=("Segoe UI", 10, "bold")
        )
        stats_value.pack()

        # 初始化智能搜索建议
        self.update_search_suggestions()

    def create_treeview(self):
        """创建现代化数据表格 - 使用卡片式设计和现代化样式"""
        # 创建表格卡片
        table_card, table_content = self.create_modern_card(self.main_frame, "📊 设备数据")
        table_card.pack(fill=tk.BOTH, expand=True)

        # 表格容器
        tree_container = tk.Frame(table_content, bg=self.colors['surface'])
        tree_container.pack(fill=tk.BOTH, expand=True)

        # 定义所有列（包括隐藏的列）
        self.all_columns = ("ID", "STATE", "Location", "Quantity", "Chair_Serial_No",
                           "Sim_Card_Model", "Sim_Card_No", "Layer", "Company",
                           "Effective_From", "Effective_To", "Rental", "SIMCARDID",
                           "Import_Date", "Last_Updated", "CurrentFlag", "DATE")

        # 定义要隐藏的列
        self.hidden_columns = ("Sim_Card_Model", "Sim_Card_No", "CurrentFlag", "SIMCARDID")

        # 定义显示的列（排除隐藏的列）
        self.columns = tuple(col for col in self.all_columns if col not in self.hidden_columns)

        # 创建现代化表格样式
        self.configure_treeview_style()

        self.tree = ttk.Treeview(tree_container, columns=self.columns, show="headings", style="Modern.Treeview")

        # --- 设置列宽和对齐 ---
        if OPTIMIZATION_ENABLED and hasattr(Config, 'UI') and hasattr(Config.UI, 'COLUMN_WIDTHS'):
            column_widths = Config.UI.COLUMN_WIDTHS
        else:
            # 保持原始定义
            column_widths = {
                "ID": 50, "STATE": 80, "Location": 120, "Quantity": 60,
                "Chair_Serial_No": 150, "Sim_Card_Model": 120, "Sim_Card_No": 120,
                "Layer": 80, "Company": 150, "Effective_From": 100, "Effective_To": 100,
                "Rental": 80, "SIMCARDID": 120, "Import_Date": 100, "Last_Updated": 120,
                "CurrentFlag": 80, "DATE": 100
            }

        # 列标题映射（更友好的显示名称）
        column_headers = {
            "ID": "🆔 ID",
            "STATE": "📊 状态",
            "Location": "📍 位置",
            "Quantity": "🔢 数量",
            "Chair_Serial_No": "🔢 序列号",
            "Sim_Card_Model": "📱 SIM卡型号",
            "Sim_Card_No": "📱 SIM卡号",
            "Layer": "📶 层级",
            "Company": "🏢 公司",
            "Effective_From": "📅 生效日期",
            "Effective_To": "📅 失效日期",
            "Rental": "💰 租金",
            "SIMCARDID": "🆔 SIM卡ID",
            "Import_Date": "📥 导入日期",
            "Last_Updated": "🔄 更新时间",
            "CurrentFlag": "🚩 当前标志",
            "DATE": "📅 日期"
        }

        for col in self.columns:
            # 使用友好的列标题
            header_text = column_headers.get(col, col)
            if col == self.sort_column_name:
                header_text += ' ▼' if self.sort_direction == 'DESC' else ' ▲'
            self.tree.heading(col, text=header_text, command=lambda _col=col: self.sort_column(_col))
            self.tree.column(col, width=column_widths.get(col, 100), anchor="center")

        # --- 添加现代化滚动条 ---
        vsb = ttk.Scrollbar(tree_container, orient="vertical", command=self.tree.yview, style="Modern.Vertical.TScrollbar")
        hsb = ttk.Scrollbar(tree_container, orient="horizontal", command=self.tree.xview, style="Modern.Horizontal.TScrollbar")
        self.tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)

        # 布局滚动条
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        self.tree.pack(fill=tk.BOTH, expand=True)

        # --- 绑定事件 ---
        self.tree.bind("<Double-1>", self.on_tree_double_click)

        # 添加行悬停效果
        self.tree.bind("<Motion>", self.on_tree_motion)
        self.tree.bind("<Leave>", self.on_tree_leave)

    def configure_treeview_style(self):
        """配置现代化表格样式"""
        # 表格样式
        self.style.configure("Modern.Treeview",
                           background=self.colors['surface'],
                           foreground=self.colors['text_primary'],
                           fieldbackground=self.colors['surface'],
                           borderwidth=0,
                           relief="flat",
                           font=("Segoe UI", 9))

        # 表格标题样式
        self.style.configure("Modern.Treeview.Heading",
                           background=self.colors['surface_variant'],
                           foreground=self.colors['text_primary'],
                           borderwidth=1,
                           relief="flat",
                           font=("Segoe UI", 9, "bold"))

        # 悬停效果
        self.style.map("Modern.Treeview",
                      background=[('selected', self.colors['selected'])],
                      foreground=[('selected', self.colors['text_primary'])])

        self.style.map("Modern.Treeview.Heading",
                      background=[('active', self.colors['hover'])],
                      foreground=[('active', self.colors['text_primary'])])

        # 滚动条样式
        self.style.configure("Modern.Vertical.TScrollbar",
                           background=self.colors['surface_variant'],
                           troughcolor=self.colors['background'],
                           borderwidth=0,
                           arrowcolor=self.colors['text_secondary'],
                           darkcolor=self.colors['surface_variant'],
                           lightcolor=self.colors['surface_variant'])

        self.style.configure("Modern.Horizontal.TScrollbar",
                           background=self.colors['surface_variant'],
                           troughcolor=self.colors['background'],
                           borderwidth=0,
                           arrowcolor=self.colors['text_secondary'],
                           darkcolor=self.colors['surface_variant'],
                           lightcolor=self.colors['surface_variant'])

    def on_tree_motion(self, event):
        """表格行悬停效果"""
        try:
            item = self.tree.identify_row(event.y)
            if item and item != getattr(self, '_last_hover_item', None):
                # 清除之前的悬停效果
                if hasattr(self, '_last_hover_item') and self._last_hover_item:
                    self.tree.set(self._last_hover_item, '', '')

                # 设置当前悬停效果
                self._last_hover_item = item
                # 这里可以添加更多悬停效果
        except:
            pass

    def on_tree_leave(self, event):
        """鼠标离开表格时清除悬停效果"""
        try:
            if hasattr(self, '_last_hover_item') and self._last_hover_item:
                self._last_hover_item = None
        except:
            pass

    def create_status_bar(self):
        status_frame = ttk.Frame(self.root, padding=(5, 2))
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # 主状态信息
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, anchor="w")
        status_label.pack(side=tk.LEFT, expand=True, fill=tk.X)

        # 分隔符
        ttk.Separator(status_frame, orient=tk.VERTICAL).pack(side=tk.RIGHT, fill=tk.Y, padx=2)

        # 缓存统计
        self.cache_status_var = tk.StringVar()
        self.cache_status_var.set("缓存: 0/0")
        cache_label = ttk.Label(status_frame, textvariable=self.cache_status_var, anchor=tk.CENTER, width=12, font=("Arial", 8))
        cache_label.pack(side=tk.RIGHT, padx=2)

        # 分隔符
        ttk.Separator(status_frame, orient=tk.VERTICAL).pack(side=tk.RIGHT, fill=tk.Y, padx=2)

        # 性能信息
        self.perf_status_var = tk.StringVar()
        self.perf_status_var.set("查询: 0.000s")
        perf_label = ttk.Label(status_frame, textvariable=self.perf_status_var, anchor=tk.CENTER, width=12, font=("Arial", 8))
        perf_label.pack(side=tk.RIGHT, padx=2)

        # 分隔符
        ttk.Separator(status_frame, orient=tk.VERTICAL).pack(side=tk.RIGHT, fill=tk.Y, padx=2)

        # 记录统计
        self.record_status_var = tk.StringVar()
        self.record_status_var.set("记录: 0/0")
        record_label = ttk.Label(status_frame, textvariable=self.record_status_var, anchor=tk.CENTER, width=12, font=("Arial", 8))
        record_label.pack(side=tk.RIGHT, padx=2)

        # 启动状态栏更新定时器
        self.update_status_bar_info()

        page_frame = ttk.Frame(status_frame)
        page_frame.pack(side=tk.RIGHT)

        ttk.Button(page_frame, text="<<", width=3, command=self.first_page).pack(side=tk.LEFT)
        ttk.Button(page_frame, text="<", width=3, command=self.prev_page).pack(side=tk.LEFT)

        self.page_var = tk.StringVar()
        ttk.Label(page_frame, textvariable=self.page_var, width=15, anchor="center").pack(side=tk.LEFT, padx=5)

        ttk.Button(page_frame, text=">", width=3, command=self.next_page).pack(side=tk.LEFT)
        ttk.Button(page_frame, text=">>", width=3, command=self.last_page).pack(side=tk.LEFT)

    def sort_column(self, col):
        """处理列标题点击事件，切换排序"""
        if self.sort_column_name == col:
            # 切换排序方向
            self.sort_direction = 'ASC' if self.sort_direction == 'DESC' else 'DESC'
        else:
            # 更换排序列，默认升序
            self.sort_column_name = col
            self.sort_direction = 'ASC'

        # 更新 Treeview 列标题显示
        for c in self.columns:
            header_text = c
            if c == self.sort_column_name:
                header_text += ' ▼' if self.sort_direction == 'DESC' else ' ▲'
            self.tree.heading(c, text=header_text)

        # 重新加载数据
        self.current_page = 1 # 排序后回到第一页
        self.load_data(self.current_query, self.current_params)


    def load_data(self, query, params):
        """通用数据加载和分页逻辑，包含排序 - 使用安全数据库包装器和缓存"""
        self.show_loading("正在加载数据...")
        try:
            # 构建排序子句
            order_clause = f"ORDER BY {self.sort_column_name} {self.sort_direction}"
            # 确保基础查询语句不包含 ORDER BY
            base_query = query.split(" ORDER BY ")[0]

            # 使用安全数据库包装器进行查询（如果可用）
            if self.safe_db is not None:
                # 获取总记录数 (基于基础查询)
                count_query = f"SELECT COUNT(*) FROM ({base_query})"
                cache_key_count = f"count_{hash(count_query + str(params))}"

                count_result = self.safe_db.execute_query(
                    count_query,
                    params,
                    fetch_all=False,
                    cache_key=cache_key_count,
                    query_type="count_query"
                )

                if count_result.success:
                    self.total_records = count_result.data[0] if count_result.data else 0
                    self.total_pages = max(1, (self.total_records + self.page_size - 1) // self.page_size)
                    # 修正当前页码
                    self.current_page = max(1, min(self.current_page, self.total_pages))

                    # 获取当前页数据 (应用排序和分页)
                    offset = (self.current_page - 1) * self.page_size
                    paginated_query = f"{base_query} {order_clause} LIMIT ? OFFSET ?"
                    cache_key_data = f"data_{hash(paginated_query + str(params) + str(self.page_size) + str(offset))}"

                    data_result = self.safe_db.execute_query(
                        paginated_query,
                        params + (self.page_size, offset),
                        fetch_all=True,
                        cache_key=cache_key_data,
                        query_type="paginated_query"
                    )

                    if data_result.success:
                        rows = data_result.data

                        # 显示性能信息
                        if count_result.execution_time > 0 or data_result.execution_time > 0:
                            total_time = count_result.execution_time + data_result.execution_time
                            print(f"📊 查询性能: 总耗时 {total_time:.3f}秒 (计数: {count_result.execution_time:.3f}s, 数据: {data_result.execution_time:.3f}s)")
                            # 更新状态栏性能信息
                            self.update_performance_info(total_time)
                    else:
                        raise Exception(f"数据查询失败: {data_result.error_message}")
                else:
                    raise Exception(f"计数查询失败: {count_result.error_message}")
            else:
                # 回退到原始数据库操作
                print("⚠️ 安全数据库包装器未初始化，使用原始方法")
                with sqlite3.connect(DB_PATH) as conn:
                    cursor = conn.cursor()
                    # 获取总记录数 (基于基础查询)
                    count_query = f"SELECT COUNT(*) FROM ({base_query})"
                    cursor.execute(count_query, params)
                    self.total_records = cursor.fetchone()[0]
                    self.total_pages = max(1, (self.total_records + self.page_size - 1) // self.page_size)
                    # 修正当前页码
                    self.current_page = max(1, min(self.current_page, self.total_pages))

                    # 获取当前页数据 (应用排序和分页)
                    offset = (self.current_page - 1) * self.page_size
                    paginated_query = f"{base_query} {order_clause} LIMIT ? OFFSET ?"
                    cursor.execute(paginated_query, params + (self.page_size, offset))
                    rows = cursor.fetchall()

            # 更新 Treeview - 改进None值显示，只显示可见列
            for i in self.tree.get_children():
                self.tree.delete(i)
            for row in rows:
                # 使用过滤函数只显示可见列的数据
                display_row = self.filter_row_for_display(row)
                self.tree.insert("", "end", values=display_row)

            # 更新状态栏和计数
            self.page_var.set(f"第 {self.current_page} / {self.total_pages} 页")

            # 判断是否为有效记录查询（默认查询或查找空值都排除过期记录）
            is_valid_records_query = (
                (hasattr(self, 'last_empty_field') and self.last_empty_field) or  # 查找空值
                ("Effective_To IS NULL OR Effective_To = '' OR Effective_To >" in base_query)  # 默认查询
            )

            if is_valid_records_query:
                self.total_count_var.set(f"有效记录总数: {self.total_records}")
            else:
                self.total_count_var.set(f"总数: {self.total_records}")

            self.status_var.set(f"加载了 {len(rows)} 条记录 (按 {self.sort_column_name} {self.sort_direction})")

            # 更新自动完成选项 (只在非搜索时更新，避免覆盖搜索结果)
            if query == "SELECT * FROM Equipment_ID":
                 self.update_combobox_options()

            # 保存当前查询状态 (不含排序，排序由成员变量控制)
            self.current_query = base_query
            self.current_params = params

        except Exception as e:
            print(f"Error loading data: {e}")
            safe_show_error(f"加载数据失败: {str(e)}")
        finally:
            self.hide_loading()

    def force_refresh_interface(self):
        """强制刷新界面，确保显示最新数据"""
        try:
            print("🔄 开始强制刷新界面...")

            # 1. 清除所有缓存
            if hasattr(self, 'safe_db') and self.safe_db:
                self.safe_db.clear_cache()
                print("   ✓ 数据库缓存已清除")

            # 2. 强制更新界面
            self.root.update_idletasks()

            # 3. 重新执行当前查询 - 添加更多调试信息
            print(f"   🔍 检查查询状态:")
            print(f"      current_query存在: {hasattr(self, 'current_query')}")
            print(f"      current_query值: {getattr(self, 'current_query', 'None')}")
            print(f"      current_params存在: {hasattr(self, 'current_params')}")
            print(f"      current_params值: {getattr(self, 'current_params', 'None')}")

            if hasattr(self, 'current_query') and self.current_query:
                query = self.current_query
                params = self.current_params if hasattr(self, 'current_params') else ()
                print(f"   ✓ 使用当前查询: {query[:50]}...")
            elif hasattr(self, 'last_query') and self.last_query:
                query = self.last_query
                params = self.last_params if hasattr(self, 'last_params') else ()
                print(f"   ✓ 使用上次查询: {query[:50]}...")
            else:
                # 默认查询
                today = safe_get_current_date()
                query = """
                    SELECT * FROM Equipment_ID
                    WHERE (Effective_To IS NULL OR Effective_To = '' OR Effective_To > ?)
                """
                params = (today,)
                self.current_query = query
                self.current_params = params
                print("   ✓ 使用默认查询")

            # 4. 清空当前表格
            children_count = len(self.tree.get_children())
            for item in self.tree.get_children():
                self.tree.delete(item)
            print(f"   ✓ 表格已清空 (删除了{children_count}行)")

            # 5. 重新加载数据
            print(f"   🔄 开始重新加载数据...")
            print(f"      查询: {query}")
            print(f"      参数: {params}")
            self.load_data(query, params)
            print("   ✓ 数据已重新加载")

            # 6. 强制更新界面显示
            self.root.update()

            # 7. 验证数据是否加载成功
            new_children_count = len(self.tree.get_children())
            print(f"   📊 刷新后表格行数: {new_children_count}")
            print("🔄 强制刷新完成！")

        except Exception as e:
            print(f"❌ 强制刷新失败: {e}")
            import traceback
            traceback.print_exc()
            # 如果强制刷新失败，回退到普通刷新
            self.refresh_data()

    def refresh_data(self):
        """刷新数据，保持当前查询状态和页码"""
        try:
            # 优先使用当前查询状态
            if hasattr(self, 'current_query') and self.current_query:
                query = self.current_query
                params = self.current_params if hasattr(self, 'current_params') else ()
            # 其次使用上次的查询状态
            elif hasattr(self, 'last_query') and self.last_query:
                query = self.last_query
                params = self.last_params if hasattr(self, 'last_params') else ()
            else:
                # 默认查询排除已失效的记录
                today = safe_get_current_date()
                query = """
                    SELECT * FROM Equipment_ID
                    WHERE (Effective_To IS NULL OR Effective_To = '' OR Effective_To > ?)
                """
                params = (today,)
                # 保存为当前查询状态
                self.current_query = query
                self.current_params = params

            # 更新列标题显示（保持排序指示器）
            for c in self.columns:
                header_text = c
                if hasattr(self, 'sort_column_name') and c == self.sort_column_name:
                    header_text += ' ▼' if getattr(self, 'sort_direction', 'DESC') == 'DESC' else ' ▲'
                self.tree.heading(c, text=header_text, command=lambda _col=c: self.sort_column(_col))

            # 重新加载数据（保持当前页码）
            self.load_data(query, params)

            # 如果是空值查询，更新状态栏
            if hasattr(self, 'last_empty_field'):
                self.status_var.set(f"显示 {self.last_empty_field} 为空的记录")

            print(f"🔄 数据已刷新 - 页码: {getattr(self, 'current_page', 1)}/{getattr(self, 'total_pages', 1)}")

        except Exception as e:
            print(f"刷新数据失败: {e}")
            # 发生错误时使用默认查询
            today = safe_get_current_date()
            default_query = """
                SELECT * FROM Equipment_ID
                WHERE (Effective_To IS NULL OR Effective_To = '' OR Effective_To > ?)
            """
            self.current_page = 1
            self.load_data(default_query, (today,))


    def search_data(self, reset_page=False):
        """根据搜索条件加载数据（根据过滤设置决定是否排除过期记录）"""
        if reset_page:
            self.current_page = 1

        serial = self.serial_var.get().strip()
        location = self.location_var.get().strip()
        state = self.state_var.get().strip()

        # 根据过滤设置决定基础查询
        if hasattr(self, 'filter_expired') and not self.filter_expired.get():
            # 不过滤失效记录，显示所有记录
            query = "SELECT * FROM Equipment_ID WHERE 1=1"
            params = []
        else:
            # 过滤失效记录（默认行为）
            today = safe_get_current_date()
            query = """
                SELECT * FROM Equipment_ID
                WHERE (Effective_To IS NULL OR Effective_To = '' OR Effective_To > ?)
            """
            params = [today]

        if serial:
            query += " AND Chair_Serial_No LIKE ?"
            params.append(f"%{serial}%")
        if location:
            query += " AND Location LIKE ?"
            params.append(f"%{location}%")
        if state:
            query += " AND STATE LIKE ?"
            params.append(f"%{state}%")

        # 保存当前查询状态，以便后续恢复
        self.last_query = query
        self.last_params = tuple(params)
        # 清除上次的空值查询字段，因为这是普通搜索
        if hasattr(self, 'last_empty_field'):
            delattr(self, 'last_empty_field')

        # 搜索时保持当前排序状态
        self.load_data(query, tuple(params))

    def clear_search(self):
        """清空搜索条件并刷新数据到默认视图"""
        # 清空搜索框
        self.serial_var.set("")
        self.location_var.set("")
        self.state_var.set("")

        # 恢复原始列布局（防止被其他功能修改）
        self.restore_original_columns()

        # 根据过滤设置决定默认查询
        if hasattr(self, 'filter_expired') and not self.filter_expired.get():
            # 不过滤失效记录
            self.current_query = "SELECT * FROM Equipment_ID"
            self.current_params = ()
        else:
            # 过滤失效记录（默认行为）
            today = safe_get_current_date()
            self.current_query = """
                SELECT * FROM Equipment_ID
                WHERE (Effective_To IS NULL OR Effective_To = '' OR Effective_To > ?)
            """
            self.current_params = (today,)

        # 重置排序状态
        self.current_page = 1
        self.sort_column_name = "ID"
        self.sort_direction = "DESC"

        # 如果存在上次查询状态，清除它们
        if hasattr(self, 'last_query'):
            self.last_query = None
        if hasattr(self, 'last_params'):
            self.last_params = None
        if hasattr(self, 'last_empty_field'):
            self.last_empty_field = None

        # 加载默认数据
        self.load_data(self.current_query, self.current_params)

        # 更新状态栏
        filter_status = "（包括失效）" if hasattr(self, 'filter_expired') and not self.filter_expired.get() else ""
        self.status_var.set(f"已恢复默认视图{filter_status}")

        # 记录操作
        add_log_entry("清空搜索", "清空搜索条件并恢复默认视图", "") # 清空后恢复默认视图和排序

    def view_all_records(self):
        """查看所有记录（包括过期记录）"""
        # 清空搜索框
        self.serial_var.set("")
        self.location_var.set("")
        self.state_var.set("")

        # 恢复原始列布局
        self.restore_original_columns()

        # 查询所有记录（不过滤过期记录）
        self.current_query = "SELECT * FROM Equipment_ID"
        self.current_params = ()

        # 重置排序状态
        self.current_page = 1
        self.sort_column_name = "ID"
        self.sort_direction = "DESC"

        # 清除查询状态标记
        if hasattr(self, 'last_query'):
            self.last_query = None
        if hasattr(self, 'last_params'):
            self.last_params = None
        if hasattr(self, 'last_empty_field'):
            self.last_empty_field = None

        # 加载所有数据
        self.load_data(self.current_query, self.current_params)

        # 更新状态栏
        self.status_var.set("显示所有记录（包括过期记录）")

        # 记录操作
        add_log_entry("查看所有记录", "显示所有记录包括过期记录", "")

    def restore_original_columns(self):
        """恢复主界面的原始列布局"""
        # 恢复原始列定义
        self.tree["columns"] = self.columns

        # 重新设置列标题和宽度
        if OPTIMIZATION_ENABLED and hasattr(Config, 'UI') and hasattr(Config.UI, 'COLUMN_WIDTHS'):
            column_widths = Config.UI.COLUMN_WIDTHS
        else:
            # 保持原始定义
            column_widths = {
                "ID": 50, "STATE": 80, "Location": 120, "Quantity": 60,
                "Chair_Serial_No": 150, "Sim_Card_Model": 120, "Sim_Card_No": 120,
                "Layer": 80, "Company": 150, "Effective_From": 100, "Effective_To": 100,
                "Rental": 80, "SIMCARDID": 120, "Import_Date": 100, "Last_Updated": 120,
                "CurrentFlag": 80, "DATE": 100
            }

        for col in self.columns:
            # 恢复列标题（包含排序指示器）
            header_text = col
            if col == self.sort_column_name:
                header_text += ' ▼' if self.sort_direction == 'DESC' else ' ▲'
            self.tree.heading(col, text=header_text, command=lambda _col=col: self.sort_column(_col))
            self.tree.column(col, width=column_widths.get(col, 100), anchor="center")

        # 恢复双击编辑事件
        self.tree.bind("<Double-1>", self.on_tree_double_click)

    def first_page(self):
        if self.current_page != 1:
            self.current_page = 1
            self.load_data(self.current_query, self.current_params)

    def last_page(self):
        if self.current_page != self.total_pages:
            self.current_page = self.total_pages
            self.load_data(self.current_query, self.current_params)

    def prev_page(self):
        if self.current_page > 1:
            self.current_page -= 1
            self.load_data(self.current_query, self.current_params)

    def next_page(self):
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.load_data(self.current_query, self.current_params)

    def update_combobox_options(self):
        """从数据库加载最新的 Location 和 State 选项"""
        try:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT Location FROM Equipment_ID WHERE Location IS NOT NULL AND Location != '' ORDER BY Location")
                locations = [row[0] for row in cursor.fetchall()]
                cursor.execute("SELECT DISTINCT STATE FROM Equipment_ID WHERE STATE IS NOT NULL AND STATE != '' ORDER BY STATE")
                states = [row[0] for row in cursor.fetchall()]
            self.location_combo["values"] = locations
            self.state_combo["values"] = states
        except Exception as e:
            print(f"Error updating combobox options: {e}")

    def update_location_autocomplete(self, event):
        value = self.location_var.get()
        try:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                # 使用 LIKE 查询匹配的选项
                cursor.execute("SELECT DISTINCT Location FROM Equipment_ID WHERE Location LIKE ? ORDER BY Location LIMIT 20", (f"%{value}%",))
                options = [row[0] for row in cursor.fetchall()]
            # 如果输入值不在选项中，也添加到列表开头，方便直接使用
            if value and value not in options:
                options.insert(0, value)
            self.location_combo["values"] = options
        except Exception as e:
            print(f"Error updating location autocomplete: {e}")


    def update_state_autocomplete(self, event):
        value = self.state_var.get()
        try:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT STATE FROM Equipment_ID WHERE STATE LIKE ? ORDER BY STATE LIMIT 20", (f"%{value}%",))
                options = [row[0] for row in cursor.fetchall()]
            if value and value not in options:
                options.insert(0, value)
            self.state_combo["values"] = options
        except Exception as e:
            print(f"Error updating state autocomplete: {e}")

    def find_empty_value(self):
        """弹窗选择字段，查找该字段为空的设备，并在主表格显示，支持编辑"""
        win = tk.Toplevel(self.root)
        win.title("查找空值")
        win.geometry("400x150") # 调整大小
        win.grab_set() # 设置为模态窗口
        win.transient(self.root) # 依附于主窗口

        tk.Label(win, text="请选择字段:").pack(pady=5)
        field_var = tk.StringVar()
        # 提供更多可能有空值的字段选项
        fields_to_check = ["Rental", "Layer", "DATE", "Chair_Serial_No", "Sim_Card_No", "Effective_To", "Company", "Effective_From", "Sim_Card_Model", "STATE", "Location"]
        field_combo = ttk.Combobox(win, textvariable=field_var, values=fields_to_check, state="readonly", width=25)
        field_combo.pack(pady=5)
        field_combo.set(fields_to_check[0]) # 默认选中第一个

        def do_search():
            field = field_var.get()
            if not field:
                safe_show_info("请选择字段")
                return

            # 获取当前日期
            today = safe_get_current_date()

            # 查询条件：字段为空 AND (没有失效日期 OR 失效日期大于今天)
            # 这样可以忽略已过期的记录
            query = f"""
                SELECT * FROM Equipment_ID
                WHERE ({field} IS NULL OR {field} = '')
                AND (Effective_To IS NULL OR Effective_To = '' OR Effective_To > ?)
            """
            try:
                # 保存当前查询状态，以便后续恢复
                self.last_empty_field = field
                self.last_query = query
                self.last_params = (today,)

                # 在主界面加载数据
                self.current_page = 1
                self.load_data(query, (today,))

                # 更新状态栏
                self.status_var.set(f"显示 {field} 为空的有效记录（已忽略过期记录）")
                win.destroy()
            except Exception as e:
                print(f"Error finding empty value for {field}: {e}")
                safe_show_error(f"查找失败: {str(e)}")

        ttk.Button(win, text="查找", command=do_search).pack(pady=10)
        ttk.Button(win, text="取消", command=win.destroy).pack(pady=5)


    def show_result_window(self, rows, title):
        """通用结果显示窗口"""
        win = tk.Toplevel(self.root)
        win.title(title)
        win.geometry("1200x500")
        win.grab_set() # 设置为模态窗口

        tree_frame = ttk.Frame(win, padding=10)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        tree = ttk.Treeview(tree_frame, columns=self.columns, show="headings")

        # --- 设置列宽和对齐 (与主窗口一致) ---
        if OPTIMIZATION_ENABLED and hasattr(Config, 'UI') and hasattr(Config.UI, 'COLUMN_WIDTHS'):
            column_widths = Config.UI.COLUMN_WIDTHS
        else:
            # 保持原始定义
            column_widths = {
                "ID": 50, "STATE": 80, "Location": 120, "Quantity": 60,
                "Chair_Serial_No": 150, "Sim_Card_Model": 120, "Sim_Card_No": 120,
                "Layer": 80, "Company": 150, "Effective_From": 100, "Effective_To": 100,
                "Rental": 80, "SIMCARDID": 120, "Import_Date": 100, "Last_Updated": 120,
                "CurrentFlag": 80, "DATE": 100
            }
        for col in self.columns:
            tree.heading(col, text=col)
            tree.column(col, width=column_widths.get(col, 100), anchor="center")

        # --- 添加滚动条 ---
        vsb = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal", command=tree.xview)
        tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        tree.pack(fill=tk.BOTH, expand=True)

        # --- 填充数据 ---
        for row in rows:
            # 使用过滤函数只显示可见列的数据
            display_row = self.filter_row_for_display(row)
            tree.insert("", "end", values=display_row)

        # --- 关闭按钮 ---
        ttk.Button(win, text="关闭", command=win.destroy).pack(pady=10)


    def view_expired(self):
        """查找并显示已过期的设备，支持批量操作"""
        today = safe_get_current_date()
        query = "SELECT * FROM Equipment_ID WHERE Effective_To < ? AND Effective_To IS NOT NULL AND Effective_To != ''"
        try:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                cursor.execute(query, (today,))
                rows = cursor.fetchall()

            if not rows:
                safe_show_info("没有已过期的设备。")
            else:
                self.show_expired_window(rows)
        except Exception as e:
            print(f"Error viewing expired equipment: {e}")
            safe_show_error(f"查询过期设备失败: {str(e)}")

    def show_expired_window(self, rows):
        """显示过期设备列表，支持批量归档和批量取消失效日期"""
        win = tk.Toplevel(self.root)
        win.title("过期设备列表")
        win.geometry("1200x550")
        win.grab_set()

        tree_frame = ttk.Frame(win, padding=10)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        tree = ttk.Treeview(tree_frame, columns=self.columns, show="headings", selectmode="extended")
        if OPTIMIZATION_ENABLED and hasattr(Config, 'UI') and hasattr(Config.UI, 'COLUMN_WIDTHS'):
            column_widths = Config.UI.COLUMN_WIDTHS
        else:
            # 保持原始定义
            column_widths = {
                "ID": 50, "STATE": 80, "Location": 120, "Quantity": 60, "Chair_Serial_No": 150, "Sim_Card_Model": 120,
                "Sim_Card_No": 120, "Layer": 80, "Company": 150, "Effective_From": 100, "Effective_To": 100,
                "Rental": 80, "SIMCARDID": 120, "Import_Date": 100, "Last_Updated": 120, "CurrentFlag": 80, "DATE": 100
            }
        for col in self.columns:
            tree.heading(col, text=col)
            tree.column(col, width=column_widths.get(col, 100), anchor="center")
        vsb = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal", command=tree.xview)
        tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        tree.pack(fill=tk.BOTH, expand=True)

        for row in rows:
            # 使用过滤函数只显示可见列的数据
            display_row = self.filter_row_for_display(row)
            tree.insert("", "end", values=display_row)

        # 右键菜单
        def show_context_menu(event):
            selected = tree.selection()
            if not selected:
                return
            menu = tk.Menu(win, tearoff=0)
            menu.add_command(label="归档", command=lambda: self.archive_selected(tree, win))
            menu.add_command(label="取消失效日期", command=lambda: self.cancel_expiry_selected(tree, win))
            menu.post(event.x_root, event.y_root)
        tree.bind("<Button-3>", show_context_menu)

        button_frame = ttk.Frame(win, padding=(0, 10))
        button_frame.pack(fill=tk.X)
        ttk.Button(button_frame, text="关闭", command=win.destroy).pack(side=tk.RIGHT, padx=10)

    def archive_selected(self, tree, win):
        selected = tree.selection()
        ids = [tree.item(i)["values"][0] for i in selected]
        if not ids:
            safe_show_info("请先选择要归档的设备")
            return
        try:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                # 这里假设归档是设置CurrentFlag为'Archived'
                cursor.execute(f"UPDATE Equipment_ID SET CurrentFlag='Archived', Last_Updated=? WHERE ID IN ({','.join(['?']*len(ids))})",
                               [safe_get_current_timestamp()] + ids)
                conn.commit()
            add_log_entry("批量归档", f"归档设备ID: {ids}", str(ids))
            safe_show_info(f"已归档 {len(ids)} 条记录")
            win.destroy()

            # 强制刷新界面
            self.force_refresh_interface()
            self.view_expired()
        except Exception as e:
            print(f"批量归档失败: {e}")
            safe_show_error(f"批量归档失败: {str(e)}")

    def cancel_expiry_selected(self, tree, win):
        selected = tree.selection()
        ids = [tree.item(i)["values"][0] for i in selected]
        if not ids:
            safe_show_info("请先选择要操作的设备")
            return

        def do_delete():
            try:
                with sqlite3.connect(DB_PATH) as conn:
                    cursor = conn.cursor()
                    cursor.execute(f"UPDATE Equipment_ID SET Effective_To=NULL, Last_Updated=? WHERE ID IN ({','.join(['?']*len(ids))})",
                                   [safe_get_current_timestamp()] + ids)
                    conn.commit()
                add_log_entry("批量删除失效日期", f"删除设备ID: {ids} 的失效日期", str(ids))
                safe_show_info("已批量删除失效日期")
                win.destroy()

                # 强制刷新界面
                self.force_refresh_interface()
                self.view_expired()
            except Exception as e:
                print(f"批量删除失效日期失败: {e}")
                safe_show_error(f"批量删除失效日期失败: {str(e)}")

        def do_edit():
            edit_win = tk.Toplevel(self.root)
            edit_win.title("批量编辑失效日期")
            edit_win.geometry("300x120")
            tk.Label(edit_win, text="新失效日期 (YYYY-MM-DD):").pack(pady=10)
            date_var = tk.StringVar()
            entry = ttk.Entry(edit_win, textvariable=date_var, width=20)
            entry.pack(pady=5)
            entry.focus()

            def save_new_date():
                new_date = date_var.get().strip()
                if not is_valid_date(new_date):
                    safe_show_error("日期格式应为YYYY-MM-DD")
                    return
                try:
                    with sqlite3.connect(DB_PATH) as conn:
                        cursor = conn.cursor()
                        cursor.execute(f"UPDATE Equipment_ID SET Effective_To=?, Last_Updated=? WHERE ID IN ({','.join(['?']*len(ids))})",
                                       [new_date, datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")] + ids)
                        conn.commit()
                    add_log_entry("批量编辑失效日期", f"批量编辑设备ID: {ids} 的失效日期为 {new_date}", str(ids))
                    safe_show_info("已批量修改失效日期")
                    edit_win.destroy()
                    win.destroy()

                    # 强制刷新界面
                    self.force_refresh_interface()
                    self.view_expired()
                except Exception as e:
                    print(f"批量编辑失效日期失败: {e}")
                    safe_show_error(f"批量编辑失效日期失败: {str(e)}")

            ttk.Button(edit_win, text="保存", command=save_new_date).pack(pady=10)

        res = messagebox.askquestion("取消失效日期", "请选择操作：\n是=删除失效日期\n否=编辑失效日期", icon="question", parent=win)
        if res == "yes":
            do_delete()
        else:
            do_edit()

    def query_unmatched_serial(self):
        """
        查询Daily_Equipment_Sales中IOT_Price和ZERO_Price大于5，
        且Chair_Serial_No有值但其它字段（除价格和日期外）为空的唯一序列号，
        显示Chair_Serial_No、IOT_Price、ZERO_Price、最新Sale_Date
        """
        query = """
            SELECT t.Chair_Serial_No, t.IOT_Price, t.ZERO_Price, t.Sale_Date
            FROM (
                SELECT Chair_Serial_No, IOT_Price, ZERO_Price, Sale_Date,
                    ROW_NUMBER() OVER (PARTITION BY Chair_Serial_No ORDER BY Sale_Date DESC) as rn,
                    State, Location, Quantity
                FROM Daily_Equipment_Sales
                WHERE Chair_Serial_No IS NOT NULL
                AND Chair_Serial_No != ''
                AND IOT_Price > 5
                AND ZERO_Price > 5
            ) t
            WHERE t.rn = 1
            AND (t.State IS NULL OR t.State = '')
            AND (t.Location IS NULL OR t.Location = '')
            AND (t.Quantity IS NULL OR t.Quantity = '')
        """
        try:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                cursor.execute(query)
                rows = cursor.fetchall()
            # 清空Treeview并只显示需要的列
            self.tree["columns"] = ("Chair_Serial_No", "IOT_Price", "ZERO_Price", "Sale_Date")
            for col in self.tree["columns"]:
                self.tree.heading(col, text=col)
                self.tree.column(col, anchor="center")
            for i in self.tree.get_children():
                self.tree.delete(i)
            for row in rows:
                # 使用过滤函数只显示可见列的数据
                display_row = self.filter_row_for_display(row)
                self.tree.insert("", "end", values=display_row)
            self.status_var.set(f"共找到 {len(rows)} 条记录")
        except Exception as e:
            safe_show_error(f"查询失败: {e}")


    def export_abnormal_by_date(self):
        """
        导出Daily_Equipment_Sales中Chair_Serial_No以PAY开头的异常序列号，
        可选择年月，导出Chair_Serial_No、IOT_Price、Sale_Date
        """
        # 弹窗选择年月
        win = tk.Toplevel(self.root)
        win.title("选择导出月份")
        tk.Label(win, text="年份:").grid(row=0, column=0, padx=5, pady=5)
        tk.Label(win, text="月份:").grid(row=1, column=0, padx=5, pady=5)
        year_var = tk.StringVar(value=str(datetime.datetime.now().year))
        month_var = tk.StringVar(value=str(datetime.datetime.now().month))
        ttk.Entry(win, textvariable=year_var, width=8).grid(row=0, column=1, padx=5)
        ttk.Entry(win, textvariable=month_var, width=8).grid(row=1, column=1, padx=5)

        def do_export():
            year = year_var.get()
            month = month_var.get().zfill(2)
            file_path = filedialog.asksaveasfilename(
                title="导出异常序列号",
                defaultextension=".xlsx",
                filetypes=[("Excel文件", "*.xlsx")]
            )
            if not file_path:
                return
            try:
                with sqlite3.connect(DB_PATH) as conn:
                    query = """
                        SELECT Chair_Serial_No, IOT_Price, Sale_Date
                        FROM Daily_Equipment_Sales
                        WHERE Chair_Serial_No LIKE 'PAY%'
                        AND strftime('%Y', Sale_Date) = ?
                        AND strftime('%m', Sale_Date) = ?
                    """
                    df = pd.read_sql_query(query, conn, params=(year, month))
                if df.empty:
                    safe_show_info("没有发现异常序列号。")
                    return
                df.to_excel(file_path, index=False)
                safe_show_info(f"异常序列号已导出到: {file_path}")
                add_log_entry("导出异常", f"导出{year}-{month}异常序列号", file_path)
                win.destroy()
            except Exception as e:
                safe_show_error(f"导出异常序列号失败: {e}")

        ttk.Button(win, text="导出", command=do_export).grid(row=2, column=0, columnspan=2, pady=10)


    def save_new(sheet, edit_win, self):
        import re
        import datetime
        failed_rows = []
        error_msgs = []
        data = sheet.get_sheet_data(return_copy=True)
        for idx, row in enumerate(data):
            try:
                # 校验必填项
                state = row[0].strip()
                location = row[1].strip()
                quantity = row[2].strip()
                chair_serial_no = row[3].strip()
                if not state or not location or not quantity or not chair_serial_no:
                    failed_rows.append(idx+1)
                    error_msgs.append(f"第{idx+1}行：必填项缺失（STATE, Location, Quantity, Chair_Serial_No）")
                    continue
                # 日期自动格式化
                def excel_date_auto_format(s):
                    s = s.strip()
                    if not s:
                        return ""
                    if re.fullmatch(r"\d{6}", s):
                        d = datetime.datetime.strptime(s, "%d%m%y")
                        if d.year < 2000:
                            d = d.replace(year=d.year + 2000)
                        return d.strftime("%Y-%m-%d")
                    for fmt in ("%d/%m/%Y", "%Y-%m-%d", "%Y/%m/%d", "%m/%d/%Y", "%d-%m-%Y"):
                        try:
                            d = datetime.datetime.strptime(s, fmt)
                            return d.strftime("%Y-%m-%d")
                        except:
                            pass
                    return s
                row = [cell if i < 10 else excel_date_auto_format(cell) if i in (10, 11) else cell for i, cell in enumerate(row)]
                eff_from = row[10]
                eff_to = row[11]
                # 校验日期格式
                def is_valid_date(s):
                    if not s:
                        return True
                    try:
                        datetime.datetime.strptime(s, "%Y-%m-%d")
                        return True
                    except ValueError:
                        return False
                if not is_valid_date(eff_from) or not is_valid_date(eff_to):
                    failed_rows.append(idx+1)
                    error_msgs.append(f"第{idx+1}行：生效日期或失效日期格式错误（应为YYYY-MM-DD）")
                    continue
                # 检查唯一约束
                try:
                    with sqlite3.connect(DB_PATH) as conn:
                        cursor = conn.cursor()
                        cursor.execute("SELECT * FROM Equipment_ID WHERE Chair_Serial_No=?", (chair_serial_no,))
                        if cursor.fetchone():
                            failed_rows.append(idx+1)
                            error_msgs.append(f"第{idx+1}行：Chair_Serial_No已存在，不能重复")
                            continue
                        try:
                            cursor.execute("""
                                INSERT INTO Equipment_ID
                                (STATE, Location, Quantity, Chair_Serial_No, Sim_Card_Model, Sim_Card_No, Layer, Company, Effective_From, Effective_To, Rental, SIMCARDID, Import_Date, Last_Updated, CurrentFlag, DATE)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, tuple(row[:16]))
                            conn.commit()
                        except sqlite3.IntegrityError as e:
                            failed_rows.append(idx+1)
                            error_msgs.append(f"第{idx+1}行：数据库唯一约束错误（{e}）")
                        except sqlite3.OperationalError as e:
                            failed_rows.append(idx+1)
                            error_msgs.append(f"第{idx+1}行：数据库操作错误（{e}）")
                        except sqlite3.DatabaseError as e:
                            failed_rows.append(idx+1)
                            error_msgs.append(f"第{idx+1}行：数据库错误（{e}）")
                        except Exception as e:
                            failed_rows.append(idx+1)
                            error_msgs.append(f"第{idx+1}行：未知数据库错误（{e}）")
                except sqlite3.OperationalError as e:
                    failed_rows.append(idx+1)
                    error_msgs.append(f"第{idx+1}行：数据库连接失败或只读（{e}）")
                except Exception as e:
                    failed_rows.append(idx+1)
                    error_msgs.append(f"第{idx+1}行：数据库连接异常（{e}）")
            except Exception as e:
                failed_rows.append(idx+1)
                error_msgs.append(f"第{idx+1}行：程序异常（{e}）")
        if failed_rows:
            msg = "部分数据添加失败：\n" + "\n".join(error_msgs)
            print(msg)
            messagebox.showwarning("部分失败", msg)
        else:
            messagebox.showinfo("成功", "批量添加成功！")
        edit_win.destroy()

        # 强制刷新界面
        self.force_refresh_interface()



    def on_tree_double_click(self, event):
        """处理树形控件的双击事件，区分列标题和数据行"""
        # 获取点击位置
        region = self.tree.identify_region(event.x, event.y)

        # 如果点击的是列标题区域，不执行编辑操作
        if region == "heading":
            return

        # 如果点击的是数据行区域，执行编辑操作
        if region == "cell" or region == "tree":
            self.edit_selected_id()

    def edit_selected_id(self):
        """获取选中行并打开编辑窗口"""
        selected = self.tree.selection()
        if not selected:
            safe_show_info("请先选择要编辑的设备")
            return
        item = self.tree.item(selected[0])
        equipment_id = item["values"][0] # ID 在第一列
        self.edit_equipment_id_by_id(equipment_id)

    def edit_equipment_id_by_id(self, equipment_id):
        """通用编辑/添加窗口"""
        is_add_mode = equipment_id is None
        win_title = "添加新设备" if is_add_mode else f"编辑设备 ID: {equipment_id}"

        win = tk.Toplevel(self.root)
        win.title(win_title)
        win.geometry("500x650") # 调整窗口大小以容纳更多字段
        win.grab_set()

        # --- 创建输入字段 ---
        fields_frame = ttk.Frame(win, padding=15)
        fields_frame.pack(fill=tk.BOTH, expand=True)

        entries = {}
        # 使用完整的列定义（排除ID），确保编辑窗口显示所有字段
        labels = self.all_columns[1:] if hasattr(self, 'all_columns') else self.columns[1:]

        # 两列布局
        for i, label in enumerate(labels):
            row = i // 2
            col = (i % 2) * 2
            ttk.Label(fields_frame, text=f"{label}:").grid(row=row, column=col, padx=5, pady=5, sticky="w")
            var = tk.StringVar()
            entry = ttk.Entry(fields_frame, textvariable=var, width=30)
            entry.grid(row=row, column=col + 1, padx=5, pady=5, sticky="ew")
            entries[label] = var

        # 如果是编辑模式，加载现有数据
        if not is_add_mode:
            try:
                with sqlite3.connect(DB_PATH) as conn:
                    cursor = conn.cursor()
                    cursor.execute(f"SELECT {', '.join(labels)} FROM Equipment_ID WHERE ID=?", (equipment_id,))
                    data = cursor.fetchone()
                if data:
                    for i, label in enumerate(labels):
                        # 处理 None 值，防止显示 "None"
                        value = data[i] if data[i] is not None else ""
                        entries[label].set(value)
                else:
                    messagebox.showerror("错误", f"找不到 ID 为 {equipment_id} 的设备", parent=win)
                    win.destroy()
                    return
            except Exception as e:
                print(f"Error fetching data for editing {equipment_id}: {e}")
                messagebox.showerror("错误", f"加载数据失败: {str(e)}", parent=win)
                win.destroy()
                return

        # --- 保存按钮逻辑 ---
        def save_data():
            values = {}
            # 数据验证
            for label, var in entries.items():
                value = var.get().strip()
                # 使用增强的输入验证
                is_valid, error_msg = self.validate_input(label, value)
                if not is_valid:
                    messagebox.showerror("输入错误", f"字段 '{label}': {error_msg}", parent=win)
                    return
               
                    

                # 将空字符串转换为 None 以便存入数据库
                values[label] = value if value else None

          

            try:
                with sqlite3.connect(DB_PATH) as conn:
                    cursor = conn.cursor()
                    if is_add_mode:
                        # 插入新记录
                        cols = ', '.join(values.keys())
                        placeholders = ', '.join('?' * len(values))
                        sql = f"INSERT INTO Equipment_ID ({cols}) VALUES ({placeholders})"
                        cursor.execute(sql, tuple(values.values()))
                        new_id = cursor.lastrowid # 获取新插入的ID
                        log_action = "添加设备"
                        log_target = new_id
                        log_desc = f"添加新设备, 序列号: {values.get('Chair_Serial_No', 'N/A')}"
                    else:
                        # 获取编辑前的数据用于撤销
                        cursor.execute(f"SELECT * FROM Equipment_ID WHERE ID=?", (equipment_id,))
                        before_data = cursor.fetchone()

                        # 更新现有记录
                        set_clause = ', '.join([f"{col}=?" for col in values.keys()])
                        sql = f"UPDATE Equipment_ID SET {set_clause} WHERE ID=?"
                        cursor.execute(sql, tuple(values.values()) + (equipment_id,))

                        # 获取编辑后的数据
                        cursor.execute(f"SELECT * FROM Equipment_ID WHERE ID=?", (equipment_id,))
                        after_data = cursor.fetchone()

                        # 记录操作历史
                        if before_data and after_data:
                            self.record_operation('EDIT', before_data=[before_data], after_data=[after_data], affected_ids=[equipment_id])

                        log_action = "编辑设备"
                        log_target = equipment_id
                        log_desc = f"编辑设备 ID: {equipment_id}"
                    conn.commit()

                add_log_entry(log_action, log_desc, log_target)
                safe_show_info("数据保存成功")
                win.destroy()

                # 强制清除缓存并刷新数据
                if hasattr(self, 'safe_db') and self.safe_db:
                    self.safe_db.clear_cache()
                    print("🗑️ 缓存已清除")

                # 强制刷新界面 - 使用更直接的方法
                self.force_refresh_interface()
                print(f"✅ 编辑保存成功，数据已刷新")

            except sqlite3.IntegrityError as e:
                 if "UNIQUE constraint failed: Equipment_ID.Chair_Serial_No" in str(e):
                     messagebox.showerror("保存失败", "椅子序列号 (Chair_Serial_No) 已存在，不能重复。", parent=win)
                 else:
                     print(f"Database integrity error: {e}")
                     messagebox.showerror("保存失败", f"数据库错误: {str(e)}", parent=win)
            except Exception as e:
                print(f"Error saving data: {e}")
                messagebox.showerror("保存失败", f"发生错误: {str(e)}", parent=win)

        # --- 按钮 ---
        button_frame = ttk.Frame(win, padding=(0, 15))
        button_frame.pack(side=tk.BOTTOM, fill=tk.X)
        ttk.Button(button_frame, text="保存", command=save_data).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=win.destroy).pack(side=tk.RIGHT, padx=10)

        # 让列自适应
        fields_frame.columnconfigure(1, weight=1)
        fields_frame.columnconfigure(3, weight=1)


    def delete_selected_id(self):
        """删除选中的单条记录，并保存记录用于撤销"""
        selected = self.tree.selection()
        if not selected:
            safe_show_info("请先选择要删除的设备")
            return

        item = self.tree.item(selected[0])
        equipment_id = item["values"][0]
        serial_no = item["values"][4] # Chair_Serial_No 在第5列 (索引4)

        if messagebox.askyesno("确认删除", f"确定要删除设备 ID: {equipment_id} (序列号: {serial_no}) 吗？\n可以使用Ctrl+Z撤销此操作"):
            try:
                # 先保存记录用于撤销
                with sqlite3.connect(DB_PATH) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT * FROM Equipment_ID WHERE ID=?", (equipment_id,))
                    record = cursor.fetchone()
                    if record:
                        # 记录操作历史
                        self.record_operation('DELETE', before_data=[record], affected_ids=[equipment_id])
                        self.deleted_records.append(record)  # 保持向后兼容

                    # 执行删除
                    cursor.execute("DELETE FROM Equipment_ID WHERE ID=?", (equipment_id,))
                    conn.commit()

                add_log_entry("删除设备", f"删除设备 ID: {equipment_id}, 序列号: {serial_no}", equipment_id)
                safe_show_info(f"设备 ID: {equipment_id} 已删除\n可以使用Ctrl+Z撤销此操作", "成功")

                # 强制刷新界面
                self.force_refresh_interface()

            except Exception as e:
                print(f"Error deleting equipment {equipment_id}: {e}")
                messagebox.showerror("删除失败", f"删除设备时发生错误: {str(e)}")

    def batch_delete_selected(self):
        """批量删除选中的多条记录"""
        selected_items = self.tree.selection()
        if not selected_items:
            safe_show_info("请先选择要批量删除的设备")
            return

        ids_to_delete = []
        details_to_log = []
        for item_id in selected_items:
            item = self.tree.item(item_id)
            equipment_id = item["values"][0]
            serial_no = item["values"][4]
            ids_to_delete.append(equipment_id)
            details_to_log.append(f"ID: {equipment_id}, SN: {serial_no}")

        if messagebox.askyesno("确认批量删除", f"确定要删除选中的 {len(ids_to_delete)} 条设备记录吗？\n可以使用Ctrl+Z撤销此操作"):
            # 对于大批量操作（超过100条），使用异步处理
            if len(ids_to_delete) > 100:
                operation_id, future = async_manager.submit_operation(
                    self._async_batch_delete,
                    ids_to_delete,
                    details_to_log,
                    description=f"批量删除 {len(ids_to_delete)} 条记录"
                )
                self.show_async_progress(operation_id, "批量删除进行中")
                return

            # 显示进度指示器（如果是中等批量操作）
            if len(ids_to_delete) > 50:
                self.show_loading(f"正在删除 {len(ids_to_delete)} 条记录...")

            try:
                # 使用优化的批量删除操作
                delete_result = self.safe_db.batch_delete_optimized(ids_to_delete, "batch_delete_equipment")

                if delete_result.success:
                    deleted_records = delete_result.data
                    deleted_count = len(deleted_records)

                    # 记录操作历史
                    if deleted_records:
                        self.record_operation('BATCH_DELETE', before_data=deleted_records, affected_ids=ids_to_delete)
                        self.deleted_records.extend(deleted_records)  # 保持向后兼容

                    # 记录日志
                    log_desc = f"批量删除 {deleted_count} 条设备: " + "; ".join(details_to_log)
                    add_log_entry("批量删除", log_desc, str(ids_to_delete))

                    # 显示结果
                    execution_time = delete_result.execution_time
                    message = f"✅ 成功删除 {deleted_count} 条记录"
                    if execution_time > 0:
                        message += f"\n⚡ 耗时: {execution_time:.3f}秒"
                    message += "\n💡 可以使用Ctrl+Z撤销此操作"

                    safe_show_info(message)

                    # 强制刷新界面
                    self.force_refresh_interface()
                else:
                    raise Exception(delete_result.error_message)

            except Exception as e:
                print(f"Error during batch delete: {e}")
                safe_show_error(f"批量删除过程中发生错误: {str(e)}")
            finally:
                if len(ids_to_delete) > 50:
                    self.hide_loading()




    def show_database_manager(self):
        """显示数据库管理器"""
        if OPTIMIZATION_ENABLED:
            try:
                from database_switcher import show_database_manager
                show_database_manager(self.root, self.refresh_after_db_switch)
            except ImportError:
                safe_show_error("数据库管理器模块未找到，请检查 database_switcher.py 文件")
        else:
            safe_show_error("数据库管理器需要优化模块支持")

    def refresh_after_db_switch(self):
        """数据库切换后刷新界面"""
        try:
            # 更新全局数据库路径
            global DB_PATH, safe_db
            if OPTIMIZATION_ENABLED:
                from config_manager import user_preferences
                new_db_path = user_preferences.get_current_database()
                if new_db_path != DB_PATH:
                    DB_PATH = new_db_path
                    print(f"📁 数据库已切换到: {DB_PATH}")

                    # 重新初始化安全数据库包装器
                    safe_db = SafeDatabaseWrapper(DB_PATH)
                    self.safe_db = safe_db
                    print(f"✅ 安全数据库包装器已重新初始化")

                    # 重新初始化数据库表结构
                    init_database()

            # 清除缓存
            if hasattr(self, 'safe_db') and self.safe_db:
                self.safe_db.clear_cache()

            # 刷新数据显示
            self.force_refresh_interface()

            # 更新状态栏
            if hasattr(self, 'status_var'):
                self.status_var.set(f"数据库已切换: {os.path.basename(DB_PATH)}")

        except Exception as e:
            safe_show_error(f"数据库切换后刷新失败: {e}")

    def import_from_excel(self):
        """Excel导入功能 - 使用新的模块化导入器"""
        if OPTIMIZATION_ENABLED:
            # 使用新的模块化导入器
            import_excel_data(self.root, self.force_refresh_interface)
        else:
            # 保持原有的导入逻辑
            self._legacy_import_from_excel()

    def _legacy_import_from_excel(self):
        """原有的Excel导入逻辑（向后兼容）"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls")]
        )
        if not file_path:
            return
        try:
            df = pd.read_excel(file_path)
            # 检查必要字段 - 改进错误提示
            required_cols = {"STATE", "Chair_Serial_No"}  # 只要求最基本的字段
            missing_cols = required_cols - set(df.columns)
            if missing_cols:
                missing_list = ', '.join(missing_cols)
                error_msg = f"❌ Excel文件缺少必要字段: {missing_list}\n\n"
                error_msg += f"📋 当前Excel文件包含的字段: {', '.join(df.columns)}\n\n"
                error_msg += f"✅ 必须包含的字段: {', '.join(required_cols)}\n\n"
                error_msg += f"💡 建议: 请确保Excel文件包含 '{missing_list}' 字段"
                safe_show_error(error_msg)
                return

            # 显示数据预览
            update_progress(f"✅ Excel文件验证通过")
            update_progress(f"📋 Excel文件列 ({len(df.columns)}个): {', '.join(df.columns)}")
            update_progress(f"📊 数据预览 (前3行):")
            for i, (_, row) in enumerate(df.head(3).iterrows()):
                row_preview = {k: str(v)[:30] + '...' if len(str(v)) > 30 else str(v) for k, v in row.items()}
                update_progress(f"  行{i+1}: {row_preview}")
            update_progress("")
                
            # 创建导入进度窗口
            import_win = tk.Toplevel(self.root)
            import_win.title("导入Excel数据")
            import_win.geometry("600x400")
            import_win.transient(self.root)
            import_win.grab_set()
            
            main_frame = ttk.Frame(import_win, padding=10)
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            ttk.Label(main_frame, text="正在导入Excel数据...", font=("Arial", 12, "bold")).pack(pady=(0, 10))
            
            # 创建进度显示区域
            progress_frame = ttk.LabelFrame(main_frame, text="导入进度", padding=10)
            progress_frame.pack(fill=tk.BOTH, expand=True)
            
            # 创建文本框显示进度
            progress_text = tk.Text(progress_frame, height=15, width=70)
            progress_text.pack(fill=tk.BOTH, expand=True)
            
            # 添加滚动条
            scrollbar = ttk.Scrollbar(progress_frame, command=progress_text.yview)
            progress_text.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # 更新进度的函数
            def update_progress(message):
                progress_text.insert(tk.END, message + "\n")
                progress_text.see(tk.END)
                import_win.update()
            
            update_progress(f"开始导入文件: {file_path}")
            update_progress(f"共有 {len(df)} 条记录待导入")
            
            # 处理数据导入
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                imported = 0
                failed = 0
                duplicate_count = 0
                
                # 获取数据库字段列表，用于set_effective_dates_for_duplicate函数
                cursor.execute("PRAGMA table_info(Equipment_ID)")
                db_fields = [field[1] for field in cursor.fetchall()]
                
                for row_idx, row in df.iterrows():
                    try:
                        # 改进数据获取逻辑
                        chair_serial_no = str(row.get("Chair_Serial_No", "")).strip()
                        if not chair_serial_no or chair_serial_no == 'nan':
                            update_progress(f"❌ 行{row_idx+1}: 跳过空序列号记录")
                            failed += 1
                            continue

                        update_progress(f"🔄 处理行{row_idx+1}: 序列号 {chair_serial_no}")

                        # 检查必填字段并提供智能补全
                        missing_required_fields = []
                        required_fields_check = ["STATE"]  # 可以根据需要添加更多必填字段

                        for field in required_fields_check:
                            field_value = str(row.get(field, "")).strip()
                            if not field_value or field_value == 'nan':
                                missing_required_fields.append(field)

                        # 如果有缺少的必填字段，提供智能补全界面
                        if missing_required_fields:
                            update_progress(f"⚠️ 行{row_idx+1}: 缺少必填字段 {', '.join(missing_required_fields)}")
                            update_progress(f"🔧 正在打开智能补全界面...")

                            # 调用智能补全函数
                            completed_data = self.smart_complete_missing_fields(
                                row, missing_required_fields, chair_serial_no, row_idx+1, import_win
                            )

                            if completed_data is None:
                                # 用户取消了补全
                                update_progress(f"❌ 用户取消了行{row_idx+1}的数据补全")
                                failed += 1
                                continue
                            else:
                                # 使用补全后的数据
                                row = completed_data
                                update_progress(f"✅ 行{row_idx+1}: 数据补全完成")
                            
                        # 检查是否存在重复序列号
                        is_duplicate = self.check_chair_serial_no_unique(chair_serial_no)
                        
                        # 改进数据解析 - 处理pandas的NaN值
                        def safe_get_value(row, key, default=None):
                            """安全获取值，处理pandas的NaN和各种数据格式"""
                            import pandas as pd
                            value = row.get(key, default)

                            # 处理pandas的NaN值
                            if pd.isna(value):
                                return None

                            # 转换为字符串并清理
                            str_value = str(value).strip()

                            # 空字符串或'nan'字符串都视为None
                            if str_value == '' or str_value.lower() == 'nan':
                                return None

                            return str_value

                        # 获取各字段值
                        state = safe_get_value(row, "STATE")
                        location = safe_get_value(row, "Location")
                        quantity = safe_get_value(row, "Quantity")
                        sim_card_model = safe_get_value(row, "Sim_Card_Model")
                        sim_card_no = safe_get_value(row, "Sim_Card_No")
                        layer = safe_get_value(row, "Layer")
                        company = safe_get_value(row, "Company")
                        rental = safe_get_value(row, "Rental")
                        simcardid = safe_get_value(row, "SIMCARDID")
                        date_field = safe_get_value(row, "DATE")

                        # 生效日期和失效日期通常为空（用户后续填写）
                        effective_from = safe_get_value(row, "Effective_From")
                        effective_to = safe_get_value(row, "Effective_To")

                        # 显示解析结果
                        update_progress(f"  STATE: '{state}', Location: '{location}', Quantity: '{quantity}'")
                        update_progress(f"  SIM Model: '{sim_card_model}', SIM ID: '{simcardid}', DATE: '{date_field}'")

                        values = [
                            state, location, quantity, chair_serial_no,
                            sim_card_model, sim_card_no, layer, company,
                            effective_from, effective_to, rental, simcardid,
                            None,  # Import_Date - 将在后面设置
                            None,  # Last_Updated - 将在后面设置
                            None,  # CurrentFlag - 默认为空
                            date_field
                        ]
                        
                        # 如果是重复序列号，弹出窗口设置生效日期
                        if is_duplicate:
                            duplicate_count += 1
                            update_progress(f"检测到重复序列号: {chair_serial_no}，请设置生效日期...")
                            
                            # 创建临时窗口作为父窗口
                            temp_win = tk.Toplevel(import_win)
                            temp_win.withdraw()  # 隐藏窗口，只用作父窗口
                            
                            # 调用设置生效日期的函数
                            if self.set_effective_dates_for_duplicate(chair_serial_no, values, temp_win):
                                # 用户确认了日期设置，执行插入
                                # 设置导入时间
                                import_time = safe_get_current_timestamp()
                                values[12] = import_time  # Import_Date
                                values[13] = import_time  # Last_Updated

                                cursor.execute("""
                                    INSERT INTO Equipment_ID
                                    (STATE, Location, Quantity, Chair_Serial_No, Sim_Card_Model, Sim_Card_No, Layer, Company,
                                     Effective_From, Effective_To, Rental, SIMCARDID, Import_Date, Last_Updated, CurrentFlag, DATE)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                """, tuple(values))
                                imported += 1
                                update_progress(f"✅ 成功导入重复序列号: {chair_serial_no}")
                            else:
                                # 用户取消了日期设置
                                failed += 1
                                update_progress(f"❌ 用户取消了重复序列号的导入: {chair_serial_no}")

                            # 销毁临时窗口
                            temp_win.destroy()
                        else:
                            # 非重复序列号，直接插入
                            # 设置导入时间
                            import_time = safe_get_current_timestamp()
                            values[12] = import_time  # Import_Date
                            values[13] = import_time  # Last_Updated

                            cursor.execute("""
                                INSERT INTO Equipment_ID
                                (STATE, Location, Quantity, Chair_Serial_No, Sim_Card_Model, Sim_Card_No, Layer, Company,
                                 Effective_From, Effective_To, Rental, SIMCARDID, Import_Date, Last_Updated, CurrentFlag, DATE)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, tuple(values))
                            imported += 1
                            update_progress(f"✅ 成功导入: {chair_serial_no}")
                    except Exception as e:
                        failed += 1
                        error_msg = str(e)
                        # 提供更详细的错误信息
                        if "UNIQUE constraint failed" in error_msg:
                            if "Chair_Serial_No" in error_msg:
                                error_detail = "序列号重复"
                            elif "SIMCARDID" in error_msg:
                                error_detail = "SIM卡ID重复"
                            else:
                                error_detail = "数据重复"
                        elif "NOT NULL constraint failed" in error_msg:
                            field_name = error_msg.split(".")[-1] if "." in error_msg else "未知字段"
                            error_detail = f"必填字段 {field_name} 为空"
                        elif "no such column" in error_msg:
                            error_detail = "数据库字段不匹配"
                        elif "datatype mismatch" in error_msg:
                            error_detail = "数据类型不匹配"
                        else:
                            error_detail = error_msg

                        update_progress(f"❌ 导入失败: {chair_serial_no} - {error_detail}")
                        print(f"详细错误信息: {error_msg}")
                
                conn.commit()
            
            update_progress(f"导入完成: 成功 {imported} 条，失败 {failed} 条，处理重复序列号 {duplicate_count} 个")
            
            # 添加完成按钮
            ttk.Button(main_frame, text="完成", command=import_win.destroy, width=15).pack(pady=10)

            # 强制刷新界面
            self.force_refresh_interface()

            # 记录日志
            add_log_entry("导入", f"从Excel导入设备记录", f"文件:{file_path}, 成功:{imported}, 失败:{failed}, 重复序列号:{duplicate_count}")
        except Exception as e:
            safe_show_error(f"导入Excel失败: {e}")

    def smart_complete_missing_fields(self, row_data, missing_fields, serial_no, row_number, parent_window):
        """智能补全缺失字段的界面"""
        try:
            # 创建补全窗口
            complete_win = tk.Toplevel(parent_window)
            complete_win.title(f"智能补全缺失字段 - 行{row_number}")
            complete_win.geometry("600x500")
            complete_win.transient(parent_window)
            complete_win.grab_set()

            # 主框架
            main_frame = ttk.Frame(complete_win, padding=15)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题信息
            title_frame = ttk.LabelFrame(main_frame, text="数据信息", padding=10)
            title_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(title_frame, text=f"序列号: {serial_no}", font=("Arial", 10, "bold")).pack(anchor="w")
            ttk.Label(title_frame, text=f"数据行: 第{row_number}行", font=("Arial", 10)).pack(anchor="w")
            ttk.Label(title_frame, text=f"缺少字段: {', '.join(missing_fields)}",
                     font=("Arial", 10), foreground="red").pack(anchor="w")

            # 数据编辑区域
            edit_frame = ttk.LabelFrame(main_frame, text="数据编辑", padding=10)
            edit_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            # 创建滚动区域
            canvas = tk.Canvas(edit_frame)
            scrollbar = ttk.Scrollbar(edit_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # 数据库字段列表（排除ID）
            db_fields = ["STATE", "Location", "Quantity", "Chair_Serial_No", "Sim_Card_Model",
                        "Sim_Card_No", "Layer", "Company", "Effective_From", "Effective_To",
                        "Rental", "SIMCARDID", "Import_Date", "Last_Updated", "CurrentFlag", "DATE"]

            entries = {}

            # 创建输入字段
            for i, field in enumerate(db_fields):
                row_frame = ttk.Frame(scrollable_frame)
                row_frame.pack(fill=tk.X, pady=2)

                # 字段标签
                label_text = field
                if field in missing_fields:
                    label_text += " *"  # 标记缺失字段

                label = ttk.Label(row_frame, text=label_text, width=15)
                label.pack(side=tk.LEFT, padx=(0, 10))

                # 输入框
                var = tk.StringVar()
                entry = ttk.Entry(row_frame, textvariable=var, width=40)
                entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

                # 自动填入已有数据
                if field == "Chair_Serial_No":
                    var.set(serial_no)
                    entry.configure(state="readonly")  # 序列号不可编辑
                else:
                    # 从Excel数据中获取值
                    excel_value = self.safe_get_excel_value(row_data, field)
                    if excel_value:
                        var.set(excel_value)

                    # 如果是缺失字段，高亮显示
                    if field in missing_fields:
                        entry.configure(style="Missing.TEntry")

                entries[field] = var

                # 添加字段说明
                if field in missing_fields:
                    help_label = ttk.Label(row_frame, text="← 必填", foreground="red", font=("Arial", 8))
                    help_label.pack(side=tk.LEFT, padx=(5, 0))

            # 配置样式
            style = ttk.Style()
            style.configure("Missing.TEntry", fieldbackground="lightyellow")

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # 按钮区域
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X)

            result = {"completed": False, "data": None}

            def save_completed_data():
                """保存补全后的数据"""
                try:
                    # 验证必填字段
                    for field in missing_fields:
                        value = entries[field].get().strip()
                        if not value:
                            messagebox.showerror("验证失败", f"字段 '{field}' 不能为空", parent=complete_win)
                            return

                    # 构建补全后的数据
                    completed_row = {}
                    for field, var in entries.items():
                        value = var.get().strip()
                        completed_row[field] = value if value else None

                    result["completed"] = True
                    result["data"] = completed_row
                    complete_win.destroy()

                except Exception as e:
                    messagebox.showerror("保存失败", f"保存数据时发生错误: {str(e)}", parent=complete_win)

            def cancel_completion():
                """取消补全"""
                result["completed"] = False
                result["data"] = None
                complete_win.destroy()

            ttk.Button(button_frame, text="保存并继续", command=save_completed_data).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="跳过此行", command=cancel_completion).pack(side=tk.LEFT, padx=5)

            # 等待用户操作
            complete_win.wait_window()

            return result["data"] if result["completed"] else None

        except Exception as e:
            print(f"智能补全界面错误: {e}")
            return None

    def safe_get_excel_value(self, row_data, field_name):
        """安全获取Excel数据中的字段值"""
        try:
            import pandas as pd
            value = row_data.get(field_name)

            if pd.isna(value):
                return ""

            str_value = str(value).strip()
            if str_value.lower() == 'nan':
                return ""

            return str_value
        except:
            return ""

    def export_to_excel(self):
        """将当前查询结果导出为Excel文件"""
        file_path = filedialog.asksaveasfilename(
            title="导出为Excel",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx")]
        )
        if not file_path:
            return
        try:
            with sqlite3.connect(DB_PATH) as conn:
                df = pd.read_sql_query(self.current_query, conn, params=self.current_params)
            df.to_excel(file_path, index=False)
            safe_show_info(f"数据已导出到: {file_path}")
            add_log_entry("导出", "导出设备数据到Excel", file_path)
        except Exception as e:
            safe_show_error(f"导出Excel失败: {e}")

   

  


    def add_new_id(self):
        import re
        db_fields = [
            "STATE", "Location", "Quantity", "Chair_Serial_No", "Sim_Card_Model", "SIMCARDID", "Sim_Card_No",
            "Layer", "Company", "DATE", "Rental", "Effective_From", "Effective_To"
        ]
        required_fields = ["STATE", "Location", "Quantity", "Chair_Serial_No"]

        def excel_date_auto_format(s, field=None):
            """自动识别并转换多种日期格式为标准格式 YYYY-MM-DD
            
            参数:
                s: 要转换的日期字符串
                field: 字段名称，用于对不同字段应用不同的处理逻辑
            """
            s = str(s).strip()
            if not s:
                return None
                
            # 对于DATE字段，直接返回原始内容，不进行任何处理
            if field == "DATE":
                return s
                
            # 处理多个日期的情况（例如：29/11/2024 09/12/2024 19/5/2025）
            if ' ' in s and not re.search(r'\d{1,2}:\d{1,2}', s):  # 包含空格但不是时间格式
                # 保留原始格式，不再只处理第一个日期
                return s
            
            # 处理常见日期格式
            date_formats = [
                # 标准格式 YYYY-MM-DD
                (r"^\d{4}-\d{1,2}-\d{1,2}$", "%Y-%m-%d"),
                # 标准格式 YYYY/MM/DD
                (r"^\d{4}/\d{1,2}/\d{1,2}$", "%Y/%m/%d"),
                # 6位数字格式 (DDMMYY)
                (r"^\d{6}$", "%d%m%y"),
                # 8位数字格式 (DDMMYYYY)
                (r"^\d{8}$", "%d%m%Y"),
                # 8位数字格式 (YYYYMMDD)
                (r"^\d{8}$", "%Y%m%d"),
                # 中文日期格式 YYYY年MM月DD日
                (r"^\d{4}年\d{1,2}月\d{1,2}日$", "%Y年%m月%d日"),
                # 带分隔符的格式 DD/MM/YYYY
                (r"^\d{1,2}/\d{1,2}/\d{4}$", "%d/%m/%Y"),
                # 带分隔符的格式 DD/MM/YY
                (r"^\d{1,2}/\d{1,2}/\d{2}$", "%d/%m/%y"),
                # 带分隔符的格式 MM/DD/YYYY
                (r"^\d{1,2}/\d{1,2}/\d{4}$", "%m/%d/%Y"),
                # 带分隔符的格式 DD-MM-YYYY
                (r"^\d{1,2}-\d{1,2}-\d{4}$", "%d-%m-%Y"),
                # 带分隔符的格式 MM-DD-YYYY
                (r"^\d{1,2}-\d{1,2}-\d{4}$", "%m-%d-%Y"),
                # 带分隔符的格式 DD.MM.YYYY
                (r"^\d{1,2}\.\d{1,2}\.\d{4}$", "%d.%m.%Y"),
                # 带分隔符的格式 YYYY.MM.DD
                (r"^\d{4}\.\d{1,2}\.\d{1,2}$", "%Y.%m.%d"),
                # 带时间的格式 YYYY-MM-DD HH:MM:SS
                (r"^\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{1,2}(:\d{1,2})?$", "%Y-%m-%d %H:%M:%S"),
                # 带时间的格式 YYYY-MM-DD HH:MM
                (r"^\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{1,2}$", "%Y-%m-%d %H:%M"),
                # 带时间的格式 DD/MM/YYYY HH:MM:SS
                (r"^\d{1,2}/\d{1,2}/\d{4}\s+\d{1,2}:\d{1,2}(:\d{1,2})?$", "%d/%m/%Y %H:%M:%S"),
                # 带时间的格式 DD/MM/YYYY HH:MM
                (r"^\d{1,2}/\d{1,2}/\d{4}\s+\d{1,2}:\d{1,2}$", "%d/%m/%Y %H:%M"),
                # ISO格式 YYYY-MM-DDTHH:MM:SS
                (r"^\d{4}-\d{1,2}-\d{1,2}T\d{1,2}:\d{1,2}(:\d{1,2})?$", "%Y-%m-%dT%H:%M:%S"),
                # Excel序列号格式 (数字)
                (r"^\d+\.\d+$", "excel")
            ]
            
            # 尝试所有格式
            for pattern, fmt in date_formats:
                if re.match(pattern, s):
                    try:
                        # 特殊处理Excel日期序列号
                        if fmt == "excel":
                            try:
                                # 尝试将数字转换为Excel日期
                                excel_date = float(s)
                                # Excel日期从1900年1月1日开始，序列号为1
                                # Python的datetime从1900年1月1日开始需要减去1
                                date_value = datetime.datetime(1899, 12, 30) + datetime.timedelta(days=excel_date)
                                return date_value.strftime("%Y-%m-%d")
                            except:
                                continue
                        
                        # 处理日期格式中的分隔符
                        s_normalized = s
                        if "/" in s and "-" in fmt:
                            s_normalized = s.replace("/", "-")
                        elif "-" in s and "/" in fmt:
                            s_normalized = s.replace("-", "/")
                        elif "." in s and "-" in fmt:
                            s_normalized = s.replace(".", "-")
                            
                        d = datetime.datetime.strptime(s_normalized, fmt)
                        # 处理两位数年份
                        if d.year < 2000 and len(str(d.year)) == 2:
                            d = d.replace(year=d.year + 2000)
                        return d.strftime("%Y-%m-%d")
                    except ValueError:
                        continue
            
            # 如果是日期时间格式，提取日期部分
            if re.search(r"\d{4}[/-]\d{1,2}[/-]\d{1,2}", s):
                date_part = re.search(r"(\d{4}[/-]\d{1,2}[/-]\d{1,2})", s).group(1)
                try:
                    d = datetime.datetime.strptime(date_part, "%Y-%m-%d")
                    return d.strftime("%Y-%m-%d")
                except ValueError:
                    pass
                try:
                    d = datetime.datetime.strptime(date_part, "%Y/%m/%d")
                    return d.strftime("%Y-%m-%d")
                except ValueError:
                    pass
            
            # 尝试使用pandas解析日期
            try:
                parsed_date = pd.to_datetime(s)
                return parsed_date.strftime("%Y-%m-%d")
            except:
                pass
                
            # 返回原始值，不报错
            return s

        def is_valid_date(s):
            if not s:
                return True
            try:
                datetime.datetime.strptime(s, "%Y-%m-%d")
                return True
            except ValueError:
                return False

        def check_chair_serial_no_unique(chair_serial_no):
            if not chair_serial_no:
                return False  # 空序列号不检查唯一性
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1 FROM Equipment_ID WHERE Chair_Serial_No = ?", (chair_serial_no,))
                return cursor.fetchone() is not None
                
        def set_effective_dates_for_duplicate(chair_serial_no, values, parent_window=None):
            """为重复序列号设置生效日期和失效日期"""
            # 创建日期设置窗口
            date_win = tk.Toplevel(parent_window or edit_win)
            date_win.title("设置生效日期")
            date_win.geometry("500x350")
            date_win.transient(parent_window or edit_win)
            date_win.grab_set()
            
            # 创建主框架
            main_frame = ttk.Frame(date_win, padding=10)
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 显示重复序列号信息
            ttk.Label(main_frame, text=f"检测到重复序列号: {chair_serial_no}", font=("Arial", 12, "bold")).pack(pady=(0, 10))
            ttk.Label(main_frame, text="请为此设备设置生效日期和失效日期，以区分不同时间段使用的相同序列号设备。", wraplength=450).pack(pady=(0, 20))
            
            # 创建日期输入框架
            date_frame = ttk.Frame(main_frame)
            date_frame.pack(fill=tk.X, pady=10)
            
            # 生效日期输入
            ttk.Label(date_frame, text="生效日期 (YYYY-MM-DD):", width=20).grid(row=0, column=0, padx=5, pady=5, sticky="w")
            effective_from_var = tk.StringVar()
            effective_from_entry = ttk.Entry(date_frame, textvariable=effective_from_var, width=30)
            effective_from_entry.grid(row=0, column=1, padx=5, pady=5, sticky="w")
            
            # 设置默认日期为今天
            today = datetime.datetime.now().strftime("%Y-%m-%d")
            effective_from_var.set(today)
            
            # 失效日期输入
            ttk.Label(date_frame, text="失效日期 (YYYY-MM-DD):", width=20).grid(row=1, column=0, padx=5, pady=5, sticky="w")
            effective_to_var = tk.StringVar()
            effective_to_entry = ttk.Entry(date_frame, textvariable=effective_to_var, width=30)
            effective_to_entry.grid(row=1, column=1, padx=5, pady=5, sticky="w")
            
            # 按钮框架
            btn_frame = ttk.Frame(main_frame)
            btn_frame.pack(fill=tk.X, pady=(20, 0))
            
            # 结果变量
            result = {"confirmed": False, "effective_from": None, "effective_to": None}
            
            def on_confirm():
                # 验证日期格式
                eff_from = effective_from_var.get().strip()
                eff_to = effective_to_var.get().strip()
                
                if eff_from and not is_valid_date(eff_from):
                    messagebox.showwarning("警告", "生效日期格式无效，请使用YYYY-MM-DD格式")
                    return
                if eff_to and not is_valid_date(eff_to):
                    messagebox.showwarning("警告", "失效日期格式无效，请使用YYYY-MM-DD格式")
                    return
                
                # 设置结果
                result["confirmed"] = True
                result["effective_from"] = eff_from
                result["effective_to"] = eff_to
                date_win.destroy()
            
            def on_cancel():
                date_win.destroy()
            
            ttk.Button(btn_frame, text="确认", command=on_confirm, width=10).pack(side=tk.RIGHT, padx=5)
            ttk.Button(btn_frame, text="取消", command=on_cancel, width=10).pack(side=tk.RIGHT, padx=5)
            
            # 设置焦点到生效日期输入框
            effective_from_entry.focus_set()
            
            # 等待窗口关闭
            date_win.wait_window()
            
            # 返回结果
            if result["confirmed"]:
                # 更新值列表中的生效日期和失效日期，确保空字符串转换为None
                eff_from = result["effective_from"].strip() if result["effective_from"] else None
                eff_to = result["effective_to"].strip() if result["effective_to"] else None

                values[db_fields.index("Effective_From")] = eff_from if eff_from else None
                values[db_fields.index("Effective_To")] = eff_to if eff_to else None
                return True
            return False

        def save_new(batch_data=None):
            today = datetime.datetime.now().strftime("%Y-%m-%d")
            failed_rows = []
            success_count = 0
            duplicate_serials = []
            invalid_date_rows = []
            missing_required_rows = []
            other_error_rows = []
            
            if batch_data:
                for row_idx, row in enumerate(batch_data):
                    row = list(row) + [None] * (len(db_fields) - len(row))
                    values = []
                    for i, field in enumerate(db_fields):
                        val = row[i] if i < len(row) else None
                        # 处理不同类型的值
                        if val is not None:
                            if isinstance(val, (int, float)):
                                if field in ["Effective_From", "Effective_To", "DATE"]:
                                    # 尝试将数字转换为日期
                                    val = excel_date_auto_format(str(val), field)
                                elif field == "SIMCARDID" and isinstance(val, float) and "E" in str(val):
                                    # 处理科学计数法格式的SIMCARDID
                                    val = '{:.0f}'.format(val)  # 转换为不带小数点的完整数字
                                else:
                                    val = str(val).strip()
                            else:
                                val = str(val).strip()
                            if val == "":
                                val = None
                        values.append(val)
                    
                    # 必填字段校验
                    if not all(values[db_fields.index(f)] for f in required_fields):
                        missing_required_rows.append(row_idx+1)
                        failed_rows.append(row_idx+1)
                        continue
                    
                    # 日期格式校验和转换
                    eff_from = values[db_fields.index("Effective_From")]
                    eff_to = values[db_fields.index("Effective_To")]
                    date_val = values[db_fields.index("DATE")]  # DATE字段直接使用原始值
                    
                    # 处理多日期格式
                    date_error = False
                    
                    # 对于多日期格式，保留原始格式
                    if eff_from and ' ' in eff_from and not re.search(r'\d{1,2}:\d{1,2}', eff_from):
                        # 多日期格式，不做处理
                        pass
                    elif eff_from and not is_valid_date(excel_date_auto_format(eff_from, "Effective_From")):
                        date_error = True
                    else:
                        eff_from = excel_date_auto_format(eff_from, "Effective_From")
                        
                    if eff_to and ' ' in eff_to and not re.search(r'\d{1,2}:\d{1,2}', eff_to):
                        # 多日期格式，不做处理
                        pass
                    elif eff_to and not is_valid_date(excel_date_auto_format(eff_to, "Effective_To")):
                        date_error = True
                    else:
                        eff_to = excel_date_auto_format(eff_to, "Effective_To")
                    
                    # DATE字段不再进行日期格式验证，直接保留原始内容
                    
                    if date_error:
                        invalid_date_rows.append(row_idx+1)
                        failed_rows.append(row_idx+1)
                        continue
                    
                    values[db_fields.index("Effective_From")] = eff_from
                    values[db_fields.index("Effective_To")] = eff_to
                    values[db_fields.index("DATE")] = date_val  # 保留原始内容
                    
                    # 序列号唯一性校验
                    chair_serial_no = values[db_fields.index("Chair_Serial_No")]
                    if check_chair_serial_no_unique(chair_serial_no):
                        # 弹出窗口让用户设置生效日期和失效日期
                        if not set_effective_dates_for_duplicate(chair_serial_no, values):
                            # 用户取消了设置，将此行添加到失败列表
                            duplicate_serials.append(chair_serial_no)
                            failed_rows.append(row_idx+1)
                            continue
                        # 用户已设置日期，继续添加
                    
                    # 设置导入日期和最后更新日期
                    import_date = today
                    last_updated = today
                    
                    try:
                        with sqlite3.connect(DB_PATH) as conn:
                            cursor = conn.cursor()
                            cursor.execute(f"""
                                INSERT INTO Equipment_ID
                                (STATE, Location, Quantity, Chair_Serial_No, Sim_Card_Model, SIMCARDID, Sim_Card_No,
                                Layer, Company, DATE, Rental, Effective_From, Effective_To, Import_Date, Last_Updated)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, (
                                values[0], values[1], values[2], values[3], values[4], values[5], values[6],
                                values[7], values[8], values[9], values[10], values[11], values[12], import_date, last_updated
                            ))
                            conn.commit()
                        success_count += 1
                    except Exception as e:
                        print(f"行 {row_idx+1} 添加失败: {e}")
                        other_error_rows.append(row_idx+1)
                        failed_rows.append(row_idx+1)
                
                # 构建详细的结果消息
                msg = f"成功添加 {success_count} 条记录。"
                if failed_rows:
                    msg += f"\n共有 {len(failed_rows)} 行添加失败。"
                    
                    if missing_required_rows:
                        msg += f"\n\n缺少必填字段的行: {missing_required_rows}"
                    if invalid_date_rows:
                        msg += f"\n\n日期格式无效的行: {invalid_date_rows}"
                    if duplicate_serials:
                        msg += f"\n\n序列号重复的行: {failed_rows[:5]}" + ("..." if len(failed_rows) > 5 else "")
                        msg += f"\n重复的序列号: {duplicate_serials[:5]}" + ("..." if len(duplicate_serials) > 5 else "")
                    if other_error_rows:
                        msg += f"\n\n其他错误的行: {other_error_rows}"
                
                # 记录日志
                add_log_entry("批量添加", f"批量添加设备记录", f"成功:{success_count},失败:{len(failed_rows)}")
                
                messagebox.showinfo("批量添加结果", msg)
                edit_win.destroy()

                # 强制刷新界面
                self.force_refresh_interface()
                return
            
            # 单条输入逻辑
            values = []
            for field in db_fields:
                label = field.replace("_", " ")
                if label == "Chair Serial No":
                    label = "Chair Serial No"
                val = entries.get(label, tk.Entry()).get().strip()
                if val == "":
                    val = None
                values.append(val)
            
            # 必填字段校验
            if not all(values[db_fields.index(f)] for f in required_fields):
                messagebox.showwarning("警告", f"以下字段为必填: {', '.join(required_fields)}")
                return
            
            # 日期格式校验
            eff_from = excel_date_auto_format(values[db_fields.index("Effective_From")], "Effective_From")
            eff_to = excel_date_auto_format(values[db_fields.index("Effective_To")], "Effective_To")
            date_val = values[db_fields.index("DATE")]  # DATE字段直接使用原始值，不进行格式转换
            
            if eff_from and not is_valid_date(eff_from):
                messagebox.showwarning("警告", "生效日期格式无效，请使用YYYY-MM-DD格式")
                return
            if eff_to and not is_valid_date(eff_to):
                messagebox.showwarning("警告", "失效日期格式无效，请使用YYYY-MM-DD格式")
                return
            
            values[db_fields.index("Effective_From")] = eff_from
            values[db_fields.index("Effective_To")] = eff_to
            values[db_fields.index("DATE")] = date_val  # 保留原始内容
            
            # 序列号唯一性校验
            chair_serial_no = values[db_fields.index("Chair_Serial_No")]
            if check_chair_serial_no_unique(chair_serial_no):
                # 弹出窗口设置生效日期和失效日期
                if not set_effective_dates_for_duplicate(chair_serial_no, values):
                    return
                # 获取设置的生效日期和失效日期
                eff_from = values[db_fields.index("Effective_From")]
                eff_to = values[db_fields.index("Effective_To")]
            
            # 执行插入
            import_date = today
            last_updated = today
            try:
                with sqlite3.connect(DB_PATH) as conn:
                    cursor = conn.cursor()
                    cursor.execute(f"""
                        INSERT INTO Equipment_ID
                        (STATE, Location, Quantity, Chair_Serial_No, Sim_Card_Model, SIMCARDID, Sim_Card_No,
                        Layer, Company, DATE, Rental, Effective_From, Effective_To, Import_Date, Last_Updated)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        values[0], values[1], values[2], values[3], values[4], values[5], values[6],
                        values[7], values[8], values[9], values[10], values[11], values[12], import_date, last_updated
                    ))
                    conn.commit()
                messagebox.showinfo("成功", "添加设备成功")
                edit_win.destroy()

                # 强制刷新界面
                self.force_refresh_interface()
            except Exception as e:
                messagebox.showerror("错误", f"添加设备失败: {e}")

        # 创建批量添加窗口
        def create_batch_window():
            batch_win = tk.Toplevel(edit_win)
            batch_win.title("批量添加设备")
            batch_win.geometry("800x600")
            batch_win.transient(edit_win)
            batch_win.grab_set()
            
            # 创建选项卡控件
            notebook = ttk.Notebook(batch_win)
            notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 创建粘贴区域选项卡
            paste_frame = ttk.Frame(notebook, padding=10)
            notebook.add(paste_frame, text="粘贴数据")
            
            # 创建Excel导入选项卡
            excel_frame = ttk.Frame(notebook, padding=10)
            notebook.add(excel_frame, text="Excel导入")
            
            # === 粘贴区域选项卡内容 ===
            paste_label_frame = ttk.LabelFrame(paste_frame, text="批量数据粘贴区域", padding=10)
            paste_label_frame.pack(fill=tk.BOTH, expand=True)
            
            # 添加说明文本
            ttk.Label(paste_label_frame, text="请在下方粘贴Excel数据，支持表格式布局：", anchor="w").pack(fill=tk.X)
            
            # 创建表头区域
            header_frame = ttk.Frame(paste_label_frame)
            header_frame.pack(fill=tk.X, pady=(5, 0))
            
            # 定义表头列名（与Excel表格对应）
            excel_columns = ["STATE", "Location", "Quantity", "Chair_Serial_No", "Sim_Card_Model", "SIMCARDID", "Sim_Card_No", "Layer", "Company", "DATE", "Rental"]
            
            # 显示表头
            for i, col in enumerate(excel_columns):
                ttk.Label(header_frame, text=col, font=("Arial", 9, "bold"), width=12, anchor="center", 
                          relief="solid", borderwidth=1, background="#e6e6e6").grid(row=0, column=i, padx=1, pady=1, sticky="nsew")
            
            # 创建文本框作为粘贴区域
            paste_text = tk.Text(paste_label_frame, height=15, width=80)
            paste_text.pack(fill=tk.BOTH, expand=True, pady=5)
            
            # 添加滚动条
            scrollbar = ttk.Scrollbar(paste_label_frame, command=paste_text.yview)
            paste_text.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # 添加使用说明
            ttk.Label(paste_label_frame, text="提示：您可以直接从Excel复制数据并粘贴到下方区域，系统会自动识别表格式布局。", foreground="green", anchor="w").pack(fill=tk.X, pady=(5, 0))
            ttk.Label(paste_label_frame, text="注意：生效日期和失效日期需要是标准日期格式，但DATE字段可以是任意格式。", foreground="green", anchor="w").pack(fill=tk.X)
            
            # 粘贴按钮
            paste_btn_frame = ttk.Frame(paste_frame)
            paste_btn_frame.pack(fill=tk.X, pady=10)
            
            def on_batch_paste():
                try:
                    content = paste_text.get("1.0", tk.END).strip()
                    if not content:
                        return
                    
                    lines = [line for line in content.split('\n') if line.strip()]
                    batch_data = []
                    
                    # 处理表格式数据
                    for line in lines:
                        if "\t" in line:
                            parts = line.split("\t")
                        else:
                            parts = line.split()
                            
                        # 确保数据与表头列对应
                        row_data = [None] * len(excel_columns)
                        for i, val in enumerate(parts):
                            if i < len(excel_columns):
                                row_data[i] = val.strip() if val.strip() else None
                        
                        # 添加到批量数据中
                        if any(row_data):  # 确保行不是全空
                            batch_data.append(row_data)
                    
                    if not batch_data:
                        messagebox.showwarning("警告", "没有检测到有效数据")
                        return
                    
                    # 将表格式数据转换为数据库字段格式
                    db_data = []
                    for row in batch_data:
                        db_row = [None] * len(db_fields)
                        
                        # 映射Excel列到数据库字段
                        for i, col in enumerate(excel_columns):
                            if i < len(row) and row[i] is not None:
                                # 找到对应的数据库字段索引
                                if col in db_fields:
                                    db_idx = db_fields.index(col)
                                    db_row[db_idx] = row[i]
                                # 特殊处理Chair_Serial_No字段（可能在Excel中是Chair Serial No）
                                elif col == "Chair_Serial_No" and "Chair Serial No" in db_fields:
                                    db_idx = db_fields.index("Chair Serial No")
                                    db_row[db_idx] = row[i]
                        
                        db_data.append(db_row)
                    
                    save_new(db_data)
                except Exception as e:
                    messagebox.showerror("错误", f"处理粘贴数据时出错: {e}")
                    print(f"粘贴数据处理错误详情: {e}")
            
            ttk.Button(paste_btn_frame, text="导入表格数据", command=on_batch_paste, width=15).pack(side=tk.LEFT, padx=5)
            ttk.Button(paste_btn_frame, text="清空", command=lambda: paste_text.delete("1.0", tk.END), width=10).pack(side=tk.LEFT, padx=5)
            
            # === Excel导入选项卡内容 ===
            excel_label_frame = ttk.LabelFrame(excel_frame, text="Excel文件导入", padding=10)
            excel_label_frame.pack(fill=tk.BOTH, expand=True)
            
            # 文件选择区域
            file_frame = ttk.Frame(excel_label_frame)
            file_frame.pack(fill=tk.X, pady=10)
            
            file_path_var = tk.StringVar()
            ttk.Label(file_frame, text="Excel文件:").pack(side=tk.LEFT, padx=5)
            ttk.Entry(file_frame, textvariable=file_path_var, width=50).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
            
            def select_excel_file():
                file_path = filedialog.askopenfilename(
                    title="选择Excel文件",
                    filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
                )
                if file_path:
                    file_path_var.set(file_path)
                    # 加载Excel文件预览
                    try:
                        df = pd.read_excel(file_path)
                        preview_data(df)
                    except Exception as e:
                        messagebox.showerror("错误", f"无法加载Excel文件: {e}")
            
            ttk.Button(file_frame, text="浏览...", command=select_excel_file).pack(side=tk.LEFT, padx=5)
            
            # 工作表选择
            sheet_frame = ttk.Frame(excel_label_frame)
            sheet_frame.pack(fill=tk.X, pady=5)
            
            sheet_var = tk.StringVar()
            ttk.Label(sheet_frame, text="工作表:").pack(side=tk.LEFT, padx=5)
            sheet_combo = ttk.Combobox(sheet_frame, textvariable=sheet_var, width=20)
            sheet_combo.pack(side=tk.LEFT, padx=5)
            
            # 预览区域
            preview_frame = ttk.LabelFrame(excel_label_frame, text="数据预览", padding=10)
            preview_frame.pack(fill=tk.BOTH, expand=True, pady=10)
            
            # 使用Treeview作为预览
            preview_tree = ttk.Treeview(preview_frame)
            preview_tree.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
            
            # 添加滚动条
            preview_vsb = ttk.Scrollbar(preview_frame, orient="vertical", command=preview_tree.yview)
            preview_hsb = ttk.Scrollbar(preview_frame, orient="horizontal", command=preview_tree.xview)
            preview_tree.configure(yscrollcommand=preview_vsb.set, xscrollcommand=preview_hsb.set)
            preview_vsb.pack(side=tk.RIGHT, fill=tk.Y)
            preview_hsb.pack(side=tk.BOTTOM, fill=tk.X)
            
            # 当前加载的DataFrame
            current_df = None
            
            def preview_data(df, sheet_name=None):
                nonlocal current_df
                
                # 清空现有数据
                for i in preview_tree.get_children():
                    preview_tree.delete(i)
                
                # 如果提供了sheet_name，则加载指定工作表
                if sheet_name is not None:
                    df = pd.read_excel(file_path_var.get(), sheet_name=sheet_name)
                
                # 保存当前DataFrame
                current_df = df
                
                # 设置列
                preview_tree["columns"] = list(df.columns)
                for col in df.columns:
                    preview_tree.heading(col, text=col)
                    preview_tree.column(col, width=100)
                
                # 添加数据 (最多显示100行)
                for i, row in df.head(100).iterrows():
                    values = [row[col] for col in df.columns]
                    preview_tree.insert("", "end", values=values)
            
            def load_sheets():
                try:
                    file_path = file_path_var.get()
                    if not file_path:
                        return
                    
                    # 获取所有工作表名称
                    xl = pd.ExcelFile(file_path)
                    sheet_names = xl.sheet_names
                    sheet_combo["values"] = sheet_names
                    
                    # 默认选择第一个工作表
                    if sheet_names:
                        sheet_var.set(sheet_names[0])
                        df = pd.read_excel(file_path, sheet_name=sheet_names[0])
                        preview_data(df)
                except Exception as e:
                    messagebox.showerror("错误", f"加载工作表失败: {e}")
            
            # 工作表切换事件
            def on_sheet_change(event):
                sheet_name = sheet_var.get()
                if sheet_name:
                    try:
                        df = pd.read_excel(file_path_var.get(), sheet_name=sheet_name)
                        preview_data(df)
                    except Exception as e:
                        messagebox.showerror("错误", f"加载工作表 {sheet_name} 失败: {e}")
            
            sheet_combo.bind("<<ComboboxSelected>>", on_sheet_change)
            
            # 刷新按钮
            ttk.Button(sheet_frame, text="刷新", command=load_sheets).pack(side=tk.LEFT, padx=5)
            
            # 导入按钮
            excel_btn_frame = ttk.Frame(excel_frame)
            excel_btn_frame.pack(fill=tk.X, pady=10)
            
            def import_from_excel():
                try:
                    if current_df is None or current_df.empty:
                        messagebox.showwarning("警告", "没有可导入的数据")
                        return
                    
                    # 将DataFrame转换为列表格式
                    batch_data = []
                    for _, row in current_df.iterrows():
                        # 将每行数据转换为列表
                        row_data = []
                        for field in db_fields:
                            # 尝试从DataFrame中获取对应列的数据
                            if field in current_df.columns:
                                value = row[field]
                                # 处理NaN值
                                if pd.isna(value):
                                    value = None
                                row_data.append(value)
                            else:
                                row_data.append(None)
                        batch_data.append(row_data)
                    
                    if not batch_data:
                        messagebox.showwarning("警告", "没有检测到有效数据")
                        return
                    
                    save_new(batch_data)
                except Exception as e:
                    messagebox.showerror("错误", f"导入Excel数据时出错: {e}")
            
            ttk.Button(excel_btn_frame, text="导入数据", command=import_from_excel, width=15).pack(side=tk.LEFT, padx=5)
            
            # 底部按钮
            btn_frame = ttk.Frame(batch_win)
            btn_frame.pack(fill=tk.X, padx=10, pady=10)
            ttk.Button(btn_frame, text="关闭", command=batch_win.destroy, width=10).pack(side=tk.RIGHT, padx=5)
            
            # 为文本框添加粘贴快捷键
            def text_paste(event):
                try:
                    paste_text.insert(tk.INSERT, batch_win.clipboard_get())
                    return "break"  # 阻止默认粘贴行为
                except Exception:
                    pass
            
            paste_text.bind("<Control-v>", text_paste)
            paste_text.bind("<Command-v>", text_paste)  # 为Mac用户
            
            # 窗口关闭时的清理
            def on_batch_window_close():
                batch_win.destroy()
            
            batch_win.protocol("WM_DELETE_WINDOW", on_batch_window_close)
            
            # 设置焦点到粘贴区域
            paste_text.focus_set()
        
        # 创建单条添加窗口
        edit_win = tk.Toplevel(self.root)
        edit_win.title("添加新设备")
        edit_win.geometry("900x700")
        
        # 主框架
        main_frame = ttk.Frame(edit_win, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 添加标题和说明
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(title_frame, text="添加新设备", font=("Arial", 14, "bold")).pack(side=tk.LEFT)
        ttk.Button(title_frame, text="批量添加", command=create_batch_window).pack(side=tk.RIGHT)
        
        # 添加说明文本框架
        info_frame = ttk.LabelFrame(main_frame, text="字段说明", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 添加说明文本
        ttk.Label(info_frame, text="DATE字段：可输入任意格式，系统不进行处理（示例：29/11/2024 09/12/2024 19/5/2025）", 
                 foreground="blue").pack(anchor="w", pady=2)
        ttk.Label(info_frame, text="生效日期/失效日期：必须为标准格式（YYYY-MM-DD），用于系统自动判断设备有效性。", 
                 foreground="red").pack(anchor="w", pady=2)
        
        # 字段框架
        fields_frame = ttk.LabelFrame(main_frame, text="设备信息", padding=10)
        fields_frame.pack(fill=tk.BOTH, expand=True)
        
        # 字段定义
        fields = [
            ("STATE", ""), ("Location", ""), ("Quantity", ""), ("Chair Serial No", ""),
            ("Sim Card Model", ""), ("Sim Card Id", ""), ("Sim Card No.", ""), ("Layer", ""),
            ("Company", ""), ("DATE", ""), ("Rental", ""), ("Effective_From", "YYYY-MM-DD"), ("Effective_To", "YYYY-MM-DD")
        ]
        
        # 创建输入字段
        entries = {}
        for idx, (label, default) in enumerate(fields):
            row = idx // 2
            col = (idx % 2) * 2
            
            # 添加特殊标记给DATE字段和日期字段
            if label == "DATE":
                ttk.Label(fields_frame, text=label+":", foreground="blue").grid(row=row, column=col, sticky="e", padx=5, pady=5)
            elif label in ["Effective_From", "Effective_To"]:
                ttk.Label(fields_frame, text=label+":", foreground="red").grid(row=row, column=col, sticky="e", padx=5, pady=5)
            else:
                ttk.Label(fields_frame, text=label+":").grid(row=row, column=col, sticky="e", padx=5, pady=5)
            
            ent = ttk.Entry(fields_frame, width=30)
            ent.grid(row=row, column=col+1, sticky="w", padx=5, pady=5)
            ent.insert(0, default)
            entries[label] = ent
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="保存", command=lambda: save_new(), width=15).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=edit_win.destroy, width=15).pack(side=tk.RIGHT, padx=5)
        
        # 窗口关闭时的清理
        def on_window_close():
            # 解绑所有粘贴事件，确保关闭窗口后不会影响主界面
            edit_win.unbind_all("<Control-v>")
            edit_win.unbind_all("<Command-v>")
            edit_win.destroy()
        
        edit_win.protocol("WM_DELETE_WINDOW", on_window_close)
        
        # 设置模态窗口
        edit_win.transient(self.root)
        edit_win.grab_set()
        edit_win.focus_set()
    
   




    def delete_selected_id(self):
        """删除设备"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("提示", "请先选择要删除的设备")
            return
        item = self.tree.item(selected[0])
        id_ = item["values"][0]
        serial = item["values"][4]
        if not messagebox.askyesno("确认", f"确定要删除序列号为 {serial} 的设备吗？"):
            return
        try:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM Equipment_ID WHERE ID=?", (id_,))
                conn.commit()
            add_log_entry("删除设备", f"删除ID:{id_}", serial)
            messagebox.showinfo("成功", "删除成功")

            # 强制刷新界面
            self.force_refresh_interface()
        except Exception as e:
            messagebox.showerror("错误", f"删除失败: {e}")


            
    def undo_operation(self):
        """通用撤回操作功能"""
        if not self.operation_history:
            safe_show_info("没有可撤销的操作")
            return

        # 获取最后一个操作
        last_operation = self.operation_history[-1]
        operation_type = last_operation['type']

        try:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                cursor.execute("BEGIN TRANSACTION")

                success_count = 0
                skipped_count = 0

                if operation_type in ['DELETE', 'BATCH_DELETE']:
                    # 撤销删除操作：恢复被删除的记录
                    for record in last_operation['before_data']:
                        # 检查记录是否已存在（避免主键冲突）
                        cursor.execute("SELECT 1 FROM Equipment_ID WHERE ID=?", (record[0],))
                        if cursor.fetchone():
                            skipped_count += 1
                            continue

                        # 检查序列号是否已存在（避免唯一约束冲突）
                        chair_serial_index = self.columns.index("Chair_Serial_No")
                        if chair_serial_index >= 0 and record[chair_serial_index]:
                            cursor.execute("SELECT 1 FROM Equipment_ID WHERE Chair_Serial_No=?", (record[chair_serial_index],))
                            if cursor.fetchone():
                                skipped_count += 1
                                continue

                        # 恢复记录 - 使用完整的列定义
                        all_cols = self.all_columns if hasattr(self, 'all_columns') else self.columns
                        placeholders = ", ".join(["?" for _ in range(len(all_cols))])
                        insert_sql = f"INSERT INTO Equipment_ID ({', '.join(all_cols)}) VALUES ({placeholders})"
                        cursor.execute(insert_sql, record)
                        success_count += 1

                elif operation_type in ['EDIT', 'BATCH_EDIT']:
                    # 撤销编辑操作：恢复到编辑前的状态
                    for before_record in last_operation['before_data']:
                        record_id = before_record[0]

                        # 检查记录是否仍然存在
                        cursor.execute("SELECT 1 FROM Equipment_ID WHERE ID=?", (record_id,))
                        if not cursor.fetchone():
                            skipped_count += 1
                            continue

                        # 恢复到编辑前的状态 - 使用完整的列定义
                        all_cols = self.all_columns if hasattr(self, 'all_columns') else self.columns
                        set_clause = ", ".join([f"{col}=?" for col in all_cols[1:]])  # 跳过ID列
                        cursor.execute(f"UPDATE Equipment_ID SET {set_clause} WHERE ID=?",
                                     before_record[1:] + (record_id,))
                        success_count += 1

                if success_count > 0:
                    cursor.execute("COMMIT")
                    # 移除已撤销的操作
                    self.operation_history.pop()

                    # 记录撤销操作的日志
                    add_log_entry("撤销操作", f"撤销{operation_type}操作，影响{success_count}条记录",
                                f"撤销时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

                    message = f"已撤销{operation_type}操作，恢复了 {success_count} 条记录"
                    if skipped_count > 0:
                        message += f"\n跳过了 {skipped_count} 条冲突记录"
                    safe_show_info(message)

                    # 强制刷新界面
                    self.force_refresh_interface()
                else:
                    cursor.execute("ROLLBACK")
                    safe_show_info("没有记录可以撤销，所有记录都存在冲突")

        except Exception as e:
            print(f"Error undoing operation: {e}")
            safe_show_error(f"撤销操作失败: {str(e)}")

    def undo_delete(self):
        """保持向后兼容的删除撤销功能"""
        self.undo_operation()
            
    def batch_edit_selected(self):
        """批量编辑选中的多条记录"""
        selected = self.tree.selection()
        if not selected:
            safe_show_info("请先选择要批量编辑的设备")
            return

        if len(selected) < 2:
            safe_show_info("请至少选择两条记录进行批量编辑")
            return
            
        # 获取选中的记录ID
        ids = []
        for sel in selected:
            item = self.tree.item(sel)
            ids.append(item["values"][0])
            
        # 创建批量编辑窗口
        edit_win = tk.Toplevel(self.root)
        edit_win.title(f"批量编辑 {len(ids)} 条记录")
        edit_win.geometry("600x650")
        edit_win.grab_set()
        
        # 创建编辑界面
        main_frame = ttk.Frame(edit_win, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(main_frame, text=f"正在批量编辑 {len(ids)} 条记录", font=("Arial", 12, "bold")).pack(pady=(0, 15))
        ttk.Label(main_frame, text="注意: Chair_Serial_No 不可批量编辑，其他字段的更改将应用到所有选中记录", foreground="red").pack(pady=(0, 15))
        
        # 创建字段输入框
        entries = {}
        fields_frame = ttk.Frame(main_frame)
        fields_frame.pack(fill=tk.BOTH, expand=True)
        
        # 获取第一条记录的数据作为默认值
        first_record = None
        try:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT * FROM Equipment_ID WHERE ID=?", (ids[0],))
                first_record = cursor.fetchone()
        except Exception as e:
            print(f"Error fetching first record: {e}")
            
        # 创建输入字段，Chair_Serial_No 除外
        # 使用完整的列定义，确保批量编辑显示所有字段
        all_cols = self.all_columns if hasattr(self, 'all_columns') else self.columns
        row = 0
        for i, col in enumerate(all_cols):
            if col == "ID" or col == "Chair_Serial_No":
                continue

            ttk.Label(fields_frame, text=f"{col}:").grid(row=row, column=0, sticky="e", padx=5, pady=5)
            var = tk.StringVar()
            if first_record and i < len(first_record):
                var.set(first_record[i] if first_record[i] is not None else "")

            entry = ttk.Entry(fields_frame, textvariable=var, width=40)
            entry.grid(row=row, column=1, sticky="w", padx=5, pady=5)
            entries[col] = var
            row += 1
            
        # 保存按钮功能
        def save_batch_edit():
            changes = {}
            # 获取所有记录的当前数据用于比较
            current_records = {}
            try:
                with sqlite3.connect(DB_PATH) as conn:
                    cursor = conn.cursor()
                    cursor.execute(f"SELECT * FROM Equipment_ID WHERE ID IN ({','.join(['?']*len(ids))})", ids)
                    records = cursor.fetchall()
                    for record in records:
                        current_records[record[0]] = record  # 以ID为键存储记录
            except Exception as e:
                print(f"Error fetching current records: {e}")
                return

            # 检查哪些字段被修改了
            for field, var in entries.items():
                new_value = var.get().strip()
                # 使用完整列定义来获取正确的字段索引
                all_cols = self.all_columns if hasattr(self, 'all_columns') else self.columns
                field_index = all_cols.index(field)

                # 获取第一条记录在该字段的原始值（作为默认显示值）
                first_record_value = None
                if first_record and field_index < len(first_record):
                    first_record_value = first_record[field_index]
                    if first_record_value is None:
                        first_record_value = ""
                    else:
                        first_record_value = str(first_record_value)

                # 只有当用户输入的值与第一条记录的原始值不同时，才认为是修改
                if new_value != first_record_value:
                    # 用户确实修改了这个字段
                    changes[field] = new_value if new_value else None

            if not changes:
                safe_show_info("没有进行任何修改")
                return
                
            try:
                with sqlite3.connect(DB_PATH) as conn:
                    cursor = conn.cursor()
                    # 开始事务，确保批量操作的原子性
                    cursor.execute("BEGIN TRANSACTION")
                    try:
                        # 保存编辑前的数据用于撤销
                        before_records = list(current_records.values())

                        # 构建更新语句
                        set_clause = ", ".join([f"{field}=?" for field in changes.keys()])
                        values = list(changes.values())

                        # 处理日期字段，增强日期格式兼容性
                        for i, field in enumerate(changes.keys()):
                            if field in ["Effective_From", "Effective_To"] and values[i]:
                                values[i] = standardize_date(values[i])

                        # 为每个ID执行更新
                        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        for id_ in ids:
                            cursor.execute(f"UPDATE Equipment_ID SET {set_clause}, Last_Updated=? WHERE ID=?",
                                          values + [timestamp, id_])

                        # 获取编辑后的数据
                        cursor.execute(f"SELECT * FROM Equipment_ID WHERE ID IN ({','.join(['?']*len(ids))})", ids)
                        after_records = cursor.fetchall()

                        # 记录操作历史
                        self.record_operation('BATCH_EDIT', before_data=before_records, after_data=after_records, affected_ids=ids)

                        # 提交事务
                        cursor.execute("COMMIT")

                        # 增强日志记录，添加更多关键信息
                        add_log_entry("批量编辑", f"批量编辑 {len(ids)} 条记录，修改字段: {list(changes.keys())}, 时间: {timestamp}", str(ids))
                        safe_show_info(f"已成功更新 {len(ids)} 条记录")
                        edit_win.destroy()

                        # 强制刷新界面
                        self.force_refresh_interface()
                    except Exception as e:
                        # 发生错误时回滚事务
                        cursor.execute("ROLLBACK")
                        raise e

            except Exception as e:
                print(f"Error batch editing: {e}")
                safe_show_error(f"批量编辑失败: {str(e)}")
                
        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(pady=15)
        ttk.Button(btn_frame, text="保存", command=save_batch_edit, width=15).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="取消", command=edit_win.destroy, width=15).pack(side=tk.LEFT, padx=10)


    def find_duplicate_serial(self):
        """查找当前生效期内有重复Chair_Serial_No的记录"""
        try:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                # 使用子查询包裹动态字段CurrentFlag，避免直接引用动态字段
                query = """
                SELECT Chair_Serial_No, 
                    GROUP_CONCAT(STATE || '|' || Location || '|' || Effective_From || '|' || Effective_To) AS Details,
                    COUNT(*) as Count
                FROM (
                    SELECT e.*,
                        CASE
                            WHEN EXISTS (SELECT 1 FROM Equipment_ID e2 
                                        WHERE e2.Chair_Serial_No = e.Chair_Serial_No 
                                        AND e2.ID != e.ID
                                        AND (e2.Effective_From IS NULL OR date(e2.Effective_From) <= date('now'))
                                        AND (e2.Effective_To IS NULL OR date(e2.Effective_To) >= date('now')))
                            THEN 'DUPLICATE'
                            ELSE 'ACTIVE'
                        END AS CurrentFlag
                    FROM Equipment_ID e
                    WHERE (e.Effective_From IS NULL OR date(e.Effective_From) <= date('now'))
                      AND (e.Effective_To IS NULL OR date(e.Effective_To) >= date('now'))
                ) AS subquery
                WHERE CurrentFlag = 'DUPLICATE'
                GROUP BY Chair_Serial_No
                ORDER BY Chair_Serial_No
                """
                cursor.execute(query)
                duplicates = cursor.fetchall()

            if not duplicates:
                safe_show_info("未找到标记为'DUPLICATE'的记录")
                return
                
            # 创建结果显示窗口
            result_win = tk.Toplevel(self.root)
            result_win.title("重复序列号记录")
            result_win.geometry("1200x600")
            result_win.grab_set()
            
            # 创建表格显示结果
            frame = ttk.Frame(result_win, padding=10)
            frame.pack(fill=tk.BOTH, expand=True)
            
            # 创建Treeview
            columns = ("Chair_Serial_No", "Record_Count", "Details")
            tree = ttk.Treeview(frame, columns=columns, show="headings")
            
            # 设置列标题和宽度
            column_widths = {
                "Chair_Serial_No": 150, 
                "Record_Count": 80,
                "Details": 800
            }
            
            tree.heading("Chair_Serial_No", text="序列号")
            tree.heading("Record_Count", text="重复数量")
            tree.heading("Details", text="记录详情（状态|位置|生效日期|失效日期）")
            
            for col in columns:
                tree.column(col, width=column_widths.get(col, 100), anchor="center")

            # 添加数据
            for row in duplicates:
                serial_no, details, count = row
                # 格式化详细信息
                detail_list = details.split(',')
                formatted_details = "\n".join([
                    f"记录{i+1}: {d.replace('|', ', ')}" 
                    for i, d in enumerate(detail_list)
                ])                
                tree.insert("", "end", values=(serial_no, count, formatted_details))

            # 添加滚动条
            vsb = ttk.Scrollbar(frame, orient="vertical", command=tree.yview)
            hsb = ttk.Scrollbar(frame, orient="horizontal", command=tree.xview)
            tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
            vsb.pack(side=tk.RIGHT, fill=tk.Y)
            hsb.pack(side=tk.BOTTOM, fill=tk.X)
            tree.pack(fill=tk.BOTH, expand=True)

            # 添加操作按钮
            btn_frame = ttk.Frame(result_win, padding=5)
            btn_frame.pack(fill=tk.X)

            # 导出到Excel功能
            def export_to_excel():
                try:
                    save_path = filedialog.asksaveasfilename(
                        parent=result_win,
                        title="保存重复序列号数据",
                        filetypes=[("Excel文件", "*.xlsx")],
                        defaultextension=".xlsx"
                    )
                    if not save_path:
                        return

                    # 准备数据
                    data = []
                    for item_id in tree.get_children():
                        data.append(tree.item(item_id, "values"))

                    # 创建DataFrame并保存
                    df = pd.DataFrame(data, columns=columns)
                    df.to_excel(save_path, index=False)
                    messagebox.showinfo("成功", f"已成功导出 {len(data)} 条重复序列号记录到 {save_path}", parent=result_win)
                except Exception as e:
                    messagebox.showerror("导出失败", f"导出重复序列号数据失败: {e}", parent=result_win)

            # 查看选中记录详情
            def view_selected_detail():
                selected = tree.selection()
                if not selected:
                    messagebox.showinfo("提示", "请先选择一条记录", parent=result_win)
                    return
                
                item = tree.item(selected[0])
                values = item["values"]
                serial_no = values[0]  # Chair_Serial_No在第一列
                
                # 根据序列号查找对应的记录
                try:
                    with sqlite3.connect(DB_PATH) as conn:
                        cursor = conn.cursor()
                        cursor.execute(
                            "SELECT ID FROM Equipment_ID WHERE Chair_Serial_No = ? AND CurrentFlag = 'DUPLICATE' LIMIT 1", 
                            (serial_no,)
                        )
                        result = cursor.fetchone()
                        if result:
                            # 打开编辑窗口查看详情
                            self.edit_equipment_id_by_id(result[0])
                        else:
                            messagebox.showinfo("提示", f"未找到序列号为 {serial_no} 的记录详情", parent=result_win)
                except Exception as e:
                    messagebox.showerror("错误", f"查找记录详情失败: {str(e)}", parent=result_win)

            # 设置生效日期功能
            def set_effective_dates():
                selected = tree.selection()
                if not selected:
                    messagebox.showinfo("提示", "请先选择一条记录", parent=result_win)
                    return
                
                item = tree.item(selected[0])
                values = item["values"]
                serial_no = values[0]  # Chair_Serial_No在第一列
                
                # 创建日期设置窗口
                date_win = tk.Toplevel(result_win)
                date_win.title(f"设置序列号 {serial_no} 的生效日期")
                date_win.geometry("500x400")
                date_win.grab_set()
                
                # 查询该序列号的所有记录
                try:
                    with sqlite3.connect(DB_PATH) as conn:
                        cursor = conn.cursor()
                        cursor.execute(
                            """SELECT ID, STATE, Location, Effective_From, Effective_To 
                               FROM Equipment_ID 
                               WHERE Chair_Serial_No = ? 
                               ORDER BY ID""", 
                            (serial_no,)
                        )
                        records = cursor.fetchall()
                        
                        if not records:
                            messagebox.showinfo("提示", f"未找到序列号为 {serial_no} 的记录", parent=date_win)
                            date_win.destroy()
                            return
                        
                        # 创建表格显示所有记录
                        date_frame = ttk.LabelFrame(date_win, text="当前记录", padding=10)
                        date_frame.pack(fill=tk.BOTH, expand=True)
                        
                        # 创建Treeview
                        date_columns = ("ID", "STATE", "Location", "Effective_From", "Effective_To")
                        date_tree = ttk.Treeview(date_frame, columns=date_columns, show="headings")
                        
                        # 设置列标题
                        for col in date_columns:
                            date_tree.heading(col, text=col)
                            date_tree.column(col, width=80, anchor="center")
                        
                        # 添加数据
                        for record in records:
                            date_tree.insert("", "end", values=record)
                        
                        # 添加滚动条
                        date_vsb = ttk.Scrollbar(date_frame, orient="vertical", command=date_tree.yview)
                        date_tree.configure(yscrollcommand=date_vsb.set)
                        date_vsb.pack(side=tk.RIGHT, fill=tk.Y)
                        date_tree.pack(fill=tk.BOTH, expand=True)
                        
                        # 创建日期输入框
                        input_frame = ttk.LabelFrame(date_win, text="设置生效日期", padding=10)
                        input_frame.pack(fill=tk.X, pady=10)
                        
                        # 选择记录ID
                        id_frame = ttk.Frame(input_frame)
                        id_frame.pack(fill=tk.X, pady=5)
                        ttk.Label(id_frame, text="选择记录ID:").pack(side=tk.LEFT, padx=5)
                        id_var = tk.StringVar()
                        id_combo = ttk.Combobox(id_frame, textvariable=id_var, width=10)
                        id_combo['values'] = [str(record[0]) for record in records]
                        id_combo.pack(side=tk.LEFT, padx=5)
                        id_combo.current(0)  # 默认选择第一条记录
                        
                        # 生效日期输入
                        from_frame = ttk.Frame(input_frame)
                        from_frame.pack(fill=tk.X, pady=5)
                        ttk.Label(from_frame, text="生效日期 (YYYY-MM-DD):").pack(side=tk.LEFT, padx=5)
                        from_var = tk.StringVar()
                        from_entry = ttk.Entry(from_frame, textvariable=from_var, width=15)
                        from_entry.pack(side=tk.LEFT, padx=5)
                        
                        # 失效日期输入
                        to_frame = ttk.Frame(input_frame)
                        to_frame.pack(fill=tk.X, pady=5)
                        ttk.Label(to_frame, text="失效日期 (YYYY-MM-DD):").pack(side=tk.LEFT, padx=5)
                        to_var = tk.StringVar()
                        to_entry = ttk.Entry(to_frame, textvariable=to_var, width=15)
                        to_entry.pack(side=tk.LEFT, padx=5)
                        
                        # 日期格式说明
                        ttk.Label(input_frame, text="注意: 日期格式为 YYYY-MM-DD，如 2023-01-01").pack(anchor="w", pady=5)
                        
                        # 更新选中记录的日期显示
                        def update_date_display(*args):
                            selected_id = id_var.get()
                            if not selected_id:
                                return
                                
                            for record in records:
                                if str(record[0]) == selected_id:
                                    from_var.set(record[3] or "")
                                    to_var.set(record[4] or "")
                                    break
                        
                        id_var.trace_add("write", update_date_display)
                        update_date_display()  # 初始化显示
                        
                        # 保存按钮
                        def save_dates():
                            record_id = id_var.get()
                            eff_from = from_var.get().strip()
                            eff_to = to_var.get().strip()
                            
                            # 验证日期格式
                            if eff_from and not is_valid_date(eff_from):
                                messagebox.showerror("错误", "生效日期格式无效，请使用YYYY-MM-DD格式", parent=date_win)
                                return
                                
                            if eff_to and not is_valid_date(eff_to):
                                messagebox.showerror("错误", "失效日期格式无效，请使用YYYY-MM-DD格式", parent=date_win)
                                return
                                
                            # 验证日期逻辑
                            if eff_from and eff_to and eff_from > eff_to:
                                messagebox.showerror("错误", "生效日期不能晚于失效日期", parent=date_win)
                                return
                            
                            try:
                                with sqlite3.connect(DB_PATH) as conn:
                                    cursor = conn.cursor()
                                    cursor.execute(
                                        "UPDATE Equipment_ID SET Effective_From = ?, Effective_To = ? WHERE ID = ?", 
                                        (eff_from or None, eff_to or None, record_id)
                                    )
                                    conn.commit()
                                    
                                    # 记录日志
                                    add_log_entry("更新", f"更新序列号 {serial_no} 的生效日期", 
                                                 f"ID: {record_id}, 生效日期: {eff_from}, 失效日期: {eff_to}")
                                    
                                    messagebox.showinfo("成功", f"已成功更新ID为 {record_id} 的记录日期", parent=date_win)
                                    
                                    # 刷新记录显示
                                    cursor.execute(
                                        """SELECT ID, STATE, Location, Effective_From, Effective_To 
                                           FROM Equipment_ID 
                                           WHERE Chair_Serial_No = ? 
                                           ORDER BY ID""", 
                                        (serial_no,)
                                    )
                                    records.clear()
                                    records.extend(cursor.fetchall())
                                    
                                    # 清空并重新加载树形视图
                                    for i in date_tree.get_children():
                                        date_tree.delete(i)
                                    for record in records:
                                        date_tree.insert("", "end", values=record)

                                    # 强制刷新主界面
                                    self.force_refresh_interface()

                            except Exception as e:
                                messagebox.showerror("错误", f"更新日期失败: {str(e)}", parent=date_win)
                        
                        btn_frame = ttk.Frame(date_win, padding=5)
                        btn_frame.pack(fill=tk.X, pady=10)
                        ttk.Button(btn_frame, text="保存", command=save_dates).pack(side=tk.LEFT, padx=5)
                        ttk.Button(btn_frame, text="关闭", command=date_win.destroy).pack(side=tk.RIGHT, padx=5)
                        
                except Exception as e:
                    messagebox.showerror("错误", f"加载记录失败: {str(e)}", parent=date_win)
                    date_win.destroy()

            ttk.Button(btn_frame, text="导出到Excel", command=export_to_excel).pack(side=tk.LEFT, padx=5)
            ttk.Button(btn_frame, text="查看选中记录详情", command=view_selected_detail).pack(side=tk.LEFT, padx=5)
            ttk.Button(btn_frame, text="设置生效日期", command=set_effective_dates).pack(side=tk.LEFT, padx=5)
            ttk.Button(btn_frame, text="关闭", command=result_win.destroy).pack(side=tk.RIGHT, padx=5)

            # 状态栏
            status_frame = ttk.Frame(result_win, padding=5)
            status_frame.pack(fill=tk.X, side=tk.BOTTOM)
            ttk.Label(status_frame, text=f"共找到 {len(duplicates)} 条标记为'DUPLICATE'的Chair_Serial_No记录").pack(side=tk.LEFT)

            # 记录日志
            add_log_entry("查询", "查找重复序列号", f"找到{len(duplicates)}条重复记录")

        except Exception as e:
            print(f"查找重复序列号时出错: {e}")
            messagebox.showerror("错误", f"查找重复序列号失败: {str(e)}")

    def view_expired(self):
        """查看过期设备"""
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        query = "SELECT * FROM Equipment_ID WHERE Effective_To IS NOT NULL AND Effective_To != '' AND Effective_To < ?"
        self.load_data(query, (today,))

    def query_unmatched_serial(self):
        """查询Daily_Equipment_Sales中指定字段全为NULL且IOT_Price或ZERO_Price大于5的最新记录"""
        query = """
            SELECT t.Chair_Serial_No, t.Sale_Date, t.IOT_Price, t.ZERO_Price
            FROM (
                SELECT Chair_Serial_No, Sale_Date, IOT_Price, ZERO_Price
                FROM Daily_Equipment_Sales
                WHERE State IS NULL
                AND Location IS NULL
                AND Quantity IS NULL
                AND Chair_Serial_No IS NOT NULL
                AND Layer IS NULL
                AND DATE IS NULL
                AND Rental IS NULL
                AND (IOT_Price > 5 OR ZERO_Price > 5)
            ) t
            INNER JOIN (
                SELECT Chair_Serial_No, MAX(Sale_Date) AS MaxDate
                FROM Daily_Equipment_Sales
                WHERE State IS NULL
                AND Location IS NULL
                AND Quantity IS NULL
                AND Chair_Serial_No IS NOT NULL
                AND Layer IS NULL
                AND DATE IS NULL
                AND Rental IS NULL
                AND (IOT_Price > 5 OR ZERO_Price > 5)
                GROUP BY Chair_Serial_No
            ) m
            ON t.Chair_Serial_No = m.Chair_Serial_No AND t.Sale_Date = m.MaxDate
        """

        try:
            with sqlite3.connect(DB_PATH) as conn:
                cursor = conn.cursor()
                cursor.execute(query)
                rows = cursor.fetchall()

            # 在新窗口中显示结果，而不是修改主界面
            self.show_unmatched_serial_window(rows)

        except Exception as e:
            messagebox.showerror("错误", f"查询异常销售记录失败: {e}")

    def show_unmatched_serial_window(self, rows):
        """在新窗口中显示未匹配的销售记录"""
        win = tk.Toplevel(self.root)
        win.title("未匹配销售记录")
        win.geometry("800x600")
        win.transient(self.root)
        win.grab_set()

        # 创建主框架
        main_frame = ttk.Frame(win, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="未匹配销售记录", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 10))

        # 说明文字
        info_label = ttk.Label(main_frame,
                              text="以下是Daily_Equipment_Sales中指定字段为空且价格大于5的记录\n双击记录可在主界面中搜索该序列号",
                              foreground="gray")
        info_label.pack(pady=(0, 10))

        # 创建Treeview
        tree_frame = ttk.Frame(main_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)

        columns = ("Chair_Serial_No", "Sale_Date", "IOT_Price", "ZERO_Price")
        tree = ttk.Treeview(tree_frame, columns=columns, show="headings")

        # 设置列标题和宽度
        column_widths = {"Chair_Serial_No": 200, "Sale_Date": 150, "IOT_Price": 100, "ZERO_Price": 100}
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=column_widths.get(col, 100), anchor="center")

        # 添加滚动条
        vsb = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
        hsb = ttk.Scrollbar(tree_frame, orient="horizontal", command=tree.xview)
        tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)

        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        tree.pack(fill=tk.BOTH, expand=True)

        # 插入数据
        for row in rows:
            tree.insert("", "end", values=row)

        # 双击事件：在主界面搜索选中的序列号
        def on_row_double_click(event):
            item = tree.selection()
            if item:
                serial = tree.item(item[0], "values")[0]
                # 在主界面中搜索该序列号
                self.serial_var.set(serial)
                self.search_data(reset_page=True)
                # 关闭当前窗口
                win.destroy()
                # 提示用户
                self.status_var.set(f"已搜索序列号: {serial}")

        tree.bind("<Double-1>", on_row_double_click)

        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=(10, 0))

        # 状态标签
        status_label = ttk.Label(btn_frame, text=f"共找到 {len(rows)} 条记录")
        status_label.pack(side=tk.LEFT)

        # 关闭按钮
        ttk.Button(btn_frame, text="关闭", command=win.destroy).pack(side=tk.RIGHT, padx=5)








if __name__ == "__main__":
    root = tk.Tk()
    app = EquipmentManager(root)
    root.mainloop()