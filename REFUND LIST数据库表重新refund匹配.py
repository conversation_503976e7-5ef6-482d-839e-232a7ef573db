import os
import shutil
import sqlite3
import datetime
import pandas as pd
import traceback
import glob

# 目标文件夹路径（可自定义）
BASE_DIR = r"C:\Users\<USER>\Desktop\Day Report"
TARGET_FOLDER_NAME = "Refunding_Retry"  # 可自定义
TARGET_DIR = os.path.join(BASE_DIR, TARGET_FOLDER_NAME)

# 自动检测并创建目标文件夹
if not os.path.exists(TARGET_DIR):
    os.makedirs(TARGET_DIR)
    print(f"已创建文件夹: {TARGET_DIR}")
else:
    print(f"文件夹已存在: {TARGET_DIR}")

DB_PATH = os.path.join(BASE_DIR, "database", "sales_reports.db")
BACKUP_DIR = TARGET_DIR  # 备份和日志都放在同一文件夹
SUCCESS_LOG = os.path.join(BACKUP_DIR, "retry_success.log")
FAIL_LOG = os.path.join(BACKUP_DIR, "retry_fail.csv")
NOTE_LOG = os.path.join(BACKUP_DIR, "retry_note.log")

PLATFORMS = ["IOT", "ZERO"]

class LogManager:
    def __init__(self, success_log, fail_log, note_log):
        self.success_log = success_log
        self.fail_log = fail_log
        self.note_log = note_log
        if not os.path.exists(self.fail_log):
            with open(self.fail_log, 'w', encoding='utf-8') as f:
                f.write('Transaction Date,Order ID,Refund,报错原因\n')
    def log_success(self, msg):
        with open(self.success_log, "a", encoding="utf-8") as f:
            f.write(f"{datetime.datetime.now()} {msg}\n")
    def log_fail(self, row, reason):
        with open(self.fail_log, "a", encoding="utf-8") as f:
            f.write(f"{row.get('Transaction Date','')},{row.get('Order ID','')},{row.get('Refund','')},{reason}\n")
    def log_note(self, msg):
        with open(self.note_log, "a", encoding="utf-8") as f:
            f.write(f"{datetime.datetime.now()} {msg}\n")

class BackupManager:
    def __init__(self, backup_dir):
        self.backup_dir = backup_dir
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def backup_table(self, table_name):
        backup_file = os.path.join(self.backup_dir, f"{table_name}_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        with sqlite3.connect(DB_PATH) as conn:
            df = pd.read_sql(f"SELECT * FROM {table_name}", conn)
            df.to_excel(backup_file, index=False)
        return backup_file
    
    def list_backups(self, table_name=None):
        """列出所有备份文件"""
        if table_name:
            pattern = os.path.join(self.backup_dir, f"{table_name}_backup_*.xlsx")
        else:
            pattern = os.path.join(self.backup_dir, "*_backup_*.xlsx")
        backups = glob.glob(pattern)
        backups.sort(key=os.path.getmtime, reverse=True)  # 按时间倒序
        return backups
    
    def restore_table(self, backup_file, table_name):
        """从备份文件恢复表数据"""
        try:
            df = pd.read_excel(backup_file)
            with sqlite3.connect(DB_PATH) as conn:
                # 清空表
                conn.execute(f"DELETE FROM {table_name}")
                # 恢复数据
                df.to_sql(table_name, conn, if_exists='append', index=False)
                conn.commit()
            return True
        except Exception as e:
            print(f"恢复失败: {e}")
            return False

REFUND_LIST_COLUMNS = [
    'Transaction Date','Settlement Date','Refund Date','Merchant Ref ID','Transaction ID','Channel','Order ID','Currency','Billing','Actual','Refund','MDR','GST','Status','Refund Fee','Quantity','Reference1','Reference2','PROCESS'
]

def standardize_date(date_str):
    if pd.isna(date_str) or not str(date_str).strip():
        return None
    s = str(date_str).strip()
    try:
        return pd.to_datetime(s).strftime("%Y-%m-%d")
    except Exception:
        return s

def clear_process_status():
    """清空所有REFUND_LIST的PROCESS状态"""
    try:
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()
            cursor.execute("UPDATE REFUND_LIST SET PROCESS=NULL")
            affected_rows = cursor.rowcount
            conn.commit()
            print(f"已清空 {affected_rows} 条记录的PROCESS状态")
            return True
    except Exception as e:
        print(f"清空PROCESS状态失败: {e}")
        return False

def process_refunds(filter_condition="PROCESS IS NULL OR PROCESS='未找到'"):
    """处理退款，支持不同的筛选条件"""
    log_mgr = LogManager(SUCCESS_LOG, FAIL_LOG, NOTE_LOG)
    backup_mgr = BackupManager(BACKUP_DIR)
    
    # 备份表
    print("正在备份数据...")
    backup_files = {}
    for plat in PLATFORMS:
        backup_files[f"{plat}_Sales"] = backup_mgr.backup_table(f"{plat}_Sales")
    backup_files["REFUND_LIST"] = backup_mgr.backup_table("REFUND_LIST")
    print("数据备份完成！")
    
    try:
        with sqlite3.connect(DB_PATH) as conn:
            conn.execute("PRAGMA foreign_keys = OFF")  # 关闭外键约束
            cursor = conn.cursor()
            
            # 获取需要处理的退款记录
            df = pd.read_sql(f"SELECT * FROM REFUND_LIST WHERE {filter_condition}", conn)
            total = len(df)
            
            if total == 0:
                print("没有需要处理的退款记录")
                return
            
            print(f"找到 {total} 条需要处理的退款记录")
            
            success_count = 0
            fail_count = 0
            
            for idx, row in df.iterrows():
                try:
                    refund_date = standardize_date(row.get('Transaction Date',''))
                    order_id = str(row.get('Order ID','')).strip()
                    refund_amt = float(row.get('Refund',0)) if str(row.get('Refund','')).strip() else 0
                    
                    if not order_id or refund_amt <= 0:
                        log_mgr.log_fail(row, f"第{idx+1}行 订单ID或退款金额无效")
                        fail_count += 1
                        continue
                    
                    found = False
                    
                    for plat in PLATFORMS:
                        table_name = f"{plat}_Sales"
                        
                        # 查找匹配的订单
                        if refund_date:
                            cursor.execute(f"SELECT rowid, * FROM {table_name} WHERE (Order_time=? OR substr(Order_time,1,10)=?)", (refund_date, refund_date))
                        else:
                            cursor.execute(f"SELECT rowid, * FROM {table_name}")
                        
                        candidates = cursor.fetchall()
                        if not candidates:
                            continue
                        
                        col_names = [d[0] for d in cursor.description]
                        
                        for c in candidates:
                            try:
                                db_id = str(c[col_names.index('Equipment_ID')]) if 'Equipment_ID' in col_names else ''
                                db_no = str(c[col_names.index('Order_No')]) if 'Order_No' in col_names else ''
                                
                                # 匹配逻辑：短ID匹配Equipment_ID，长ID匹配Order_No
                                if (len(order_id) <= 9 and order_id == db_id) or (len(order_id) > 9 and order_id == db_no):
                                    found = True
                                    rowid = c[0]
                                    orig_amt = float(c[col_names.index('Order_price')]) if 'Order_price' in col_names else 0
                                    new_amt = orig_amt - refund_amt
                                    
                                    log_mgr.log_note(f"第{idx+1}行 找到匹配: 表={table_name}, rowid={rowid}, 原金额={orig_amt}, 退款={refund_amt}, 新金额={new_amt}")
                                    
                                    if new_amt <= 0:
                                        # 删除记录
                                        cursor.execute(f"DELETE FROM {table_name} WHERE rowid=?", (rowid,))
                                        affected = cursor.rowcount
                                        process_status = '已删除'
                                        log_mgr.log_note(f"删除操作影响行数: {affected}")
                                    else:
                                        # 更新金额
                                        cursor.execute(f"UPDATE {table_name} SET Order_price=? WHERE rowid=?", (new_amt, rowid))
                                        affected = cursor.rowcount
                                        process_status = '已退款'
                                        log_mgr.log_note(f"更新操作影响行数: {affected}")
                                    
                                    # 更新REFUND_LIST状态
                                    cursor.execute("UPDATE REFUND_LIST SET PROCESS=? WHERE id=?", (process_status, row['id']))
                                    
                                    # 立即提交事务
                                    conn.commit()
                                    
                                    # 验证操作结果
                                    if process_status == '已删除':
                                        cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE rowid=?", (rowid,))
                                        exists = cursor.fetchone()[0]
                                        if exists == 0:
                                            log_mgr.log_success(f"第{idx+1}行 删除成功验证: 记录已不存在")
                                        else:
                                            log_mgr.log_note(f"第{idx+1}行 删除验证失败: 记录仍存在")
                                    else:
                                        cursor.execute(f"SELECT Order_price FROM {table_name} WHERE rowid=?", (rowid,))
                                        current_price = cursor.fetchone()
                                        if current_price and abs(float(current_price[0]) - new_amt) < 0.01:
                                            log_mgr.log_success(f"第{idx+1}行 更新成功验证: 新金额={current_price[0]}")
                                        else:
                                            log_mgr.log_note(f"第{idx+1}行 更新验证失败: 期望={new_amt}, 实际={current_price[0] if current_price else 'NULL'}")
                                    
                                    log_mgr.log_success(f"第{idx+1}行 退款成功，状态:{process_status}")
                                    success_count += 1
                                    break
                            except Exception as e:
                                log_mgr.log_note(f"第{idx+1}行 处理候选记录时出错: {e}")
                                continue
                        
                        if found:
                            break
                    
                    if not found:
                        cursor.execute("UPDATE REFUND_LIST SET PROCESS=? WHERE id=?", ('未找到', row['id']))
                        conn.commit()
                        log_mgr.log_fail(row, f"第{idx+1}行 未找到匹配数据")
                        fail_count += 1
                    
                    print(f"正在处理第{idx+1}/{total}条记录 (成功:{success_count}, 失败:{fail_count})")
                    
                except Exception as e:
                    log_mgr.log_fail(row, f"第{idx+1}行 处理异常: {e}\n{traceback.format_exc()}")
                    fail_count += 1
                    try:
                        conn.rollback()
                    except:
                        pass
            
            print(f"\n处理完成！成功: {success_count}, 失败: {fail_count}")
            
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        print(traceback.format_exc())

def show_backup_menu():
    """显示备份管理菜单"""
    backup_mgr = BackupManager(BACKUP_DIR)
    
    while True:
        print("\n=== 备份管理 ===")
        print("1. 查看所有备份")
        print("2. 恢复IOT_Sales表")
        print("3. 恢复ZERO_Sales表")
        print("4. 恢复REFUND_LIST表")
        print("0. 返回主菜单")
        
        choice = input("请选择操作: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            backups = backup_mgr.list_backups()
            if not backups:
                print("没有找到备份文件")
            else:
                print("\n备份文件列表:")
                for i, backup in enumerate(backups, 1):
                    filename = os.path.basename(backup)
                    mtime = datetime.datetime.fromtimestamp(os.path.getmtime(backup))
                    print(f"{i}. {filename} ({mtime.strftime('%Y-%m-%d %H:%M:%S')})")
        elif choice in ['2', '3', '4']:
            table_map = {'2': 'IOT_Sales', '3': 'ZERO_Sales', '4': 'REFUND_LIST'}
            table_name = table_map[choice]
            
            backups = backup_mgr.list_backups(table_name)
            if not backups:
                print(f"没有找到{table_name}的备份文件")
                continue
            
            print(f"\n{table_name}备份文件:")
            for i, backup in enumerate(backups, 1):
                filename = os.path.basename(backup)
                mtime = datetime.datetime.fromtimestamp(os.path.getmtime(backup))
                print(f"{i}. {filename} ({mtime.strftime('%Y-%m-%d %H:%M:%S')})")
            
            try:
                idx = int(input("请选择要恢复的备份 (输入序号): ")) - 1
                if 0 <= idx < len(backups):
                    confirm = input(f"确认恢复 {table_name} 表吗? (y/N): ").strip().lower()
                    if confirm == 'y':
                        if backup_mgr.restore_table(backups[idx], table_name):
                            print(f"{table_name} 表恢复成功！")
                        else:
                            print(f"{table_name} 表恢复失败！")
                    else:
                        print("取消恢复")
                else:
                    print("无效的序号")
            except ValueError:
                print("请输入有效的数字")
        else:
            print("无效选择")

def main_menu():
    """主菜单"""
    while True:
        print("\n=== 退款处理系统 ===")
        print("1. 处理未找到的退款记录")
        print("2. 清空所有PROCESS状态并重新处理")
        print("3. 处理所有退款记录 (包括已处理)")
        print("4. 备份管理")
        print("5. 查看处理统计")
        print("0. 退出")
        
        choice = input("请选择操作: ").strip()
        
        if choice == '0':
            print("退出程序")
            break
        elif choice == '1':
            print("\n开始处理未找到的退款记录...")
            process_refunds("PROCESS IS NULL OR PROCESS='未找到'")
        elif choice == '2':
            confirm = input("确认清空所有PROCESS状态并重新处理吗? (y/N): ").strip().lower()
            if confirm == 'y':
                if clear_process_status():
                    print("\n开始重新处理所有退款记录...")
                    process_refunds("1=1")  # 处理所有记录
            else:
                print("取消操作")
        elif choice == '3':
            confirm = input("确认处理所有退款记录 (包括已处理) 吗? (y/N): ").strip().lower()
            if confirm == 'y':
                print("\n开始处理所有退款记录...")
                process_refunds("1=1")  # 处理所有记录
            else:
                print("取消操作")
        elif choice == '4':
            show_backup_menu()
        elif choice == '5':
            try:
                with sqlite3.connect(DB_PATH) as conn:
                    df = pd.read_sql("SELECT PROCESS, COUNT(*) as count FROM REFUND_LIST GROUP BY PROCESS", conn)
                    print("\n处理状态统计:")
                    for _, row in df.iterrows():
                        status = row['PROCESS'] if row['PROCESS'] else '未处理'
                        print(f"{status}: {row['count']} 条")
            except Exception as e:
                print(f"查询统计失败: {e}")
        else:
            print("无效选择")

if __name__ == "__main__":
    main_menu()