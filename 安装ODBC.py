def download_driver():
    """下载SQLite ODBC驱动到临时目录"""
    print("正在下载SQLite ODBC驱动...")
    temp_dir = os.path.join(os.environ["TEMP"], "SQLiteODBC")
    os.makedirs(temp_dir, exist_ok=True)
    download_path = os.path.join(temp_dir, "sqliteodbc.zip")
    
    session = requests.Session()
    session.verify = False
    session.mount('https://', requests.adapters.HTTPAdapter(max_retries=3))
    
    try:
        response = session.get(ODBC_DRIVER_URL, stream=True)
        response.raise_for_status()
        with open(download_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        print(f"下载完成，文件保存在: {download_path}")
        return download_path
    except requests.exceptions.RequestException as e:
        print(f"下载失败: {str(e)}")
        raise

def main():
    try:
        requests.packages.urllib3.disable_warnings()
        
        driver_path = download_driver()
        
        print("\n请按以下步骤手动安装:")
        print(f"1. 解压下载的文件: {driver_path}")
        print("2. 右键点击'sqlite3odbc.dll'，选择'以管理员身份运行'")
        print("3. 按照安装向导完成安装")
        print("4. 安装完成后，按任意键继续配置DSN...")
        input("按任意键继续...")
        
        configure_dsn()
        
        if verify_installation():
            create_excel_with_powerquery()
            print("\n配置成功！已创建包含所有表连接的Excel文件")
        else:
            print("\n配置出现问题，请检查错误信息")
    except Exception as e:
        print(f"安装过程中出错: {str(e)}")
        traceback.print_exc()