import os
import sqlite3
import shutil
import datetime
import traceback

# 配置常量
DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
BACKUP_DIR = r"C:\Users\<USER>\Desktop\Day Report\database\backups"

def create_backup():
    """创建数据库备份"""
    try:
        if not os.path.exists(BACKUP_DIR):
            os.makedirs(BACKUP_DIR)
            
        date_str = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(BACKUP_DIR, f"sales_reports_{date_str}.db")
        
        # 确保数据库文件存在
        if not os.path.exists(DB_PATH):
            raise FileNotFoundError(f"数据库文件不存在: {DB_PATH}")
            
        # 创建备份
        shutil.copy2(DB_PATH, backup_path)
        print(f"数据库备份已创建: {backup_path}")
        return backup_path
        
    except Exception as e:
        print(f"创建备份失败: {str(e)}")
        traceback.print_exc()
        return None

def restore_backup(backup_path=None):
    """从备份恢复数据库"""
    try:
        if backup_path is None:
            # 查找最新的备份文件
            if not os.path.exists(BACKUP_DIR):
                raise FileNotFoundError("备份目录不存在")
                
            backups = [f for f in os.listdir(BACKUP_DIR) if f.startswith("sales_reports_") and f.endswith(".db")]
            if not backups:
                raise FileNotFoundError("未找到备份文件")
                
            backups.sort(reverse=True)
            backup_path = os.path.join(BACKUP_DIR, backups[0])
        
        if not os.path.exists(backup_path):
            raise FileNotFoundError(f"备份文件不存在: {backup_path}")
            
        # 关闭所有可能的数据库连接
        if os.path.exists(DB_PATH):
            os.remove(DB_PATH)
            
        # 恢复备份
        shutil.copy2(backup_path, DB_PATH)
        print(f"数据库已从 {backup_path} 成功恢复")
        return True
        
    except Exception as e:
        print(f"恢复失败: {str(e)}")
        traceback.print_exc()
        return False

def list_backups():
    """列出所有可用备份"""
    try:
        if not os.path.exists(BACKUP_DIR):
            print("备份目录不存在")
            return []
            
        backups = [f for f in os.listdir(BACKUP_DIR) if f.startswith("sales_reports_") and f.endswith(".db")]
        backups.sort(reverse=True)
        
        print("\n可用备份列表:")
        for i, backup in enumerate(backups, 1):
            print(f"{i}. {backup}")
            
        return backups
        
    except Exception as e:
        print(f"列出备份失败: {str(e)}")
        return []

if __name__ == "__main__":
    print("数据库备份与恢复工具")
    print("1. 创建备份")
    print("2. 恢复最新备份")
    print("3. 选择备份恢复")
    print("4. 列出所有备份")
    
    choice = input("请选择操作 (1-4): ")
    
    if choice == "1":
        backup_path = create_backup()
        if backup_path:
            print("备份创建成功")
        else:
            print("备份创建失败")
            
    elif choice == "2":
        if restore_backup():
            print("恢复成功")
        else:
            print("恢复失败")
            
    elif choice == "3":
        backups = list_backups()
        if backups:
            try:
                selection = int(input("请输入要恢复的备份编号: ")) - 1
                if 0 <= selection < len(backups):
                    backup_path = os.path.join(BACKUP_DIR, backups[selection])
                    if restore_backup(backup_path):
                        print("恢复成功")
                    else:
                        print("恢复失败")
                else:
                    print("无效的选择")
            except ValueError:
                print("请输入有效的数字")
                
    elif choice == "4":
        list_backups()
        
    else:
        print("无效的选择")