# -*- coding: utf-8 -*-
"""
标准化错误处理模块
统一处理各种异常，提供友好的错误提示
"""

import sqlite3
import traceback
import logging
from typing import Optional, Dict, Any, Callable
from tkinter import messagebox
from constants import Config


class ErrorType:
    """错误类型常量"""
    DATABASE = "database"
    FILE_IO = "file_io"
    VALIDATION = "validation"
    PERMISSION = "permission"
    NETWORK = "network"
    UNKNOWN = "unknown"


class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.error_callbacks: Dict[str, Callable] = {}
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("ErrorHandler")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(Config.LOG.LOG_FORMAT)
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def register_callback(self, error_type: str, callback: Callable):
        """注册错误回调函数"""
        self.error_callbacks[error_type] = callback
    
    def handle_exception(self, e: Exception, operation: str = "操作", 
                        show_dialog: bool = True, log_error: bool = True) -> Dict[str, Any]:
        """统一异常处理"""
        error_info = self._analyze_exception(e, operation)
        
        if log_error:
            self._log_error(error_info, e)
        
        if show_dialog:
            self._show_error_dialog(error_info)
        
        # 调用注册的回调函数
        error_type = error_info.get("type", ErrorType.UNKNOWN)
        if error_type in self.error_callbacks:
            try:
                self.error_callbacks[error_type](error_info)
            except Exception as callback_error:
                self.logger.error(f"错误回调函数执行失败: {callback_error}")
        
        return error_info
    
    def _analyze_exception(self, e: Exception, operation: str) -> Dict[str, Any]:
        """分析异常类型和原因"""
        error_info = {
            "operation": operation,
            "exception": e,
            "type": ErrorType.UNKNOWN,
            "message": "",
            "user_message": "",
            "suggestions": []
        }
        
        # 数据库相关错误
        if isinstance(e, sqlite3.Error):
            error_info.update(self._handle_database_error(e, operation))
        
        # 文件IO错误
        elif isinstance(e, (FileNotFoundError, PermissionError, IOError)):
            error_info.update(self._handle_file_error(e, operation))
        
        # 值错误（通常是验证错误）
        elif isinstance(e, ValueError):
            error_info.update(self._handle_validation_error(e, operation))
        
        # 其他异常
        else:
            error_info.update(self._handle_generic_error(e, operation))
        
        return error_info
    
    def _handle_database_error(self, e: sqlite3.Error, operation: str) -> Dict[str, Any]:
        """处理数据库错误"""
        error_str = str(e).lower()
        
        if "unique constraint failed" in error_str:
            if "chair_serial_no" in error_str:
                return {
                    "type": ErrorType.DATABASE,
                    "message": f"椅子序列号重复: {e}",
                    "user_message": "椅子序列号已存在，请使用不同的序列号",
                    "suggestions": ["检查序列号是否已存在", "使用新的序列号", "更新现有记录"]
                }
            else:
                return {
                    "type": ErrorType.DATABASE,
                    "message": f"数据重复: {e}",
                    "user_message": "数据重复，请检查输入的信息",
                    "suggestions": ["检查重复的字段", "修改输入数据"]
                }
        
        elif "not null constraint failed" in error_str:
            field_name = error_str.split(".")[-1] if "." in error_str else "未知字段"
            return {
                "type": ErrorType.DATABASE,
                "message": f"必填字段为空: {e}",
                "user_message": f"必填字段 '{field_name}' 不能为空",
                "suggestions": [f"请填写 {field_name} 字段", "检查所有必填字段"]
            }
        
        elif "no such table" in error_str:
            return {
                "type": ErrorType.DATABASE,
                "message": f"表不存在: {e}",
                "user_message": "数据库表不存在，可能需要重新初始化数据库",
                "suggestions": ["重新初始化数据库", "检查数据库文件完整性"]
            }
        
        elif "database is locked" in error_str:
            return {
                "type": ErrorType.DATABASE,
                "message": f"数据库被锁定: {e}",
                "user_message": "数据库正在被其他程序使用，请稍后重试",
                "suggestions": ["关闭其他使用数据库的程序", "等待片刻后重试"]
            }
        
        else:
            return {
                "type": ErrorType.DATABASE,
                "message": f"数据库错误: {e}",
                "user_message": f"{operation}失败，数据库操作出错",
                "suggestions": ["检查数据库连接", "重试操作", "联系技术支持"]
            }
    
    def _handle_file_error(self, e: Exception, operation: str) -> Dict[str, Any]:
        """处理文件错误"""
        if isinstance(e, FileNotFoundError):
            return {
                "type": ErrorType.FILE_IO,
                "message": f"文件未找到: {e}",
                "user_message": "文件不存在或路径不正确",
                "suggestions": ["检查文件路径", "确认文件是否存在", "选择正确的文件"]
            }
        
        elif isinstance(e, PermissionError):
            return {
                "type": ErrorType.PERMISSION,
                "message": f"权限不足: {e}",
                "user_message": "没有足够的权限访问文件",
                "suggestions": ["以管理员身份运行程序", "检查文件权限", "选择其他位置保存"]
            }
        
        else:
            return {
                "type": ErrorType.FILE_IO,
                "message": f"文件操作错误: {e}",
                "user_message": f"{operation}失败，文件操作出错",
                "suggestions": ["检查文件是否被占用", "重试操作", "选择其他文件"]
            }
    
    def _handle_validation_error(self, e: ValueError, operation: str) -> Dict[str, Any]:
        """处理验证错误"""
        error_str = str(e).lower()
        
        if "date" in error_str or "时间" in error_str:
            return {
                "type": ErrorType.VALIDATION,
                "message": f"日期格式错误: {e}",
                "user_message": "日期格式不正确，请使用 YYYY-MM-DD 格式",
                "suggestions": ["使用正确的日期格式", "检查日期是否有效", "使用日期选择器"]
            }
        
        elif "number" in error_str or "数字" in error_str:
            return {
                "type": ErrorType.VALIDATION,
                "message": f"数字格式错误: {e}",
                "user_message": "请输入有效的数字",
                "suggestions": ["检查数字格式", "确保输入的是数字", "移除非数字字符"]
            }
        
        else:
            return {
                "type": ErrorType.VALIDATION,
                "message": f"验证错误: {e}",
                "user_message": f"输入数据不符合要求: {e}",
                "suggestions": ["检查输入格式", "参考示例数据", "联系技术支持"]
            }
    
    def _handle_generic_error(self, e: Exception, operation: str) -> Dict[str, Any]:
        """处理通用错误"""
        return {
            "type": ErrorType.UNKNOWN,
            "message": f"未知错误: {e}",
            "user_message": f"{operation}失败，发生未知错误",
            "suggestions": ["重试操作", "重启程序", "联系技术支持"]
        }
    
    def _log_error(self, error_info: Dict[str, Any], exception: Exception):
        """记录错误日志"""
        self.logger.error(
            f"操作: {error_info['operation']}, "
            f"类型: {error_info['type']}, "
            f"消息: {error_info['message']}"
        )
        self.logger.debug(f"异常详情: {traceback.format_exc()}")
    
    def _show_error_dialog(self, error_info: Dict[str, Any]):
        """显示错误对话框"""
        title = f"{error_info['operation']}失败"
        message = error_info['user_message']
        
        if error_info['suggestions']:
            message += "\n\n建议解决方案："
            for i, suggestion in enumerate(error_info['suggestions'], 1):
                message += f"\n{i}. {suggestion}"
        
        messagebox.showerror(title, message)


class ValidationError(Exception):
    """自定义验证异常"""
    def __init__(self, field: str, value: Any, message: str):
        self.field = field
        self.value = value
        self.message = message
        super().__init__(f"字段 '{field}' 验证失败: {message}")


class DatabaseOperationError(Exception):
    """自定义数据库操作异常"""
    def __init__(self, operation: str, message: str, original_error: Exception = None):
        self.operation = operation
        self.original_error = original_error
        super().__init__(f"数据库操作 '{operation}' 失败: {message}")


# 全局错误处理器实例
error_handler = ErrorHandler()


# 便捷函数
def handle_error(e: Exception, operation: str = "操作", show_dialog: bool = True) -> Dict[str, Any]:
    """便捷的错误处理函数"""
    return error_handler.handle_exception(e, operation, show_dialog)


def safe_execute(func: Callable, operation: str = "操作", 
                default_return: Any = None, show_dialog: bool = True) -> Any:
    """安全执行函数，自动处理异常"""
    try:
        return func()
    except Exception as e:
        handle_error(e, operation, show_dialog)
        return default_return


def validate_required_fields(data: Dict[str, Any], required_fields: list) -> None:
    """验证必填字段"""
    for field in required_fields:
        if field not in data or not data[field]:
            raise ValidationError(field, data.get(field), "此字段为必填项")


def validate_date_format(date_str: str, field_name: str = "日期") -> None:
    """验证日期格式"""
    if not date_str:
        return
    
    import re
    from datetime import datetime
    
    # 检查基本格式
    if not re.match(r'^\d{4}-\d{1,2}-\d{1,2}$', date_str):
        raise ValidationError(field_name, date_str, "日期格式应为 YYYY-MM-DD")
    
    # 检查日期有效性
    try:
        datetime.strptime(date_str, Config.VALIDATION.DATE_FORMAT)
    except ValueError:
        raise ValidationError(field_name, date_str, "无效的日期")


def validate_numeric_range(value: Any, field_name: str, min_val: float = None, max_val: float = None) -> None:
    """验证数值范围"""
    if value is None or value == "":
        return
    
    try:
        num_value = float(value)
    except (ValueError, TypeError):
        raise ValidationError(field_name, value, "必须是有效的数字")
    
    if min_val is not None and num_value < min_val:
        raise ValidationError(field_name, value, f"值不能小于 {min_val}")
    
    if max_val is not None and num_value > max_val:
        raise ValidationError(field_name, value, f"值不能大于 {max_val}")


# 装饰器
def error_handler_decorator(operation: str = "操作", show_dialog: bool = True):
    """错误处理装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                handle_error(e, operation, show_dialog)
                return None
        return wrapper
    return decorator
