import sqlite3
import shutil
import os

DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
BACKUP_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports_backup.db"

def backup_db():
    if os.path.exists(DB_PATH):
        shutil.copy2(DB_PATH, BACKUP_PATH)
        print(f"已备份数据库到: {BACKUP_PATH}")
    else:
        print("数据库文件不存在，无法备份！")

def fix_zero_sales_id():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT rowid FROM ZERO_Sales WHERE id IS NULL")
    rows = cursor.fetchall()
    count = 0
    for (rowid,) in rows:
        cursor.execute("SELECT MAX(id) FROM ZERO_Sales")
        max_id = cursor.fetchone()[0]
        new_id = 1 if max_id is None else max_id + 1
        cursor.execute("UPDATE ZERO_Sales SET id = ? WHERE rowid = ?", (new_id, rowid))
        count += 1
    conn.commit()
    conn.close()
    print(f"已修正 {count} 条 id 字段为空的数据。")

if __name__ == "__main__":
    backup_db()
    fix_zero_sales_id()