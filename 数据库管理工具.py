import os
import sqlite3
import shutil
import datetime
import pandas as pd
import traceback

# 配置常量
DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
BACKUP_DIR = r"C:\Users\<USER>\Desktop\Day Report\database\backups"

def log_message(msg, log_file=r"C:\Users\<USER>\Desktop\Day Report\database_management.log"):
    """记录日志消息"""
    with open(log_file, "a", encoding="utf-8") as f:
        f.write(f"{datetime.datetime.now()} {msg}\n")

def backup_database(backup_name=None):
    """备份数据库
    
    Args:
        backup_name: 备份文件名，如果为None则使用当前日期时间
    
    Returns:
        tuple: (成功标志, 消息)
    """
    try:
        # 确保备份目录存在
        os.makedirs(BACKUP_DIR, exist_ok=True)
        
        # 生成备份文件名
        if backup_name is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"sales_reports_backup_{timestamp}.db"
        
        backup_path = os.path.join(BACKUP_DIR, backup_name)
        
        # 检查数据库文件是否存在
        if not os.path.exists(DB_PATH):
            msg = f"数据库文件不存在: {DB_PATH}"
            log_message(msg)
            return False, msg
        
        # 复制数据库文件
        shutil.copy2(DB_PATH, backup_path)
        
        msg = f"数据库备份成功: {backup_path}"
        log_message(msg)
        return True, msg
    
    except Exception as e:
        tb = traceback.format_exc()
        msg = f"数据库备份失败: {str(e)}\n{tb}"
        log_message(msg)
        return False, msg

def restore_database(backup_file):
    """从备份恢复数据库
    
    Args:
        backup_file: 备份文件名或完整路径
    
    Returns:
        tuple: (成功标志, 消息)
    """
    try:
        # 处理备份文件路径
        if os.path.dirname(backup_file) == "":
            backup_path = os.path.join(BACKUP_DIR, backup_file)
        else:
            backup_path = backup_file
        
        # 检查备份文件是否存在
        if not os.path.exists(backup_path):
            msg = f"备份文件不存在: {backup_path}"
            log_message(msg)
            return False, msg
        
        # 先备份当前数据库
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        current_backup = f"pre_restore_backup_{timestamp}.db"
        success, _ = backup_database(current_backup)
        
        if not success:
            msg = "恢复前备份当前数据库失败，操作已取消"
            log_message(msg)
            return False, msg
        
        # 恢复数据库
        shutil.copy2(backup_path, DB_PATH)
        
        msg = f"数据库已从 {backup_path} 恢复"
        log_message(msg)
        return True, msg
    
    except Exception as e:
        tb = traceback.format_exc()
        msg = f"数据库恢复失败: {str(e)}\n{tb}"
        log_message(msg)
        return False, msg

def list_backups():
    """列出所有可用的数据库备份
    
    Returns:
        list: 备份文件列表
    """
    try:
        if not os.path.exists(BACKUP_DIR):
            return []
        
        backups = [f for f in os.listdir(BACKUP_DIR) if f.endswith(".db")]
        return sorted(backups, reverse=True)  # 最新的排在前面
    
    except Exception as e:
        log_message(f"获取备份列表失败: {str(e)}")
        return []

def update_views_for_app_sales():
    """更新视图以包含APP销售数据
    
    Returns:
        tuple: (成功标志, 消息)
    """
    try:
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()
            
            # 1. 创建APP_Daily_Sales视图
            cursor.execute("DROP VIEW IF EXISTS APP_Daily_Sales")
            app_daily_sql = """
            CREATE VIEW APP_Daily_Sales AS
            SELECT 
                Equipment_ID as Chair_Serial_No,
                strftime('%Y-%m-%d', Order_time) as Sale_Date,
                SUM(Order_Price) as Daily_APP_Price,
                COUNT(*) as Daily_APP_Count
            FROM APP_Sales
            GROUP BY Equipment_ID, Sale_Date
            """
            cursor.execute(app_daily_sql)
            
            # 2. 更新Daily_Equipment_Sales视图以包含APP销售数据
            cursor.execute("DROP VIEW IF EXISTS Daily_Equipment_Sales")
            daily_equipment_sql = """
            CREATE VIEW Daily_Equipment_Sales AS
            SELECT 
                e.Chair_Serial_No,
                e.STATE,
                e.Location,
                e.Quantity,
                e.Layer,
                e.Effective_From,
                e.Effective_To,
                e.Rental,
                e.DATE,
                d.Sale_Date,
                COALESCE(i.Daily_IOT_Price, 0) as IOT_Price,
                COALESCE(z.Daily_ZERO_Price, 0) as ZERO_Price,
                COALESCE(a.Daily_APP_Price, 0) as APP_Price,
                COALESCE(i.Daily_IOT_Count, 0) as IOT_Count,
                COALESCE(z.Daily_ZERO_Count, 0) as ZERO_Count,
                COALESCE(a.Daily_APP_Count, 0) as APP_Count,
                (COALESCE(i.Daily_IOT_Price, 0) + COALESCE(z.Daily_ZERO_Price, 0) + COALESCE(a.Daily_APP_Price, 0)) as Total_Price
            FROM Equipment_Valid e
            CROSS JOIN (
                SELECT DISTINCT Sale_Date FROM 
                (
                    SELECT Sale_Date FROM IOT_Daily_Sales
                    UNION
                    SELECT Sale_Date FROM ZERO_Daily_Sales
                    UNION
                    SELECT Sale_Date FROM APP_Daily_Sales
                )
            ) d
            LEFT JOIN IOT_Daily_Sales i ON e.Chair_Serial_No = i.Chair_Serial_No AND d.Sale_Date = i.Sale_Date
            LEFT JOIN ZERO_Daily_Sales z ON e.Chair_Serial_No = z.Chair_Serial_No AND d.Sale_Date = z.Sale_Date
            LEFT JOIN APP_Daily_Sales a ON e.Chair_Serial_No = a.Chair_Serial_No AND d.Sale_Date = a.Sale_Date
            WHERE e.Version_Rank = 1
            AND d.Sale_Date BETWEEN e.Effective_From AND e.Effective_To

            UNION ALL

            SELECT 
                i.Chair_Serial_No,
                NULL AS STATE,
                NULL AS Location,
                NULL AS Quantity,
                NULL AS Layer,
                NULL AS Effective_From,
                NULL AS Effective_To,
                NULL AS Rental,
                NULL AS DATE,
                i.Sale_Date,
                i.Daily_IOT_Price,
                0 AS ZERO_Price,
                0 AS APP_Price,
                i.Daily_IOT_Count,
                0 AS ZERO_Count,
                0 AS APP_Count,
                i.Daily_IOT_Price AS Total_Price
            FROM IOT_Daily_Sales i
            LEFT JOIN Equipment_Valid e
                ON i.Chair_Serial_No = e.Chair_Serial_No
                AND e.Version_Rank = 1
                AND i.Sale_Date BETWEEN e.Effective_From AND e.Effective_To
            WHERE e.Chair_Serial_No IS NULL

            UNION ALL

            SELECT 
                z.Chair_Serial_No,
                NULL AS STATE,
                NULL AS Location,
                NULL AS Quantity,
                NULL AS Layer,
                NULL AS Effective_From,
                NULL AS Effective_To,
                NULL AS Rental,
                NULL AS DATE,
                z.Sale_Date,
                0 AS IOT_Price,
                z.Daily_ZERO_Price,
                0 AS APP_Price,
                0 AS IOT_Count,
                z.Daily_ZERO_Count,
                0 AS APP_Count,
                z.Daily_ZERO_Price AS Total_Price
            FROM ZERO_Daily_Sales z
            LEFT JOIN Equipment_Valid e
                ON z.Chair_Serial_No = e.Chair_Serial_No
                AND e.Version_Rank = 1
                AND z.Sale_Date BETWEEN e.Effective_From AND e.Effective_To
            WHERE e.Chair_Serial_No IS NULL
            
            UNION ALL
            
            SELECT 
                a.Chair_Serial_No,
                NULL AS STATE,
                NULL AS Location,
                NULL AS Quantity,
                NULL AS Layer,
                NULL AS Effective_From,
                NULL AS Effective_To,
                NULL AS Rental,
                NULL AS DATE,
                a.Sale_Date,
                0 AS IOT_Price,
                0 AS ZERO_Price,
                a.Daily_APP_Price,
                0 AS IOT_Count,
                0 AS ZERO_Count,
                a.Daily_APP_Count,
                a.Daily_APP_Price AS Total_Price
            FROM APP_Daily_Sales a
            LEFT JOIN Equipment_Valid e
                ON a.Chair_Serial_No = e.Chair_Serial_No
                AND e.Version_Rank = 1
                AND a.Sale_Date BETWEEN e.Effective_From AND e.Effective_To
            WHERE e.Chair_Serial_No IS NULL
            """
            cursor.execute(daily_equipment_sql)
            
            # 3. 更新Sales_Combined视图以包含APP销售数据
            cursor.execute("DROP VIEW IF EXISTS Sales_Combined")
            view_sql = """
            CREATE VIEW Sales_Combined AS
            SELECT 
                e.Chair_Serial_No,
                e.STATE,
                e.Location,
                e.Quantity,
                e.Layer,
                CASE 
                    WHEN e.Effective_From IS NULL THEN date('1900-01-01')
                    ELSE date(e.Effective_From)
                END as Effective_From,
                CASE 
                    WHEN e.Effective_To IS NULL THEN date('9999-12-31')
                    ELSE date(e.Effective_To)
                END as Effective_To,
                e.Rental,
                COUNT(i.ID) as IOT_Sales,
                COUNT(z.ID) as ZERO_Sales,
                COUNT(a.ID) as APP_Sales,
                COALESCE(SUM(i.Order_Price), 0) as IOT_Order_Price,
                COALESCE(SUM(z.Order_Price), 0) as ZERO_Order_Price,
                COALESCE(SUM(a.Order_Price), 0) as APP_Order_Price,
                (COALESCE(SUM(i.Order_Price), 0) + COALESCE(SUM(z.Order_Price), 0) + COALESCE(SUM(a.Order_Price), 0)) as Total_Order_Price,
                MAX(i.Order_time) as IOT_Date,
                MAX(z.Order_time) as ZERO_Date,
                MAX(a.Order_time) as APP_Date,
                ROW_NUMBER() OVER (
                    PARTITION BY e.Chair_Serial_No 
                    ORDER BY 
                        CASE 
                            WHEN e.Effective_From IS NULL THEN 1 
                            ELSE 0 
                        END DESC,
                        date(e.Effective_From) DESC,
                        e.ID DESC
                ) as Version_Rank
            FROM Equipment_ID e
            LEFT JOIN IOT_Sales i ON e.Chair_Serial_No = i.Equipment_ID
            LEFT JOIN ZERO_Sales z ON e.Chair_Serial_No = z.Equipment_ID
            LEFT JOIN APP_Sales a ON e.Chair_Serial_No = a.Equipment_ID
            GROUP BY e.Chair_Serial_No, e.STATE, e.Location, e.Quantity, e.Layer, 
                     e.Effective_From, e.Effective_To, e.Rental, e.ID
            """
            cursor.execute(view_sql)
            
            # 4. 更新Daily_Sales视图以包含APP销售数据
            cursor.execute("DROP VIEW IF EXISTS Daily_Sales")
            daily_sales_sql = """
            CREATE VIEW Daily_Sales AS
            SELECT 
                strftime('%Y-%m-%d', Order_time) as Sale_Date,
                SUM(COALESCE(Order_Price, 0)) as Daily_Total_Price
            FROM (
                SELECT Order_time, Order_Price FROM IOT_Sales
                UNION ALL
                SELECT Order_time, Order_Price FROM ZERO_Sales
                UNION ALL
                SELECT Order_time, Order_Price FROM APP_Sales
            )
            GROUP BY Sale_Date
            """
            cursor.execute(daily_sales_sql)
            
            conn.commit()
            msg = "成功更新视图以包含APP销售数据"
            log_message(msg)
            return True, msg
    
    except Exception as e:
        tb = traceback.format_exc()
        msg = f"更新视图失败: {str(e)}\n{tb}"
        log_message(msg)
        return False, msg

def main():
    """主函数，提供简单的命令行界面"""
    print("===== 数据库管理工具 =====")
    print("1. 备份数据库")
    print("2. 恢复数据库")
    print("3. 更新视图以包含APP销售数据")
    print("4. 退出")
    
    choice = input("请选择操作 [1-4]: ")
    
    if choice == "1":
        success, msg = backup_database()
        print(msg)
    
    elif choice == "2":
        backups = list_backups()
        if not backups:
            print("没有可用的备份文件")
            return
        
        print("\n可用的备份文件:")
        for i, backup in enumerate(backups):
            print(f"{i+1}. {backup}")
        
        try:
            idx = int(input("\n请选择要恢复的备份文件编号 [输入0取消]: "))
            if idx == 0:
                print("操作已取消")
                return
            
            if 1 <= idx <= len(backups):
                confirm = input(f"确定要恢复备份 {backups[idx-1]}? [y/N]: ")
                if confirm.lower() == 'y':
                    success, msg = restore_database(backups[idx-1])
                    print(msg)
                else:
                    print("操作已取消")
            else:
                print("无效的选择")
        
        except ValueError:
            print("请输入有效的数字")
    
    elif choice == "3":
        confirm = input("此操作将更新视图以包含APP销售数据，确定继续? [y/N]: ")
        if confirm.lower() == 'y':
            success, msg = update_views_for_app_sales()
            print(msg)
        else:
            print("操作已取消")
    
    elif choice == "4":
        print("退出程序")
        return
    
    else:
        print("无效的选择，请重新输入")

if __name__ == "__main__":
    main()