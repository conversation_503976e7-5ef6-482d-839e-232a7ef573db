# -*- coding: utf-8 -*-
"""
异步操作和用户体验增强方案
解决界面卡顿问题，提升用户体验
"""

import tkinter as tk
from tkinter import ttk
import threading
import queue
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Callable, Any, List, Dict


class AsyncOperationManager:
    """异步操作管理器"""
    
    def __init__(self, parent_window):
        self.parent = parent_window
        self.executor = ThreadPoolExecutor(max_workers=3)
        self.result_queue = queue.Queue()
        self.active_operations = {}
        self.operation_id = 0
        
        # 启动结果处理线程
        self._start_result_processor()
    
    def _start_result_processor(self):
        """启动结果处理线程"""
        def process_results():
            while True:
                try:
                    result = self.result_queue.get(timeout=0.1)
                    if result is None:  # 停止信号
                        break
                    
                    operation_id, success, data, callback = result
                    
                    # 在主线程中执行回调
                    if callback:
                        self.parent.after(0, lambda: callback(success, data))
                    
                    # 清理操作记录
                    if operation_id in self.active_operations:
                        del self.active_operations[operation_id]
                        
                except queue.Empty:
                    continue
                except Exception as e:
                    print(f"⚠️ 结果处理异常: {e}")
        
        self.result_thread = threading.Thread(target=process_results, daemon=True)
        self.result_thread.start()
    
    def execute_async(self, operation_func: Callable, callback: Callable = None, 
                     progress_callback: Callable = None, operation_name: str = "操作") -> int:
        """执行异步操作"""
        self.operation_id += 1
        current_id = self.operation_id
        
        def async_wrapper():
            try:
                print(f"🚀 开始异步操作: {operation_name} (ID: {current_id})")
                
                # 执行操作
                if progress_callback:
                    result = operation_func(progress_callback)
                else:
                    result = operation_func()
                
                # 将结果放入队列
                self.result_queue.put((current_id, True, result, callback))
                print(f"✅ 异步操作完成: {operation_name} (ID: {current_id})")
                
            except Exception as e:
                print(f"❌ 异步操作失败: {operation_name} (ID: {current_id}), 错误: {e}")
                self.result_queue.put((current_id, False, str(e), callback))
        
        # 提交任务
        future = self.executor.submit(async_wrapper)
        self.active_operations[current_id] = {
            'name': operation_name,
            'future': future,
            'start_time': time.time()
        }
        
        return current_id
    
    def cancel_operation(self, operation_id: int) -> bool:
        """取消异步操作"""
        if operation_id in self.active_operations:
            operation = self.active_operations[operation_id]
            if operation['future'].cancel():
                del self.active_operations[operation_id]
                print(f"🚫 已取消操作: {operation['name']} (ID: {operation_id})")
                return True
        return False
    
    def get_active_operations(self) -> Dict:
        """获取活动操作列表"""
        return self.active_operations.copy()
    
    def shutdown(self):
        """关闭异步操作管理器"""
        self.executor.shutdown(wait=False)
        self.result_queue.put(None)  # 发送停止信号


class AdvancedProgressDialog:
    """高级进度对话框"""
    
    def __init__(self, parent, title="处理中...", cancelable=True):
        self.parent = parent
        self.title = title
        self.cancelable = cancelable
        self.dialog = None
        self.progress_var = None
        self.status_var = None
        self.detail_var = None
        self.cancel_callback = None
        self.start_time = None
        self.estimated_time_var = None
        
    def show(self, total_steps=100, cancel_callback=None):
        """显示进度对话框"""
        self.cancel_callback = cancel_callback
        self.start_time = time.time()
        
        # 创建对话框
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("500x200")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        self.dialog.resizable(False, False)
        
        # 禁用关闭按钮（如果不可取消）
        if not self.cancelable:
            self.dialog.protocol("WM_DELETE_WINDOW", lambda: None)
        
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 状态标签
        self.status_var = tk.StringVar(value="准备中...")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, font=("Arial", 11, "bold"))
        status_label.pack(pady=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            main_frame, 
            variable=self.progress_var, 
            maximum=total_steps,
            length=400
        )
        progress_bar.pack(pady=(0, 10), fill=tk.X)
        
        # 详细信息标签
        self.detail_var = tk.StringVar(value="")
        detail_label = ttk.Label(main_frame, textvariable=self.detail_var, font=("Arial", 9))
        detail_label.pack(pady=(0, 10))
        
        # 预计时间标签
        self.estimated_time_var = tk.StringVar(value="")
        time_label = ttk.Label(main_frame, textvariable=self.estimated_time_var, font=("Arial", 9))
        time_label.pack(pady=(0, 10))
        
        # 按钮框架
        if self.cancelable:
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X)
            
            cancel_btn = ttk.Button(
                button_frame, 
                text="取消", 
                command=self._on_cancel,
                width=10
            )
            cancel_btn.pack(side=tk.RIGHT)
        
        # 居中显示
        self._center_dialog()
        
        # 更新界面
        self.dialog.update()
    
    def update_progress(self, current_step, status_message="", detail_message=""):
        """更新进度"""
        if not self.dialog:
            return
        
        try:
            # 更新进度条
            if self.progress_var:
                self.progress_var.set(current_step)
            
            # 更新状态消息
            if self.status_var and status_message:
                self.status_var.set(status_message)
            
            # 更新详细信息
            if self.detail_var:
                self.detail_var.set(detail_message)
            
            # 计算预计时间
            if self.start_time and current_step > 0:
                elapsed_time = time.time() - self.start_time
                total_steps = self.progress_var.get() if hasattr(self.progress_var, 'get') else 100
                
                if current_step < total_steps:
                    estimated_total = elapsed_time * total_steps / current_step
                    remaining_time = estimated_total - elapsed_time
                    
                    if remaining_time > 60:
                        time_text = f"预计剩余: {int(remaining_time // 60)}分{int(remaining_time % 60)}秒"
                    else:
                        time_text = f"预计剩余: {int(remaining_time)}秒"
                else:
                    time_text = "即将完成..."
                
                if self.estimated_time_var:
                    self.estimated_time_var.set(time_text)
            
            # 强制更新界面
            self.dialog.update()
            
        except Exception as e:
            print(f"⚠️ 更新进度失败: {e}")
    
    def _on_cancel(self):
        """取消按钮回调"""
        if self.cancel_callback:
            self.cancel_callback()
        self.hide()
    
    def _center_dialog(self):
        """居中显示对话框"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def hide(self):
        """隐藏进度对话框"""
        if self.dialog:
            self.dialog.destroy()
            self.dialog = None


class AsyncDataImporter:
    """异步数据导入器"""
    
    def __init__(self, async_manager: AsyncOperationManager):
        self.async_manager = async_manager
    
    def import_excel_async(self, file_path: str, callback: Callable, 
                          progress_dialog: AdvancedProgressDialog = None):
        """异步导入Excel数据"""
        
        def import_operation(progress_callback=None):
            """实际的导入操作"""
            import pandas as pd
            
            try:
                # 读取Excel文件
                if progress_callback:
                    progress_callback(10, "正在读取Excel文件...", f"文件: {file_path}")
                
                df = pd.read_excel(file_path)
                total_rows = len(df)
                
                if progress_callback:
                    progress_callback(20, f"读取完成，共{total_rows}行数据", "开始处理数据...")
                
                # 数据验证和处理
                processed_data = []
                errors = []
                
                for index, row in df.iterrows():
                    try:
                        # 数据验证逻辑
                        processed_row = self._validate_and_process_row(row)
                        processed_data.append(processed_row)
                        
                        # 更新进度
                        if progress_callback and index % 10 == 0:
                            progress = 20 + (index / total_rows) * 60
                            progress_callback(
                                progress, 
                                f"处理中... ({index + 1}/{total_rows})",
                                f"当前行: {row.get('Chair_Serial_No', 'N/A')}"
                            )
                    
                    except Exception as e:
                        errors.append(f"第{index + 1}行: {str(e)}")
                
                # 批量插入数据库
                if progress_callback:
                    progress_callback(80, "正在保存到数据库...", f"准备插入{len(processed_data)}条记录")
                
                success_count = self._batch_insert_data(processed_data, progress_callback)
                
                if progress_callback:
                    progress_callback(100, "导入完成", f"成功导入{success_count}条记录")
                
                return {
                    'success_count': success_count,
                    'total_count': total_rows,
                    'errors': errors
                }
                
            except Exception as e:
                raise Exception(f"导入失败: {str(e)}")
        
        # 创建进度回调
        def progress_callback(progress, status, detail):
            if progress_dialog:
                progress_dialog.update_progress(progress, status, detail)
        
        # 执行异步导入
        return self.async_manager.execute_async(
            lambda: import_operation(progress_callback),
            callback,
            operation_name="Excel数据导入"
        )
    
    def _validate_and_process_row(self, row) -> Dict:
        """验证和处理单行数据"""
        # 数据验证逻辑
        processed = {}
        
        # 必填字段检查
        required_fields = ['Chair_Serial_No', 'STATE', 'Location', 'Quantity']
        for field in required_fields:
            if pd.isna(row.get(field)) or str(row.get(field)).strip() == '':
                raise ValueError(f"必填字段 {field} 为空")
            processed[field] = str(row[field]).strip()
        
        # 可选字段处理
        optional_fields = ['Sim_Card_Model', 'Sim_Card_No', 'Layer', 'Company', 
                          'Effective_From', 'Effective_To', 'Rental', 'SIMCARDID', 'DATE']
        for field in optional_fields:
            value = row.get(field)
            if pd.isna(value):
                processed[field] = None
            else:
                processed[field] = str(value).strip() if str(value).strip() else None
        
        return processed
    
    def _batch_insert_data(self, data_list: List[Dict], progress_callback=None) -> int:
        """批量插入数据"""
        # 这里应该使用实际的数据库插入逻辑
        success_count = 0
        total_count = len(data_list)
        
        for i, data in enumerate(data_list):
            try:
                # 模拟数据库插入
                time.sleep(0.01)  # 模拟插入耗时
                success_count += 1
                
                # 更新进度
                if progress_callback and i % 50 == 0:
                    progress = 80 + (i / total_count) * 20
                    progress_callback(
                        progress,
                        "正在保存到数据库...",
                        f"已保存 {i + 1}/{total_count} 条记录"
                    )
                    
            except Exception as e:
                print(f"插入数据失败: {e}")
        
        return success_count


class AsyncQueryExecutor:
    """异步查询执行器"""
    
    def __init__(self, async_manager: AsyncOperationManager):
        self.async_manager = async_manager
    
    def execute_complex_query_async(self, query_func: Callable, callback: Callable,
                                  query_name: str = "复杂查询"):
        """异步执行复杂查询"""
        
        def query_operation():
            """查询操作"""
            start_time = time.time()
            result = query_func()
            end_time = time.time()
            
            return {
                'data': result,
                'execution_time': end_time - start_time,
                'query_name': query_name
            }
        
        return self.async_manager.execute_async(
            query_operation,
            callback,
            operation_name=query_name
        )
    
    def search_with_progress(self, search_params: Dict, callback: Callable):
        """带进度的搜索操作"""
        
        def search_operation(progress_callback):
            """搜索操作"""
            # 模拟复杂搜索过程
            steps = [
                (20, "正在构建查询条件...", "分析搜索参数"),
                (40, "正在执行数据库查询...", "查询Equipment_ID表"),
                (60, "正在处理查询结果...", "数据格式化"),
                (80, "正在应用过滤条件...", "结果筛选"),
                (100, "搜索完成", "准备显示结果")
            ]
            
            results = []
            for progress, status, detail in steps:
                time.sleep(0.5)  # 模拟处理时间
                progress_callback(progress, status, detail)
                
                if progress == 40:
                    # 模拟实际查询
                    results = self._execute_search(search_params)
            
            return results
        
        return self.async_manager.execute_async(
            lambda: search_operation(lambda p, s, d: None),
            callback,
            operation_name="数据搜索"
        )
    
    def _execute_search(self, search_params: Dict) -> List:
        """执行实际搜索"""
        # 这里应该是实际的搜索逻辑
        # 模拟返回结果
        return [
            (1, "Active", "Location1", 10, "SN001", "Model1", "Card1"),
            (2, "Active", "Location2", 5, "SN002", "Model2", "Card2"),
        ]


def create_async_integration_example():
    """创建异步集成示例"""
    
    example_code = '''
# === 在ID管理工具.py中集成异步操作 ===

# 1. 在__init__方法中初始化异步组件
def __init__(self):
    # ... 现有代码 ...
    
    # 初始化异步操作管理器
    self.async_manager = AsyncOperationManager(self.root)
    self.async_importer = AsyncDataImporter(self.async_manager)
    self.async_query_executor = AsyncQueryExecutor(self.async_manager)

# 2. 异步导入Excel数据
def import_excel_data_async(self):
    """异步导入Excel数据"""
    file_path = filedialog.askopenfilename(
        title="选择Excel文件",
        filetypes=[("Excel文件", "*.xlsx *.xls")]
    )
    
    if not file_path:
        return
    
    # 创建进度对话框
    progress_dialog = AdvancedProgressDialog(
        self.root, 
        "导入Excel数据", 
        cancelable=True
    )
    
    def on_import_complete(success, result):
        """导入完成回调"""
        progress_dialog.hide()
        
        if success:
            success_count = result['success_count']
            total_count = result['total_count']
            errors = result['errors']
            
            message = f"导入完成！\\n成功: {success_count}/{total_count} 条记录"
            if errors:
                message += f"\\n错误: {len(errors)} 条"
            
            safe_show_info(message, "导入完成")
            self.refresh_data()  # 刷新界面数据
        else:
            safe_show_error(f"导入失败: {result}")
    
    def on_cancel():
        """取消导入"""
        # 这里可以添加取消逻辑
        pass
    
    # 显示进度对话框
    progress_dialog.show(100, on_cancel)
    
    # 开始异步导入
    self.async_importer.import_excel_async(
        file_path, 
        on_import_complete, 
        progress_dialog
    )

# 3. 异步复杂查询
def execute_complex_search_async(self):
    """异步执行复杂搜索"""
    search_params = {
        'serial': self.search_serial_var.get().strip(),
        'location': self.search_location_var.get().strip(),
        'state': self.search_state_var.get().strip()
    }
    
    def search_query():
        # 实际的搜索逻辑
        return self.perform_complex_search(search_params)
    
    def on_search_complete(success, result):
        """搜索完成回调"""
        if success:
            data = result['data']
            execution_time = result['execution_time']
            
            self.populate_tree(data)
            self.update_status_bar(len(data), None)
            
            if execution_time > 1.0:
                safe_show_info(f"搜索完成，耗时 {execution_time:.2f} 秒")
        else:
            safe_show_error(f"搜索失败: {result}")
    
    # 执行异步搜索
    self.async_query_executor.execute_complex_query_async(
        search_query,
        on_search_complete,
        "设备搜索"
    )

# 4. 在程序关闭时清理异步资源
def on_closing(self):
    """程序关闭时的清理工作"""
    # 关闭异步操作管理器
    if hasattr(self, 'async_manager'):
        self.async_manager.shutdown()
    
    # 其他清理工作...
    self.root.destroy()
'''
    
    return example_code


def main():
    """主函数 - 演示异步操作"""
    print("🚀 异步操作和用户体验增强方案")
    print("=" * 50)
    
    print("✅ 异步组件:")
    print("1. AsyncOperationManager - 异步操作管理器")
    print("2. AdvancedProgressDialog - 高级进度对话框")
    print("3. AsyncDataImporter - 异步数据导入器")
    print("4. AsyncQueryExecutor - 异步查询执行器")
    
    print("\n🎯 解决的问题:")
    print("- 大数据量导入时界面卡顿")
    print("- 复杂查询时用户体验差")
    print("- 缺少操作进度反馈")
    print("- 无法取消长时间操作")
    
    print("\n📈 预期收益:")
    print("- 界面响应性大幅提升")
    print("- 用户体验显著改善")
    print("- 支持操作进度显示")
    print("- 支持操作取消功能")
    
    print("\n📖 集成示例:")
    print(create_async_integration_example())


if __name__ == "__main__":
    main()
