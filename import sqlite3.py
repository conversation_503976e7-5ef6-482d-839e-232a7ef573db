import sqlite3
import os

# 数据库文件路径
DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"

# 确保数据库目录存在
os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)

# 连接到SQLite数据库（如果不存在则创建）
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()

# 创建IOT销售表
cursor.execute('''
CREATE TABLE IF NOT EXISTS IOT_Sales (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    Copartner_name TEXT,
    Order_No TEXT,
    Order_types TEXT,
    Order_status TEXT,
    Order_price REAL,
    Payment REAL,
    Order_time TEXT,
    Equipment_ID TEXT,
    Equipment_name TEXT,
    Branch_name TEXT,
    Payment_date TEXT,
    User_name TEXT,
    Time TEXT,
    Matched_Order_ID TEXT,
    OrderTime_dt TEXT,
    Import_Date TEXT
)
''')

# 创建ZERO销售表
cursor.execute('''
CREATE TABLE IF NOT EXISTS ZERO_Sales (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    Copartner_name TEXT,
    Order_No TEXT,
    Order_types TEXT,
    Order_status TEXT,
    Order_price REAL,
    Payment REAL,
    Order_time TEXT,
    Equipment_ID TEXT,
    Equipment_name TEXT,
    Branch_name TEXT,
    Payment_date TEXT,
    User_name TEXT,
    Time TEXT,
    Matched_Order_ID TEXT,
    OrderTime_dt TEXT,
    Import_Date TEXT
)
''')

# 创建APP销售表
cursor.execute('''
CREATE TABLE IF NOT EXISTS APP_Sales (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    Copartner_name TEXT,
    Order_No TEXT,
    Order_types TEXT,
    Order_status TEXT,
    Order_price REAL,
    Payment REAL,
    Order_time TEXT,
    Equipment_ID TEXT,
    Equipment_name TEXT,
    Branch_name TEXT,
    Payment_date TEXT,
    User_name TEXT,
    Time TEXT,
    Matched_Order_ID TEXT,
    OrderTime_dt TEXT,
    Import_Date TEXT
)
''')

# 创建合并销售表
cursor.execute('''
CREATE TABLE IF NOT EXISTS Combined_Sales (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    Platform TEXT,
    Copartner_name TEXT,
    Order_No TEXT,
    Order_types TEXT,
    Order_status TEXT,
    Order_price REAL,
    Payment REAL,
    Order_time TEXT,
    Equipment_ID TEXT,
    Equipment_name TEXT,
    Branch_name TEXT,
    Payment_date TEXT,
    User_name TEXT,
    Time TEXT,
    Matched_Order_ID TEXT,
    OrderTime_dt TEXT,
    Import_Date TEXT
)
''')

# 创建产品信息表 (SCD Type 2)
cursor.execute('''
CREATE TABLE IF NOT EXISTS Products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    Equipment_ID TEXT,
    STATE TEXT,
    Location TEXT,
    Quantity INTEGER,
    Chair_Serial_No TEXT,
    Sim_Card_Model TEXT,
    Sim_Card_No TEXT,
    Layer TEXT,
    Company TEXT,
    Install_Date TEXT,
    Rental REAL,
    SIMCARDID TEXT,
    EffectiveFrom TEXT,
    EffectiveTo TEXT,
    CurrentFlag TEXT
)
''')

# 创建导入日志表
cursor.execute('''
CREATE TABLE IF NOT EXISTS Import_Logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    Platform TEXT,
    Filename TEXT,
    Import_Date TEXT,
    Status TEXT,
    Message TEXT
)
''')

# 提交更改并关闭连接
conn.commit()
conn.close()

print("数据库和表结构已成功创建！")