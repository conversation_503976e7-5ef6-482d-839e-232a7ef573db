# -*- coding: utf-8 -*-
import os
import sys
import sqlite3
import pandas as pd
import datetime
import logging
import shutil

# ---- 配置 ----
DB_PATH        = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
BACKUP_DIR     = r"C:\Users\<USER>\Desktop\Day Report\database\backups"
ID_FILE_PATH   = r"C:\Users\<USER>\Desktop\Day Report\ID INFO\LOCATION ID.xlsx"

# 日期常量
EXPIRY_DATE    = "2025-03-31"
EFFECTIVE_DATE = "2025-04-01"

# 必需列（Excel 中的列名）
REQUIRED_COLS  = ['STATE', 'Location', 'Quantity', 'Chair Serial No', 'DATE', 'Rental']

# 日志配置
LOG_FILE = os.path.join(os.path.dirname(DB_PATH), "import_log.txt")
logging.basicConfig(
    level    = logging.INFO,
    format   = "[%(asctime)s] %(levelname)s %(message)s",
    datefmt  = "%Y-%m-%d %H:%M:%S",
    handlers = [
        logging.FileHandler(LOG_FILE, mode='a', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

# ---- 数据库助手 ----
class DatabaseManager:
    @staticmethod
    def get_connection():
        conn = sqlite3.connect(DB_PATH, timeout=30, isolation_level=None)
        conn.execute("PRAGMA foreign_keys = ON")
        return conn


def create_backup():
    os.makedirs(BACKUP_DIR, exist_ok=True)
    ts = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    dst = os.path.join(BACKUP_DIR, f"sales_reports_backup_{ts}.db")
    shutil.copy2(DB_PATH, dst)
    logging.info(f"BACKUP CREATED: {dst}")
    return dst


def restore_backup(backup_path=None):
    if backup_path is None:
        files = sorted(
            [f for f in os.listdir(BACKUP_DIR) if f.startswith("sales_reports_backup_")],
            reverse=True
        )
        if not files:
            logging.error("No backup found to restore.")
            return False
        backup_path = os.path.join(BACKUP_DIR, files[0])
    try:
        if os.path.exists(DB_PATH):
            os.remove(DB_PATH)
        shutil.copy2(backup_path, DB_PATH)
        logging.info(f"RESTORED from {backup_path}")
        return True
    except Exception:
        logging.exception("Restore failed")
        return False


def safe_float(val):
    """尝试将值转换为浮点数，若失败则返回原字符串"""
    try:
        return float(val)
    except Exception:
        return val


def import_and_update():
    # 1. 读取并验证 Excel
    df = pd.read_excel(ID_FILE_PATH, dtype=str)
    missing = [c for c in REQUIRED_COLS if c not in df.columns]
    if missing:
        raise RuntimeError(f"缺少必要列: {missing}")

    # 2. 去重并重命名
    df = df.drop_duplicates(subset=['Chair Serial No'])
    df = df[REQUIRED_COLS].copy()
    df.rename(columns={
        'Chair Serial No': 'Chair_Serial_No'
    }, inplace=True)

    # 3. 转数值并校验：将无法转换的行记录并跳过
    df['Quantity'] = pd.to_numeric(df['Quantity'], errors='coerce')
    df['Rental'] = pd.to_numeric(df['Rental'], errors='coerce')
    invalid_mask = df[['Quantity', 'Rental']].isna().any(axis=1)
    if invalid_mask.any():
        bad_rows = df[invalid_mask].index.tolist()
        logging.warning(f"SKIP INVALID ROWS: Quantity或Rental无法转换, 行: {bad_rows}")
        df = df[~invalid_mask].reset_index(drop=True)

    serials_excel = set(df['Chair_Serial_No'])

    conn = DatabaseManager.get_connection()
    try:
        # 建表
        conn.execute("""
        CREATE TABLE IF NOT EXISTS Equipment_ID (
            ID               INTEGER PRIMARY KEY AUTOINCREMENT,
            STATE            TEXT NOT NULL,
            Location         TEXT NOT NULL,
            Quantity         INTEGER NOT NULL,
            Rental           TEXT,
            Chair_Serial_No  TEXT NOT NULL,
            DATE             TEXT,
            Effective_From   TEXT,
            Effective_To     TEXT,
            Last_Updated     TEXT,
            UNIQUE(Chair_Serial_No, Effective_From)
        )""")

        cur = conn.cursor()

        # 总记录数
        cur.execute("SELECT COUNT(*) FROM Equipment_ID")
        total_count = cur.fetchone()[0]

        # 4. 失效处理
        cur.execute("BEGIN")
        cur.execute("SELECT Chair_Serial_No, STATE, Location, Effective_To FROM Equipment_ID")
        expired_count = 0
        for serial, state, loc, eff_to in cur.fetchall():
            if serial not in serials_excel and (eff_to is None or eff_to > EXPIRY_DATE):
                cur.execute(
                    "UPDATE Equipment_ID SET Effective_To=?, Last_Updated=? WHERE Chair_Serial_No=? AND (Effective_To IS NULL OR Effective_To>?)",
                    (EXPIRY_DATE, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'), serial, EXPIRY_DATE)
                )
                expired_count += 1
                logging.info(f"EXPIRE: STATE={state} Location={loc} Chair_Serial_No={serial} To={EXPIRY_DATE}")
        conn.commit()

        # 全部失效检测
        if total_count > 0 and expired_count == total_count:
            msg = f"ALL EXPIRED: 共{total_count}条, 全部失效(Effective_To={EXPIRY_DATE}), 检查Excel数据!"
            logging.error(msg)
            raise RuntimeError(msg)

        # 5. 新增 & 版本变动检测
        cur.execute("BEGIN")
        cur.execute("SELECT * FROM Equipment_ID WHERE Effective_To IS NULL")
        db_rows = cur.fetchall()
        cols = [d[0] for d in cur.description]
        db_map = {row[cols.index('Chair_Serial_No')]: dict(zip(cols, row)) for row in db_rows}

        monitored = ['STATE', 'Location', 'Quantity', 'Rental', 'DATE']
        for _, row in df.iterrows():
            serial = row['Chair_Serial_No']
            now_ts = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            if serial not in db_map:
                # 插入新记录
                cur.execute(
                    """
                    INSERT INTO Equipment_ID
                    (STATE, Location, Quantity, Rental, Chair_Serial_No, DATE, Effective_From, Effective_To, Last_Updated)
                    VALUES (?,?,?,?,?,?,?,?,?)
                    """,
                    (
                        row['STATE'], row['Location'], int(row['Quantity']),
                        row['Rental'], serial, row['DATE'], EFFECTIVE_DATE, None, now_ts
                    )
                )
                logging.info(f"INSERT: Chair_Serial_No={serial}")
            else:
                db = db_map[serial]
                changes = {}
                for fld in monitored:
                    old = db[fld]
                    new = row[fld]
                    if fld in ['Quantity', 'Rental']:
                        old = safe_float(old)
                        new = safe_float(new)
                    if str(old) != str(new):
                        changes[fld] = (old, new)
                # 大版本变动
                if len(changes) == len(monitored):
                    cur.execute(
                        "UPDATE Equipment_ID SET Effective_To=?, Last_Updated=? WHERE Chair_Serial_No=? AND Effective_To IS NULL",
                        (EXPIRY_DATE, now_ts, serial)
                    )
                    logging.info(f"VERSION EXPIRE: Chair_Serial_No={serial} To={EXPIRY_DATE}")
                    cur.execute(
                        """
                        INSERT INTO Equipment_ID
                        (STATE, Location, Quantity, Rental, Chair_Serial_No, DATE, Effective_From, Effective_To, Last_Updated)
                        VALUES (?,?,?,?,?,?,?,?,?)
                        """,
                        (
                            row['STATE'], row['Location'], int(row['Quantity']),
                            row['Rental'], serial, row['DATE'], EFFECTIVE_DATE, None, now_ts
                        )
                    )
                    logging.info(f"VERSION INSERT: Chair_Serial_No={serial}")
                elif changes:
                    # 小范围更新
                    set_clause = ", ".join([f"{k}=?" for k in changes]) + ", Last_Updated=?"
                    vals = [v[1] for v in changes.values()] + [now_ts, serial]
                    cur.execute(
                        f"UPDATE Equipment_ID SET {set_clause} WHERE Chair_Serial_No=? AND Effective_To IS NULL",
                        tuple(vals)
                    )
                    logging.info(f"UPDATE Chair_Serial_No={serial}: " + ", ".join([f"{k} {v[0]}->{v[1]}" for k,v in changes.items()]))
        conn.commit()

        # 6. 冲突检测
        cur.execute(
            """
            SELECT Chair_Serial_No, STATE, Location, Effective_From, Effective_To
            FROM Equipment_ID
            WHERE Effective_From <= ?
            AND (Effective_To IS NULL OR Effective_To >= ?)
            ORDER BY Chair_Serial_No
            """,
            (EFFECTIVE_DATE, EFFECTIVE_DATE)
        )
        dup_map = {}
        for serial, state, loc, ef, et in cur.fetchall():
            dup_map.setdefault(serial, []).append((state, loc, ef, et))
        for serial, recs in dup_map.items():
            if len(recs) > 1:
                logging.warning(f"DUPLICATE ACTIVE: Chair_Serial_No={serial}")
                for s, l, ef, et in recs:
                    logging.warning(f"  • {s},{l},{ef}->{et}")

    except Exception:
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    backup = None
    try:
        backup = create_backup()
        import_and_update()
        logging.info("IMPORT COMPLETE")
    except Exception:
        logging.exception("异常，尝试恢复...")
        if backup:
            restore_backup(backup)
        sys.exit(1)
