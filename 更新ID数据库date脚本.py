import os
import sqlite3
import datetime
import time
import traceback
import shutil

# 配置常量
DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
BACKUP_DIR = r"C:\Users\<USER>\Desktop\Day Report\database\backups"
MAX_RETRIES = 3
RETRY_DELAY = 1

class DatabaseManager:
    @staticmethod
    def get_connection():
        try:
            conn = sqlite3.connect(DB_PATH, timeout=30, isolation_level='IMMEDIATE')
            conn.execute("PRAGMA foreign_keys = ON")
            return conn
        except sqlite3.Error as e:
            print(f"数据库连接失败: {str(e)}")
            raise

def create_backup():
    """创建数据库备份"""
    try:
        if not os.path.exists(BACKUP_DIR):
            os.makedirs(BACKUP_DIR)
            
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(BACKUP_DIR, f"sales_reports_backup_{timestamp}.db")
        shutil.copy2(DB_PATH, backup_path)
        print(f"数据库已备份至: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"备份失败: {str(e)}")
        return None

def log_operation(operation: str, status: str, message: str) -> bool:
    """记录操作日志"""
    for attempt in range(MAX_RETRIES):
        try:
            with DatabaseManager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS Import_Logs (
                    ID INTEGER PRIMARY KEY AUTOINCREMENT,
                    Platform TEXT,
                    Filename TEXT,
                    Status TEXT,
                    Message TEXT,
                    Log_Time TEXT
                )''')
                cursor.execute(
                    "INSERT INTO Import_Logs (Platform, Filename, Status, Message, Log_Time) "
                    "VALUES (?, ?, ?, ?, ?)",
                    (operation, "数据库更新", status, message, 
                     datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                )
                conn.commit()
                return True
        except Exception as e:
            if attempt == MAX_RETRIES - 1:
                print(f"记录日志失败: {str(e)}")
                return False
            time.sleep(RETRY_DELAY)

def reset_id_sequence():
    """重置Equipment_ID表的ID序列从1开始"""
    try:
        # 先创建备份
        backup_path = create_backup()
        if not backup_path:
            print("备份失败，操作已取消")
            return False
        
        print("开始重置ID序列...")
        
        with DatabaseManager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取当前数据
            cursor.execute("SELECT * FROM Equipment_ID ORDER BY ID")
            rows = cursor.fetchall()
            
            # 获取列名
            cursor.execute("PRAGMA table_info(Equipment_ID)")
            columns = [col[1] for col in cursor.fetchall()]
            
            # 创建临时表
            temp_table = "Equipment_ID_temp"
            columns_str = ", ".join(columns)
            cursor.execute(f"CREATE TABLE {temp_table} AS SELECT * FROM Equipment_ID WHERE 1=0")
            
            # 删除原表
            cursor.execute("DROP TABLE Equipment_ID")
            
            # 创建新表
            cursor.execute(f'''
            CREATE TABLE Equipment_ID (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                STATE TEXT,
                Location TEXT,
                Quantity INTEGER,
                Chair_Serial_No TEXT,
                Sim_Card_Model TEXT,
                Sim_Card_No TEXT,
                Layer TEXT,
                Company TEXT,
                Effective_From DATE,
                Effective_To DATE,
                Rental REAL,
                SIMCARDID TEXT,
                Import_Date DATETIME,
                Last_Updated DATETIME,
                CurrentFlag TEXT,
                DATE TEXT
            )''')
            
            # 重新插入数据
            for row in rows:
                placeholders = ",".join(["?" for _ in row[1:]])
                cursor.execute(
                    f"INSERT INTO Equipment_ID ({columns_str}) VALUES (NULL, {placeholders})",
                    row[1:]
                )
            
            # 删除临时表
            cursor.execute(f"DROP TABLE {temp_table}")
            
            conn.commit()
            print(f"成功重置ID序列，共处理{len(rows)}条记录")
            log_operation("RESET_ID", "成功", f"已重置{len(rows)}条记录的ID序列")
            return True
            
    except Exception as e:
        error_msg = f"重置失败: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        log_operation("RESET_ID", "错误", error_msg)
        return False

def delete_all_data():
    """删除数据库中的所有数据"""
    try:
        # 先创建备份
        backup_path = create_backup()
        if not backup_path:
            print("备份失败，操作已取消")
            return False
        
        print("开始删除数据库中的所有数据...")
        
        with DatabaseManager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 删除Equipment_ID表中的所有数据
            cursor.execute("DELETE FROM Equipment_ID")
            deleted_rows = cursor.rowcount
            
            # 重置自增ID
            cursor.execute("DELETE FROM sqlite_sequence WHERE name='Equipment_ID'")
            
            conn.commit()
            
            print(f"成功删除{deleted_rows}条记录")
            log_operation("DELETE_DATA", "成功", f"已删除Equipment_ID表中的{deleted_rows}条记录")
            return True
            
    except Exception as e:
        error_msg = f"删除失败: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        log_operation("DELETE_DATA", "错误", error_msg)
        return False

def update_effective_from():
    """将Effective_From更新为DATE字段的值"""
    try:
        # 先创建备份
        backup_path = create_backup()
        if not backup_path:
            print("备份失败，操作已取消")
            return False
        
        print("开始更新数据库中的Effective_From字段...")
        
        with DatabaseManager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='Equipment_ID'")
            if not cursor.fetchone():
                raise Exception("Equipment_ID表不存在")
            
            # 检查DATE列是否存在
            cursor.execute("PRAGMA table_info(Equipment_ID)")
            columns = [col[1] for col in cursor.fetchall()]
            print("当前表中的列:", columns)
            
            if 'DATE' not in columns:
                raise Exception("DATE列不存在，无法更新")
            
            # 获取需要更新的记录数量
            cursor.execute("SELECT COUNT(*) FROM Equipment_ID")
            total_records = cursor.fetchone()[0]
            print(f"数据库中共有{total_records}条记录")
            
            # 更新Effective_From为DATE的值
            cursor.execute("UPDATE Equipment_ID SET Effective_From = DATE")
            updated_rows = cursor.rowcount
            conn.commit()
            
            print(f"成功更新{updated_rows}条记录")
            log_operation("UPDATE_FIELDS", "成功", f"已将{updated_rows}条记录的Effective_From更新为DATE的值")
            return True
            
    except Exception as e:
        error_msg = f"更新失败: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        log_operation("UPDATE_FIELDS", "错误", error_msg)
        return False

if __name__ == "__main__":
    print("1. 更新Effective_From字段")
    print("2. 删除所有数据")
    print("3. 重置ID序列")
    choice = input("请选择操作(1/2/3): ")
    
    if choice == "1":
        print("此脚本将把数据库中所有记录的Effective_From字段更新为DATE字段的值")
        print("警告: 此操作不可逆，请确保已备份数据库")
        sub_choice = input("是否继续? (y/n): ")
        
        if sub_choice.lower() == 'y':
            start_time = datetime.datetime.now()
            print(f"开始更新: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            success = update_effective_from()
            status = "成功" if success else "失败"
            
            duration = (datetime.datetime.now() - start_time).total_seconds()
            print(f"更新完成. 状态: {status} | 耗时: {duration:.2f}秒")
            print("="*50)
            print("详细执行日志请查看数据库中的 Import_Logs 表")
    
    elif choice == "2":
        print("警告: 此操作将删除Equipment_ID表中的所有数据！")
        print("此操作不可逆，请确保已备份数据库")
        sub_choice = input("是否继续? (y/n): ")
        
        if sub_choice.lower() == 'y':
            start_time = datetime.datetime.now()
            print(f"开始删除: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            success = delete_all_data()
            status = "成功" if success else "失败"
            
            duration = (datetime.datetime.now() - start_time).total_seconds()
            print(f"删除完成. 状态: {status} | 耗时: {duration:.2f}秒")
            print("="*50)
            print("详细执行日志请查看数据库中的 Import_Logs 表")
    
    elif choice == "3":
        print("警告: 此操作将重置Equipment_ID表的ID序列！")
        print("此操作不可逆，请确保已备份数据库")
        sub_choice = input("是否继续? (y/n): ")
        
        if sub_choice.lower() == 'y':
            start_time = datetime.datetime.now()
            print(f"开始重置: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            success = reset_id_sequence()
            status = "成功" if success else "失败"
            
            duration = (datetime.datetime.now() - start_time).total_seconds()
            print(f"重置完成. 状态: {status} | 耗时: {duration:.2f}秒")
            print("="*50)
            print("详细执行日志请查看数据库中的 Import_Logs 表")
    else:
        print("无效的选择")