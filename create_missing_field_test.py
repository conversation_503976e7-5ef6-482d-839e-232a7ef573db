#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建缺少字段的测试Excel文件，用于测试智能补全功能
"""

import pandas as pd

# 创建缺少STATE字段的测试数据
data = {
    # 故意不包含 'STATE' 字段来测试智能补全
    'Location': ['PRANGIN MALL', 'GURNEY PLAZA', 'QUEENSBAY MALL'],
    'Quantity': [1, 2, 1],
    'Chair_Serial_No': ['TEST001', 'TEST002', 'TEST003'],
    'Sim_Card_Model': ['Celcom', 'Maxis', 'Digi'],
    'SIMCARDID': ['896019210381540476', '896019210381540477', '896019210381540478'],
    'Sim_Card_No': ['015-92270267', '015-92270268', '015-92270269'],
    'Layer': ['2', '3', '1'],
    'Company': ['Test Company A', 'Test Company B', 'Test Company C'],
    'DATE': ['30/05/2024', '31/05/2024', '01/06/2024'],
    'Rental': ['280.00', '300.00', '250.00']
}

df = pd.DataFrame(data)

# 保存路径
excel_path = "C:/Users/<USER>/Desktop/Day Report/test_missing_state.xlsx"

try:
    df.to_excel(excel_path, index=False)
    print(f"✅ 缺少STATE字段的测试Excel文件已创建: {excel_path}")
    print(f"📊 数据内容:")
    print(df.to_string())
    
    # 验证文件
    test_df = pd.read_excel(excel_path)
    print(f"\n✅ 验证成功，包含 {len(test_df)} 行数据")
    print(f"📋 列名: {list(test_df.columns)}")
    print(f"❌ 缺少的必填字段: STATE")
    
    # 显示每个字段的值和类型
    print(f"\n📝 详细字段信息:")
    for col in test_df.columns:
        val = test_df[col].iloc[0]
        print(f"  {col}: '{val}' (类型: {type(val).__name__})")
        
    print(f"\n🎯 测试说明:")
    print(f"1. 打开ID管理工具")
    print(f"2. 选择 文件 -> 导入Excel")
    print(f"3. 选择文件: {excel_path}")
    print(f"4. 程序会检测到缺少STATE字段")
    print(f"5. 自动打开智能补全界面")
    print(f"6. 所有其他字段会自动填入")
    print(f"7. 只需要填写STATE字段即可")
    
except Exception as e:
    print(f"❌ 创建文件失败: {e}")

# 创建第二个测试文件：缺少多个字段
print(f"\n" + "="*50)
print("创建缺少多个字段的测试文件...")

data2 = {
    # 只包含部分字段
    'Chair_Serial_No': ['MULTI001', 'MULTI002'],
    'Sim_Card_Model': ['Celcom', 'Maxis'],
    'SIMCARDID': ['896019210381540480', '896019210381540481'],
    'DATE': ['02/06/2024', '03/06/2024'],
    'Rental': ['320.00', '350.00']
    # 缺少: STATE, Location, Company 等
}

df2 = pd.DataFrame(data2)
excel_path2 = "C:/Users/<USER>/Desktop/Day Report/test_missing_multiple.xlsx"

try:
    df2.to_excel(excel_path2, index=False)
    print(f"✅ 缺少多个字段的测试Excel文件已创建: {excel_path2}")
    print(f"📊 数据内容:")
    print(df2.to_string())
    print(f"❌ 缺少的字段: STATE, Location, Company 等")
    
except Exception as e:
    print(f"❌ 创建第二个文件失败: {e}")
