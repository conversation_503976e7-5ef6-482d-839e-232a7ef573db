# -*- coding: utf-8 -*-
"""
配置管理模块
集中管理应用程序配置，支持配置文件读写和动态更新
"""

import os
import json
import configparser
from typing import Dict, Any, Optional, Union
from constants import Config


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = None):
        self.config_file = config_file or Config.FILE.CONFIG_FILE
        self.config_data = {}
        self.ini_config = configparser.ConfigParser()
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            # 尝试加载INI格式配置文件
            if os.path.exists(self.config_file):
                self.ini_config.read(self.config_file, encoding='utf-8')
                self._convert_ini_to_dict()
            else:
                self._create_default_config()
                
        except Exception as e:
            print(f"⚠️ 加载配置文件失败: {e}")
            self._create_default_config()
    
    def _convert_ini_to_dict(self):
        """将INI配置转换为字典"""
        for section in self.ini_config.sections():
            self.config_data[section] = {}
            for key, value in self.ini_config.items(section):
                # 尝试转换数据类型
                self.config_data[section][key] = self._convert_value(value)
    
    def _convert_value(self, value: str) -> Union[str, int, float, bool]:
        """转换配置值的数据类型"""
        # 布尔值
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # 整数
        try:
            if '.' not in value:
                return int(value)
        except ValueError:
            pass
        
        # 浮点数
        try:
            return float(value)
        except ValueError:
            pass
        
        # 字符串
        return value
    
    def _create_default_config(self):
        """创建默认配置"""
        self.config_data = {
            'database': {
                'path': Config.DB.DEFAULT_DB_PATH,
                'timeout': Config.DB.CONNECTION_TIMEOUT,
                'pool_size': Config.DB.CONNECTION_POOL_SIZE
            },
            'ui': {
                'main_window_size': Config.UI.MAIN_WINDOW_SIZE,
                'page_size': Config.UI.DEFAULT_PAGE_SIZE,
                'theme': 'default',
                'language': 'zh_CN'
            },
            'operation': {
                'history_limit': Config.UI.OPERATION_HISTORY_LIMIT,
                'async_threshold': Config.OPERATION.ASYNC_THRESHOLD,
                'cache_timeout': Config.OPERATION.CACHE_TIMEOUT
            },
            'file': {
                'max_import_rows': Config.FILE.MAX_IMPORT_ROWS,
                'batch_size': Config.FILE.BATCH_SIZE,
                'auto_backup': True
            },
            'logging': {
                'level': 'INFO',
                'file': Config.LOG.LOG_FILE,
                'max_size': Config.LOG.MAX_LOG_SIZE,
                'backup_count': Config.LOG.BACKUP_COUNT
            }
        }
        self.save_config()
    
    def get(self, section: str, key: str = None, default: Any = None) -> Any:
        """获取配置值"""
        if key is None:
            return self.config_data.get(section, default)
        
        section_data = self.config_data.get(section, {})
        return section_data.get(key, default)
    
    def set(self, section: str, key: str, value: Any):
        """设置配置值"""
        if section not in self.config_data:
            self.config_data[section] = {}
        
        self.config_data[section][key] = value
    
    def update_section(self, section: str, data: Dict[str, Any]):
        """更新整个配置节"""
        if section not in self.config_data:
            self.config_data[section] = {}
        
        self.config_data[section].update(data)
    
    def save_config(self):
        """保存配置到文件"""
        try:
            # 清空现有配置
            self.ini_config.clear()
            
            # 转换字典到INI格式
            for section, data in self.config_data.items():
                self.ini_config.add_section(section)
                for key, value in data.items():
                    self.ini_config.set(section, key, str(value))
            
            # 写入文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.ini_config.write(f)
            
            print(f"✅ 配置已保存到: {self.config_file}")
            
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
    
    def reload_config(self):
        """重新加载配置"""
        self._load_config()
    
    def reset_to_default(self):
        """重置为默认配置"""
        self._create_default_config()
    
    def export_config(self, file_path: str, format_type: str = 'json'):
        """导出配置到文件"""
        try:
            if format_type.lower() == 'json':
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"不支持的格式: {format_type}")
            
            print(f"✅ 配置已导出到: {file_path}")
            
        except Exception as e:
            print(f"❌ 导出配置失败: {e}")
    
    def import_config(self, file_path: str, format_type: str = 'json'):
        """从文件导入配置"""
        try:
            if format_type.lower() == 'json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_data = json.load(f)
                
                # 合并配置
                for section, data in imported_data.items():
                    if isinstance(data, dict):
                        self.update_section(section, data)
                
                self.save_config()
                print(f"✅ 配置已从 {file_path} 导入")
            else:
                raise ValueError(f"不支持的格式: {format_type}")
                
        except Exception as e:
            print(f"❌ 导入配置失败: {e}")
    
    def validate_config(self) -> Dict[str, list]:
        """验证配置有效性"""
        errors = {}
        
        # 验证数据库配置
        db_config = self.get('database', {})
        db_errors = []
        
        if not db_config.get('path'):
            db_errors.append("数据库路径不能为空")
        
        timeout = db_config.get('timeout', 0)
        if not isinstance(timeout, (int, float)) or timeout <= 0:
            db_errors.append("数据库超时时间必须是正数")
        
        if db_errors:
            errors['database'] = db_errors
        
        # 验证UI配置
        ui_config = self.get('ui', {})
        ui_errors = []
        
        page_size = ui_config.get('page_size', 0)
        if not isinstance(page_size, int) or page_size < 1 or page_size > 1000:
            ui_errors.append("页面大小必须是1-1000之间的整数")
        
        if ui_errors:
            errors['ui'] = ui_errors
        
        return errors
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.get('database', {})
    
    def get_ui_config(self) -> Dict[str, Any]:
        """获取UI配置"""
        return self.get('ui', {})
    
    def get_operation_config(self) -> Dict[str, Any]:
        """获取操作配置"""
        return self.get('operation', {})
    
    def get_file_config(self) -> Dict[str, Any]:
        """获取文件配置"""
        return self.get('file', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get('logging', {})


class UserPreferences:
    """用户偏好设置"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.preferences_section = 'user_preferences'
    
    def get_window_position(self, window_name: str) -> Optional[str]:
        """获取窗口位置"""
        return self.config_manager.get(self.preferences_section, f'{window_name}_position')
    
    def set_window_position(self, window_name: str, position: str):
        """设置窗口位置"""
        self.config_manager.set(self.preferences_section, f'{window_name}_position', position)
    
    def get_column_widths(self) -> Dict[str, int]:
        """获取列宽设置"""
        widths = self.config_manager.get(self.preferences_section, 'column_widths', {})
        if isinstance(widths, str):
            try:
                widths = json.loads(widths)
            except:
                widths = {}
        return widths
    
    def set_column_widths(self, widths: Dict[str, int]):
        """设置列宽"""
        self.config_manager.set(self.preferences_section, 'column_widths', json.dumps(widths))
    
    def get_recent_files(self) -> list:
        """获取最近使用的文件"""
        files = self.config_manager.get(self.preferences_section, 'recent_files', [])
        if isinstance(files, str):
            try:
                files = json.loads(files)
            except:
                files = []
        return files
    
    def add_recent_file(self, file_path: str, max_count: int = 10):
        """添加最近使用的文件"""
        recent_files = self.get_recent_files()

        # 移除已存在的路径
        if file_path in recent_files:
            recent_files.remove(file_path)

        # 添加到开头
        recent_files.insert(0, file_path)

        # 限制数量
        recent_files = recent_files[:max_count]

        self.config_manager.set(self.preferences_section, 'recent_files', json.dumps(recent_files))

    def get_recent_databases(self) -> list:
        """获取最近使用的数据库"""
        databases = self.config_manager.get(self.preferences_section, 'recent_databases', [])
        if isinstance(databases, str):
            try:
                databases = json.loads(databases)
            except:
                databases = []
        return databases

    def add_recent_database(self, db_path: str, max_count: int = 10):
        """添加最近使用的数据库"""
        recent_databases = self.get_recent_databases()

        # 移除已存在的路径
        if db_path in recent_databases:
            recent_databases.remove(db_path)

        # 添加到开头
        recent_databases.insert(0, db_path)

        # 限制数量
        recent_databases = recent_databases[:max_count]

        self.config_manager.set(self.preferences_section, 'recent_databases', json.dumps(recent_databases))

    def set_current_database(self, db_path: str):
        """设置当前数据库路径"""
        self.config_manager.set('database', 'path', db_path)
        self.add_recent_database(db_path)

    def get_current_database(self) -> str:
        """获取当前数据库路径"""
        return self.config_manager.get('database', 'path', Config.DB.DEFAULT_DB_PATH)
    
    def get_search_history(self) -> list:
        """获取搜索历史"""
        history = self.config_manager.get(self.preferences_section, 'search_history', [])
        if isinstance(history, str):
            try:
                history = json.loads(history)
            except:
                history = []
        return history
    
    def add_search_history(self, search_term: str, max_count: int = 20):
        """添加搜索历史"""
        history = self.get_search_history()
        
        # 移除已存在的搜索词
        if search_term in history:
            history.remove(search_term)
        
        # 添加到开头
        history.insert(0, search_term)
        
        # 限制数量
        history = history[:max_count]
        
        self.config_manager.set(self.preferences_section, 'search_history', json.dumps(history))
    
    def save_preferences(self):
        """保存用户偏好"""
        self.config_manager.save_config()


# 全局配置管理器实例
config_manager = ConfigManager()
user_preferences = UserPreferences(config_manager)


# 便捷函数
def get_config(section: str, key: str = None, default: Any = None) -> Any:
    """获取配置值"""
    return config_manager.get(section, key, default)


def set_config(section: str, key: str, value: Any):
    """设置配置值"""
    config_manager.set(section, key, value)


def save_config():
    """保存配置"""
    config_manager.save_config()


def get_db_path() -> str:
    """获取数据库路径"""
    return get_config('database', 'path', Config.DB.DEFAULT_DB_PATH)


def get_page_size() -> int:
    """获取页面大小"""
    return get_config('ui', 'page_size', Config.UI.DEFAULT_PAGE_SIZE)


def get_window_size(window_type: str = 'main') -> str:
    """获取窗口尺寸"""
    size_key = f'{window_type}_window_size'
    return get_config('ui', size_key, Config.get_window_size(window_type))
