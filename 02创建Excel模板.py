import xlwings as xw
import os
import datetime
import win32com.client as win32
import logging
import time

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('excel_template.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def get_vba_code():
    return r'''
Option Explicit

Sub RefreshAllData()
    On Error Resume Next
    Application.ScreenUpdating = False
    Application.EnableEvents = False
    Application.Calculation = xlCalculationManual
    Dim qry As WorkbookQuery
    For Each qry In ThisWorkbook.Queries
        ThisWorkbook.Connections(qry.Name).Refresh
    Next qry
    Dim ws As Worksheet, pt As PivotTable
    For Each ws In ThisWorkbook.Worksheets
        For Each pt In ws.PivotTables
            pt.RefreshTable
        Next pt
    Next ws
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True
    Application.ScreenUpdating = True
End Sub

Sub CreatePowerQuery(queryName As String, m_code As String)
    On Error Resume Next
    Dim qry As WorkbookQuery
    For Each qry In ThisWorkbook.Queries
        If qry.Name = queryName Then
            qry.Delete
            Exit For
        End If
    Next qry
    ThisWorkbook.Queries.Add queryName, m_code
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Sheets("Data")
    Dim conn As WorkbookConnection
    For Each conn In ThisWorkbook.Connections
        If InStr(conn.Name, queryName) > 0 Then
            conn.Delete
            Exit For
        End If
    Next conn
    Dim cmdText As String
    cmdText = "OLEDB;Provider=Microsoft.Mashup.OleDb.1;Data Source=$Workbook$;Location=" & queryName & ";Extended Properties="""""
    ThisWorkbook.Connections.Add2 _
        Name:=queryName, _
        Description:="", _
        ConnectionString:=cmdText, _
        CommandText:="", _
        lCmdtype:=7, _
        CreateModelConnection:=True, _
        ImportRelationships:=False
End Sub

Sub CreateMainPivotTable()
    On Error Resume Next
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Sheets("Sales Analysis")
    Dim pvt As PivotTable
    For Each pvt In ws.PivotTables
        If pvt.Name = "SalesAnalysis" Then
            pvt.TableRange2.Clear
            Exit For
        End If
    Next pvt
    Dim pc As PivotCache
    Set pc = ThisWorkbook.PivotCaches.Create(SourceType:=xlExternal, SourceData:=ThisWorkbook.Model.ModelTables("Equipment").ModelTableColumns("STATE").ModelFormatString)
    Dim pt As PivotTable
    Set pt = pc.CreatePivotTable(TableDestination:=ws.Range("A3"), TableName:="SalesAnalysis")
    With pt
        .CubeFields("[Equipment].[STATE]").Orientation = xlRowField
        .CubeFields("[Equipment].[STATE]").Position = 1
        .CubeFields("[Equipment].[Location]").Orientation = xlRowField
        .CubeFields("[Equipment].[Location]").Position = 2
        .CubeFields("[Equipment].[Layer]").Orientation = xlRowField
        .CubeFields("[Equipment].[Layer]").Position = 3
        .CubeFields("[Equipment].[Quantity]").Orientation = xlDataField
        .CubeFields("[Equipment].[Quantity]").Function = xlSum
        .CubeFields("[Equipment].[Quantity]").Caption = "Total Quantity"
        .CubeFields("[Measures].[Total Sales]").Orientation = xlDataField
        .CubeFields("[Measures].[Total Sales]").Caption = "Total Sales"
        .CubeFields("[Measures].[Total Rental]").Orientation = xlDataField
        .CubeFields("[Measures].[Total Rental]").Caption = "Total Rental"
        .CubeFields("[Measures].[Profit]").Orientation = xlDataField
        .CubeFields("[Measures].[Profit]").Caption = "Profit"
        .CubeFields("[Measures].[Profit Per Unit]").Orientation = xlDataField
        .CubeFields("[Measures].[Profit Per Unit]").Caption = "Profit Per Unit"
        Dim df As PivotField
        For Each df In .DataFields
            Select Case df.Name
                Case "Total Quantity"
                    df.NumberFormat = "#,##0"
                Case Else
                    df.NumberFormat = "#,##0.00"
            End Select
        Next df
        Dim profitField As PivotField
        Set profitField = .DataFields("Profit")
        Dim profitRange As Range
        Set profitRange = profitField.DataRange
        profitRange.FormatConditions.Delete
        With profitRange.FormatConditions.Add(Type:=xlCellValue, Operator:=xlLess, Formula1:="0")
            .Interior.Color = RGB(255, 199, 206)
            .Font.Color = RGB(156, 0, 6)
        End With
        With profitRange.FormatConditions.Add(Type:=xlCellValue, Operator:=xlBetween, Formula1:="0.1", Formula2:="500")
            .Interior.Color = RGB(255, 235, 156)
            .Font.Color = RGB(156, 101, 0)
        End With
        With profitRange.FormatConditions.Add(Type:=xlCellValue, Operator:=xlBetween, Formula1:="501", Formula2:="1000")
            .Interior.Color = RGB(255, 192, 0)
            .Font.Color = RGB(99, 37, 35)
        End With
        With profitRange.FormatConditions.Add(Type:=xlCellValue, Operator:=xlGreater, Formula1:="1000")
            .Interior.Color = RGB(198, 239, 206)
            .Font.Color = RGB(0, 97, 0)
        End With
        .ShowTableStyleRowHeaders = True
        .ShowTableStyleColumnHeaders = True
        .TableStyle2 = "PivotStyleMedium9"
    End With
    ws.Columns.AutoFit
End Sub

Sub CreateDatePivotTable()
    On Error Resume Next
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Sheets("Sales Analysis")
    Dim pvt As PivotTable
    For Each pvt In ws.PivotTables
        If pvt.Name = "DateAnalysis" Then
            pvt.TableRange2.Clear
            Exit For
        End If
    Next pvt
    Dim pc As PivotCache
    Set pc = ThisWorkbook.PivotCaches.Create(SourceType:=xlExternal, SourceData:=ThisWorkbook.Model.ModelTables("Sales").ModelTableColumns("Sale_Date").ModelFormatString)
    Dim pt As PivotTable
    Set pt = pc.CreatePivotTable(TableDestination:=ws.Range("A20"), TableName:="DateAnalysis")
    With pt
        .CubeFields("[Equipment].[STATE]").Orientation = xlRowField
        .CubeFields("[Equipment].[STATE]").Position = 1
        .CubeFields("[Sales].[Sale_Date]").Orientation = xlRowField
        .CubeFields("[Sales].[Sale_Date]").Position = 2
        .CubeFields("[Measures].[Total Sales]").Orientation = xlDataField
        .CubeFields("[Measures].[Total Sales]").Caption = "Daily Sales"
        .CubeFields("[Measures].[Profit]").Orientation = xlDataField
        .CubeFields("[Measures].[Profit]").Caption = "Daily Profit"
        Dim df As PivotField
        For Each df In .DataFields
            df.NumberFormat = "#,##0.00"
        Next df
        Dim profitField As PivotField
        Set profitField = .DataFields("Daily Profit")
        Dim profitRange As Range
        Set profitRange = profitField.DataRange
        profitRange.FormatConditions.Delete
        With profitRange.FormatConditions.Add(Type:=xlCellValue, Operator:=xlLess, Formula1:="0")
            .Interior.Color = RGB(255, 199, 206)
            .Font.Color = RGB(156, 0, 6)
        End With
        With profitRange.FormatConditions.Add(Type:=xlCellValue, Operator:=xlBetween, Formula1:="0.1", Formula2:="500")
            .Interior.Color = RGB(255, 235, 156)
            .Font.Color = RGB(156, 101, 0)
        End With
        With profitRange.FormatConditions.Add(Type:=xlCellValue, Operator:=xlBetween, Formula1:="501", Formula2:="1000")
            .Interior.Color = RGB(255, 192, 0)
            .Font.Color = RGB(99, 37, 35)
        End With
        With profitRange.FormatConditions.Add(Type:=xlCellValue, Operator:=xlGreater, Formula1:="1000")
            .Interior.Color = RGB(198, 239, 206)
            .Font.Color = RGB(0, 97, 0)
        End With
        .ShowTableStyleRowHeaders = True
        .ShowTableStyleColumnHeaders = True
        .TableStyle2 = "PivotStyleMedium9"
        ws.Range("A19").Value = "Daily Sales Analysis"
        ws.Range("A19").Font.Bold = True
        ws.Range("A19").Font.Size = 14
    End With
    ws.Columns.AutoFit
End Sub
'''

def wait_for_model_table(model, table_name, timeout=60):
    for _ in range(timeout):
        try:
            _ = model.ModelTables(table_name)
            return True
        except Exception:
            time.sleep(1)
    return False

def refresh_all_connections(file_path, wait_seconds=10):
    xl = win32.Dispatch('Excel.Application')
    xl.Visible = False
    wb = xl.Workbooks.Open(file_path)
    try:
        wb.RefreshAll()
        for _ in range(wait_seconds):
            if xl.CalculationState == 0:
                break
            time.sleep(1)
        wb.Save()
    finally:
        wb.Close(SaveChanges=True)
        xl.Quit()

def try_load_model_table(method_idx, file_path):
    if method_idx == 0:
        refresh_all_connections(file_path, wait_seconds=15)
    elif method_idx == 1:
        xl = win32.Dispatch('Excel.Application')
        xl.Visible = False
        wb = xl.Workbooks.Open(file_path)
        wb.Save()
        wb.Close(SaveChanges=True)
        xl.Quit()
        refresh_all_connections(file_path, wait_seconds=15)
    elif method_idx == 2:
        app = xw.App(visible=False)
        wb = app.books.open(file_path)
        wb.save()
        wb.close()
        app.quit()
        refresh_all_connections(file_path, wait_seconds=15)
    elif method_idx == 3:
        refresh_all_connections(file_path, wait_seconds=15)
        refresh_all_connections(file_path, wait_seconds=15)
    elif method_idx == 4:
        xl = win32.Dispatch('Excel.Application')
        xl.Visible = False
        wb = xl.Workbooks.Open(file_path)
        wb.RefreshAll()
        wb.Save()
        wb.Close(SaveChanges=True)
        xl.Quit()
    else:
        raise Exception("不支持的方法编号")

def create_excel_template():
    app = None
    wb = None
    try:
        logging.info("开始创建Excel模板")
        app = xw.App(visible=False)
        wb = app.books.add()
        if 'Control' not in [sheet.name for sheet in wb.sheets]:
            wb.sheets.add('Control')
        if 'Data' not in [sheet.name for sheet in wb.sheets]:
            wb.sheets.add('Data')
        if 'Sales Analysis' not in [sheet.name for sheet in wb.sheets]:
            wb.sheets.add('Sales Analysis')
        control_sheet = wb.sheets['Control']
        control_sheet.range('A1').value = 'Sales Report Control Panel'
        control_sheet.range('A1:F1').merge()
        control_sheet.range('A1').font.bold = True
        control_sheet.range('A1').font.size = 14
        control_sheet.range('A5').value = 'Date Range'
        control_sheet.range('A5').font.bold = True
        control_sheet.range('A6').value = 'Start Date:'
        control_sheet.range('B6').value = datetime.date(2025, 3, 1)
        control_sheet.range('B6').number_format = 'yyyy-mm-dd'
        control_sheet.range('A7').value = 'End Date:'
        control_sheet.range('B7').value = datetime.date(2025, 3, 31)
        control_sheet.range('B7').number_format = 'yyyy-mm-dd'
        refresh_button = control_sheet.api.Buttons().Add(
            control_sheet.range('A9').left,
            control_sheet.range('A9').top,
            120,
            30
        )
        refresh_button.OnAction = "RefreshAllData"
        refresh_button.Caption = "Refresh All Data"
        refresh_button.Name = "RefreshButton"
        output_file = os.path.join(os.path.dirname(__file__), "Sales_Report_PowerBI.xlsm")
        wb.save(output_file)
        logging.info("添加VBA代码")
        vba_module = wb.api.VBProject.VBComponents.Add(1)
        vba_module.CodeModule.AddFromString(get_vba_code())
        wb.save(output_file)
        wb.close()
        app.quit()
        logging.info("创建Power Query连接")
        create_power_queries(output_file)
        found = False
        for method_idx in range(5):
            logging.info(f"尝试第{method_idx+1}种加载模型表的方法...")
            try_load_model_table(method_idx, output_file)
            xl = win32.Dispatch('Excel.Application')
            wb_xl = xl.Workbooks.Open(output_file)
            try:
                model = wb_xl.Model
                eq_ok = wait_for_model_table(model, "Equipment", timeout=10)
                sales_ok = wait_for_model_table(model, "Sales", timeout=10)
                if eq_ok and sales_ok:
                    found = True
                    break
                else:
                    logging.warning(f"第{method_idx+1}种方法未检测到模型表，尝试下一种方法。")
                    time.sleep(3)
            finally:
                wb_xl.Close(SaveChanges=True)
                xl.Quit()
        if not found:
            raise Exception("尝试所有方法后仍未检测到模型表: Equipment 或 Sales，请检查Power Query查询和数据源。")
        logging.info("创建数据模型和度量值")
        create_data_model(output_file)
        logging.info("创建透视表")
        create_pivot_tables(output_file)
        logging.info("Excel模板创建完成")
        return output_file
    except Exception as e:
        logging.error(f"创建Excel模板时出错: {str(e)}", exc_info=True)
        raise
    finally:
        if wb:
            wb.close()
        if app:
            app.quit()

def create_power_queries(file_path):
    xl = win32.Dispatch('Excel.Application')
    xl.Visible = False
    wb_xl = xl.Workbooks.Open(file_path)
    try:
        dsn_name = "SalesReportDB"
        try:
            test_conn = win32.Dispatch('ADODB.Connection')
            test_conn.Open(f'DSN={dsn_name}')
            test_conn.Close()
        except Exception as e:
            raise Exception(f"ODBC数据源 {dsn_name} 连接失败: {str(e)}")
        equipment_m_code = '''
let
    Source = Odbc.DataSource("DSN=SalesReportDB", [HierarchicalNavigation=true]),
    Equipment_ID = Source{[Schema="main",Item="Equipment_ID"]}[Data],
    FilteredRows = Table.SelectRows(Equipment_ID, each 
        ([CurrentFlag] = null) and 
        [STATE] <> null and 
        [Chair_Serial_No] <> null
    ),
    CleanedData = Table.TransformColumns(FilteredRows, {
        {"Quantity", each if _ = null then 0 else _},
        {"Rental", each if _ = null then "0" else Text.Trim(_)},
        {"Layer", each if _ = null then "未分类" else _}
    }),
    SelectedColumns = Table.SelectColumns(CleanedData, {"STATE", "Location", "Chair_Serial_No", "Quantity", "Rental", "Layer", "DATE"})
in
    SelectedColumns
'''
        sales_m_code = '''
let
    Source = Odbc.DataSource("DSN=SalesReportDB", [HierarchicalNavigation=true]),
    Daily_Sales = Source{[Schema="main",Item="Daily_Equipment_Sales"]}[Data],
    FilteredRows = Table.SelectRows(Daily_Sales, each 
        [Sale_Date] >= #date(2025, 3, 1) and 
        [Sale_Date] <= #date(2025, 3, 31) and
        [Chair_Serial_No] <> null
    ),
    CleanedData = Table.TransformColumns(FilteredRows, {
        {"IOT_Price", each if _ = null then 0 else _},
        {"ZERO_Price", each if _ = null then 0 else _},
        {"Total_Price", each if _ = null then 0 else _}
    }),
    SelectedColumns = Table.SelectColumns(CleanedData, {"Chair_Serial_No", "Sale_Date", "IOT_Price", "ZERO_Price", "Total_Price"})
in
    SelectedColumns
'''
        xl.Run("CreatePowerQuery", "Equipment", equipment_m_code)
        xl.Run("CreatePowerQuery", "Sales", sales_m_code)
        wb_xl.Save()
    finally:
        wb_xl.Close(SaveChanges=True)
        xl.Quit()

def create_data_model(file_path):
    xl = win32.Dispatch('Excel.Application')
    xl.Visible = False
    wb_xl = xl.Workbooks.Open(file_path)
    try:
        model = wb_xl.Model
        from_table = model.ModelTables("Equipment")
        to_table = model.ModelTables("Sales")
        from_col = from_table.ModelTableColumns("Chair_Serial_No")
        to_col = to_table.ModelTableColumns("Chair_Serial_No")
        rel_exists = False
        for rel in model.ModelRelationships:
            if (rel.FromColumn.Name == "Chair_Serial_No" and rel.ToColumn.Name == "Chair_Serial_No"):
                rel_exists = True
                break
        if not rel_exists:
            model.ModelRelationships.Add(from_col, to_col)
        dax_measures = {
            "Total Sales": "SUMX(Sales, Sales[IOT_Price] + Sales[ZERO_Price])",
            "Total Rental": '''
VAR CurrentRental = TRIM(UPPER(SELECTEDVALUE(Equipment[Rental], "0")))
VAR CurrentSales = [Total Sales]
VAR RentalValue =
    SWITCH(
        TRUE(),
        ISBLANK(CurrentRental) || CurrentRental = "0", 0,
        CONTAINSSTRING(CurrentRental, "/"),
            VAR parts = SUBSTITUTE(CurrentRental, " ", "")
            VAR numerator = VALUE(LEFT(parts, FIND("/", parts) - 1))
            VAR denominator = VALUE(MID(parts, FIND("/", parts) + 1, LEN(parts)))
            RETURN IF(denominator = 0, 0, numerator / denominator) * SUM(Equipment[Quantity]),
        CONTAINSSTRING(CurrentRental, "%"),
            VAR percentage = VALUE(SUBSTITUTE(CurrentRental, "%", ""))
            RETURN CurrentSales * percentage / 100,
        CurrentRental = "FOC", 0,
        CONTAINSSTRING(CurrentRental, "RM"),
            VALUE(SUBSTITUTE(CurrentRental, "RM", "")) * SUM(Equipment[Quantity]),
        CONTAINSSTRING(CurrentRental, "+") && CONTAINSSTRING(CurrentRental, "%"),
            VAR parts = SUBSTITUTE(CurrentRental, " ", "")
            VAR base = VALUE(LEFT(parts, FIND("+", parts) - 1))
            VAR pct = VALUE(SUBSTITUTE(MID(parts, FIND("+", parts) + 1, LEN(parts)), "%", "")) / 100
            RETURN base + (CurrentSales * pct),
        ISNUMBER(VALUE(CurrentRental)),
            VALUE(CurrentRental) * SUM(Equipment[Quantity]),
        0
    )
RETURN IF(RentalValue >= 1, RentalValue, RentalValue * [Total Sales])
''',
            "Profit": "[Total Sales] - [Total Rental]",
            "Profit Per Unit": "DIVIDE([Profit], SUM(Equipment[Quantity]), 0)",
            "Sales YTD": '''
VAR CurrentYear = YEAR(MAX(Sales[Sale_Date]))
RETURN CALCULATE([Total Sales], FILTER(ALL(Sales[Sale_Date]), YEAR(Sales[Sale_Date]) = CurrentYear && Sales[Sale_Date] <= MAX(Sales[Sale_Date])))
''',
            "Sales Growth": '''
VAR CurrentSales = [Total Sales]
VAR PreviousSales = CALCULATE([Total Sales], DATEADD(Sales[Sale_Date], -1, MONTH))
RETURN IF(PreviousSales = 0, 0, (CurrentSales - PreviousSales) / PreviousSales)
''',
            "Sales Moving Average": "AVERAGEX(DATESINPERIOD(Sales[Sale_Date], MAX(Sales[Sale_Date]), -7, DAY), [Total Sales])"
        }
        for measure_name, dax_formula in dax_measures.items():
            exists = False
            for ms in model.ModelMeasures:
                if ms.Name == measure_name:
                    exists = True
                    ms.Formula = dax_formula
                    break
            if not exists:
                model.ModelMeasures.Add(measure_name, dax_formula)
        wb_xl.Save()
    finally:
        wb_xl.Close(SaveChanges=True)
        xl.Quit()

def create_pivot_tables(file_path):
    xl = win32.Dispatch('Excel.Application')
    xl.Visible = False
    wb_xl = xl.Workbooks.Open(file_path)
    try:
        xl.Run("CreateMainPivotTable")
        xl.Run("CreateDatePivotTable")
        wb_xl.Save()
    finally:
        wb_xl.Close(SaveChanges=True)
        xl.Quit()

if __name__ == "__main__":
    try:
        output_path = create_excel_template()
        print(f"Excel模板已创建: {output_path}")
    except Exception as e:
        print(f"创建失败: {str(e)}\n详细错误信息请查看日志文件")