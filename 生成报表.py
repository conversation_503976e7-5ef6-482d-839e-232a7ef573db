import sqlite3
import os
import datetime
import pyodbc
from typing import List, Optional, Dict, Any

# 配置常量
DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
# ODBC连接字符串 - 使用DSN
ODBC_CONN_STR = "DSN=SalesReportDB"
# 或者继续使用直接连接方式
# ODBC_CONN_STR = "DRIVER={SQLite3 ODBC Driver};DATABASE=" + DB_PATH

class DatabaseManager:
    @staticmethod
    def get_connection(use_odbc=False):
        try:
            if use_odbc:
                # 使用ODBC连接
                conn = pyodbc.connect(ODBC_CONN_STR)
                return conn
            else:
                # 使用SQLite直接连接
                conn = sqlite3.connect(DB_PATH, timeout=30, isolation_level='IMMEDIATE')
                conn.execute("PRAGMA foreign_keys = ON")
                return conn
        except Exception as e:
            print(f"数据库连接失败: {str(e)}")
            raise

# ... 其余代码保持不变 ...