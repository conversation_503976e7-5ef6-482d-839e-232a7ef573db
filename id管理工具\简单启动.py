# -*- coding: utf-8 -*-
"""
ID管理工具简单启动脚本
避免复杂的初始化问题，确保程序能够正常启动
"""

import sys
import os
import traceback

def main():
    """主函数"""
    print("🎯 ID管理工具启动中...")
    print("=" * 50)
    
    try:
        # 设置环境变量，禁用优化模块以避免初始化问题
        os.environ['DISABLE_OPTIMIZATION'] = '1'
        
        print("📦 导入主程序模块...")
        
        # 修改导入方式，直接运行主程序
        import ID管理工具
        
        print("🚀 启动应用程序...")
        
        # 直接调用main函数
        if hasattr(ID管理工具, 'main'):
            ID管理工具.main()
        else:
            # 如果没有main函数，创建根窗口并启动
            import tkinter as tk
            root = tk.Tk()
            app = ID管理工具.EquipmentManager(root)
            root.mainloop()
        
        print("✅ 程序正常退出")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保所有必要的文件都在当前目录中")
        return False
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n📋 错误详情:")
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n💥 意外错误: {e}")
        traceback.print_exc()
        input("\n按回车键退出...")
