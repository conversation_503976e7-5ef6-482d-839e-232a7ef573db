import sqlite3
import os
from typing import List, Optional

# 配置常量
DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"

class DatabaseManager:
    @staticmethod
    def get_connection():
        try:
            conn = sqlite3.connect(DB_PATH, timeout=30, isolation_level='IMMEDIATE')
            conn.execute("PRAGMA foreign_keys = ON")
            return conn
        except sqlite3.Error as e:
            print(f"数据库连接失败: {str(e)}")
            raise

def check_table_structure():
    """检查表结构和示例数据"""
    try:
        with DatabaseManager.get_connection() as conn:
            cursor = conn.cursor()
            
            tables = ['Equipment_ID', 'IOT_Sales', 'ZERO_Sales']
            for table in tables:
                print(f"\n{table} 表结构:")
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                for col in columns:
                    print(f"列名: {col[1]}, 类型: {col[2]}")
                
                print(f"\n{table} 示例数据:")
                cursor.execute(f"SELECT * FROM {table} LIMIT 1")
                row = cursor.fetchone()
                if row:
                    column_names = [description[0] for description in cursor.description]
                    print("列名:", column_names)
                    print("数据:", row)
                else:
                    print("无数据")
                
    except Exception as e:
        print(f"检查表结构时出错: {str(e)}")

def create_sales_view():
    """创建销售数据视图，支持孤儿销售数据"""
    try:
        with DatabaseManager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建每日销售统计视图
            cursor.execute("DROP VIEW IF EXISTS Daily_Sales")
            daily_sales_sql = """
            CREATE VIEW Daily_Sales AS
            SELECT 
                strftime('%Y-%m-%d', Order_time) as Sale_Date,
                SUM(COALESCE(Order_Price, 0)) as Daily_Total_Price
            FROM (
                SELECT Order_time, Order_Price FROM IOT_Sales
                UNION ALL
                SELECT Order_time, Order_Price FROM ZERO_Sales
            )
            GROUP BY Sale_Date
            """
            cursor.execute(daily_sales_sql)
            print("成功创建Daily_Sales视图")
            
            # 创建IOT销售日期视图
            cursor.execute("DROP VIEW IF EXISTS IOT_Daily_Sales")
            iot_daily_sql = """
            CREATE VIEW IOT_Daily_Sales AS
            SELECT 
                Equipment_ID as Chair_Serial_No,
                strftime('%Y-%m-%d', Order_time) as Sale_Date,
                SUM(Order_Price) as Daily_IOT_Price,
                COUNT(*) as Daily_IOT_Count
            FROM IOT_Sales
            GROUP BY Equipment_ID, Sale_Date
            """
            cursor.execute(iot_daily_sql)
            print("成功创建IOT_Daily_Sales视图")
            
            # 创建ZERO销售日期视图
            cursor.execute("DROP VIEW IF EXISTS ZERO_Daily_Sales")
            zero_daily_sql = """
            CREATE VIEW ZERO_Daily_Sales AS
            SELECT 
                Equipment_ID as Chair_Serial_No,
                strftime('%Y-%m-%d', Order_time) as Sale_Date,
                SUM(Order_Price) as Daily_ZERO_Price,
                COUNT(*) as Daily_ZERO_Count
            FROM ZERO_Sales
            GROUP BY Equipment_ID, Sale_Date
            """
            cursor.execute(zero_daily_sql)
            print("成功创建ZERO_Daily_Sales视图")
            
            # 创建设备有效期视图
            cursor.execute("DROP VIEW IF EXISTS Equipment_Valid")
            equipment_valid_sql = """
            CREATE VIEW Equipment_Valid AS
            SELECT 
                Chair_Serial_No,
                STATE,
                Location,
                Quantity,
                Layer,
                CASE 
                    WHEN Effective_From IS NULL THEN date('1900-01-01')
                    ELSE date(Effective_From)
                END as Effective_From,
                CASE 
                    WHEN Effective_To IS NULL THEN date('9999-12-31')
                    ELSE date(Effective_To)
                END as Effective_To,
                Rental,
                ID,
                DATE,
                ROW_NUMBER() OVER (
                    PARTITION BY Chair_Serial_No 
                    ORDER BY 
                        CASE 
                            WHEN Effective_From IS NULL THEN 1 
                            ELSE 0 
                        END DESC,
                        date(Effective_From) DESC,
                        ID DESC
                ) as Version_Rank
            FROM Equipment_ID
            """
            cursor.execute(equipment_valid_sql)
            print("成功创建Equipment_Valid视图")
            
            # 创建每日设备销售汇总视图（支持孤儿销售），并加上DATE字段
            cursor.execute("DROP VIEW IF EXISTS Daily_Equipment_Sales")
            daily_equipment_sql = """
            CREATE VIEW Daily_Equipment_Sales AS
            SELECT 
                e.Chair_Serial_No,
                e.STATE,
                e.Location,
                e.Quantity,
                e.Layer,
                e.Effective_From,
                e.Effective_To,
                e.Rental,
                e.DATE,
                d.Sale_Date,
                COALESCE(i.Daily_IOT_Price, 0) as IOT_Price,
                COALESCE(z.Daily_ZERO_Price, 0) as ZERO_Price,
                COALESCE(i.Daily_IOT_Count, 0) as IOT_Count,
                COALESCE(z.Daily_ZERO_Count, 0) as ZERO_Count,
                (COALESCE(i.Daily_IOT_Price, 0) + COALESCE(z.Daily_ZERO_Price, 0)) as Total_Price
            FROM Equipment_Valid e
            CROSS JOIN (
                SELECT DISTINCT Sale_Date FROM 
                (
                    SELECT Sale_Date FROM IOT_Daily_Sales
                    UNION
                    SELECT Sale_Date FROM ZERO_Daily_Sales
                )
            ) d
            LEFT JOIN IOT_Daily_Sales i ON e.Chair_Serial_No = i.Chair_Serial_No AND d.Sale_Date = i.Sale_Date
            LEFT JOIN ZERO_Daily_Sales z ON e.Chair_Serial_No = z.Chair_Serial_No AND d.Sale_Date = z.Sale_Date
            WHERE e.Version_Rank = 1
            AND d.Sale_Date BETWEEN e.Effective_From AND e.Effective_To

            UNION ALL

            SELECT 
                i.Chair_Serial_No,
                NULL AS STATE,
                NULL AS Location,
                NULL AS Quantity,
                NULL AS Layer,
                NULL AS Effective_From,
                NULL AS Effective_To,
                NULL AS Rental,
                NULL AS DATE,
                i.Sale_Date,
                i.Daily_IOT_Price,
                0 AS ZERO_Price,
                i.Daily_IOT_Count,
                0 AS ZERO_Count,
                i.Daily_IOT_Price AS Total_Price
            FROM IOT_Daily_Sales i
            LEFT JOIN Equipment_Valid e
                ON i.Chair_Serial_No = e.Chair_Serial_No
                AND e.Version_Rank = 1
                AND i.Sale_Date BETWEEN e.Effective_From AND e.Effective_To
            WHERE e.Chair_Serial_No IS NULL

            UNION ALL

            SELECT 
                z.Chair_Serial_No,
                NULL AS STATE,
                NULL AS Location,
                NULL AS Quantity,
                NULL AS Layer,
                NULL AS Effective_From,
                NULL AS Effective_To,
                NULL AS Rental,
                NULL AS DATE,
                z.Sale_Date,
                0 AS IOT_Price,
                z.Daily_ZERO_Price,
                0 AS IOT_Count,
                z.Daily_ZERO_Count,
                z.Daily_ZERO_Price AS Total_Price
            FROM ZERO_Daily_Sales z
            LEFT JOIN Equipment_Valid e
                ON z.Chair_Serial_No = e.Chair_Serial_No
                AND e.Version_Rank = 1
                AND z.Sale_Date BETWEEN e.Effective_From AND e.Effective_To
            WHERE e.Chair_Serial_No IS NULL
            """
            cursor.execute(daily_equipment_sql)
            print("成功创建Daily_Equipment_Sales视图")
            
            # 创建当前有效设备视图
            cursor.execute("DROP VIEW IF EXISTS Current_Equipment")
            current_equipment_sql = """
            CREATE VIEW Current_Equipment AS
            SELECT 
                Chair_Serial_No,
                STATE,
                Location,
                Quantity,
                Layer,
                Effective_From,
                Effective_To,
                Rental
            FROM Equipment_Valid
            WHERE Version_Rank = 1
            AND (Effective_To >= date('now') OR Effective_To = date('9999-12-31'))
            """
            cursor.execute(current_equipment_sql)
            print("成功创建Current_Equipment视图")
            
            # 保留原有视图
            cursor.execute("DROP VIEW IF EXISTS Sales_Combined")
            view_sql = """
            CREATE VIEW Sales_Combined AS
            SELECT 
                e.Chair_Serial_No,
                e.STATE,
                e.Location,
                e.Quantity,
                e.Layer,
                CASE 
                    WHEN e.Effective_From IS NULL THEN date('1900-01-01')
                    ELSE date(e.Effective_From)
                END as Effective_From,
                CASE 
                    WHEN e.Effective_To IS NULL THEN date('9999-12-31')
                    ELSE date(e.Effective_To)
                END as Effective_To,
                e.Rental,
                COUNT(i.ID) as IOT_Sales,
                COUNT(z.ID) as ZERO_Sales,
                COALESCE(SUM(i.Order_Price), 0) as IOT_Order_Price,
                COALESCE(SUM(z.Order_Price), 0) as ZERO_Order_Price,
                (COALESCE(SUM(i.Order_Price), 0) + COALESCE(SUM(z.Order_Price), 0)) as Total_Order_Price,
                MAX(i.Order_time) as IOT_Date,
                MAX(z.Order_time) as ZERO_Date,
                ROW_NUMBER() OVER (
                    PARTITION BY e.Chair_Serial_No 
                    ORDER BY 
                        CASE 
                            WHEN e.Effective_From IS NULL THEN 1 
                            ELSE 0 
                        END DESC,
                        date(e.Effective_From) DESC,
                        e.ID DESC
                ) as Version_Rank
            FROM Equipment_ID e
            LEFT JOIN IOT_Sales i ON e.Chair_Serial_No = i.Equipment_ID
            LEFT JOIN ZERO_Sales z ON e.Chair_Serial_No = z.Equipment_ID
            GROUP BY e.Chair_Serial_No, e.STATE, e.Location, e.Quantity, e.Layer, 
                     e.Effective_From, e.Effective_To, e.Rental, e.ID
            """
            cursor.execute(view_sql)
            print("成功创建Sales_Combined视图")
            
            cursor.execute("DROP VIEW IF EXISTS Valid_Sales")
            valid_sales_sql = """
            CREATE VIEW Valid_Sales AS
            SELECT 
                Chair_Serial_No,
                STATE,
                Location,
                Quantity,
                Layer,
                Effective_From,
                Effective_To,
                Rental,
                IOT_Sales,
                ZERO_Sales,
                IOT_Order_Price,
                ZERO_Order_Price,
                Total_Order_Price,
                IOT_Date,
                ZERO_Date
            FROM Sales_Combined
            WHERE Version_Rank = 1
            AND (Effective_To >= date('now') OR Effective_To IS NULL)
            """
            cursor.execute(valid_sales_sql)
            print("成功创建Valid_Sales视图")
            
            conn.commit()
            return True
    except Exception as e:
        print(f"创建视图时出错: {str(e)}")
        return False

def view_sales_data():
    """查看销售数据"""
    try:
        with DatabaseManager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 查看每日销售统计
            print("\n每日销售统计:")
            cursor.execute("SELECT Sale_Date, Daily_Total_Price FROM Daily_Sales ORDER BY Sale_Date DESC LIMIT 5")
            for row in cursor.fetchall():
                print(f"日期: {row[0]}, 销售总额: {row[1]}")
            
            # 查看每日设备销售数据
            print("\n每日设备销售数据:")
            cursor.execute("""
                SELECT Sale_Date, Chair_Serial_No, Location, Total_Price, DATE
                FROM Daily_Equipment_Sales
                WHERE Total_Price > 0
                ORDER BY Sale_Date DESC, Total_Price DESC
                LIMIT 5
            """)
            for row in cursor.fetchall():
                print(f"日期: {row[0]}, 设备号: {row[1]}, 位置: {row[2]}, 销售额: {row[3]}, DATE: {row[4]}")
            
            # 查看当前有效设备
            print("\n当前有效设备:")
            cursor.execute("""
                SELECT Chair_Serial_No, Location, STATE, Effective_From, Effective_To
                FROM Current_Equipment
                LIMIT 5
            """)
            for row in cursor.fetchall():
                print(f"设备号: {row[0]}, 位置: {row[1]}, 状态: {row[2]}")
                print(f"生效日期: {row[3]} 至 {row[4]}\n")
            
            # 查看有效销售数据
            print("\n有效销售数据:")
            cursor.execute("""
                SELECT Chair_Serial_No, Location, Total_Order_Price, IOT_Date, ZERO_Date
                FROM Valid_Sales 
                WHERE IOT_Date IS NOT NULL OR ZERO_Date IS NOT NULL
                ORDER BY 
                    CASE 
                        WHEN IOT_Date > ZERO_Date OR ZERO_Date IS NULL THEN IOT_Date
                        ELSE ZERO_Date
                    END DESC
                LIMIT 5
            """)
            for row in cursor.fetchall():
                print(f"设备号: {row[0]}, 位置: {row[1]}, 总销售额: {row[2]}")
                print(f"IOT最新订单时间: {row[3]}, ZERO最新订单时间: {row[4]}\n")
            
    except Exception as e:
        print(f"查询数据时出错: {str(e)}")

def setup_database():
    """设置数据库结构"""
    print("开始设置数据库...")
    
    if create_sales_view():
        print("视图创建成功")
    else:
        print("视图创建失败")
        return False
    
    print("数据库设置完成")
    return True

if __name__ == "__main__":
    print("检查表结构和示例数据:")
    check_table_structure()
    
    print("\n创建视图:")
    if setup_database():
        print("\n查看视图数据:")
        view_sales_data()