import os
import sqlite3
import datetime
import traceback

# 配置常量
DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
LOG_PATH = r"C:\Users\<USER>\Desktop\Day Report\app_table_creation.log"

def log_message(msg):
    """记录日志消息"""
    with open(LOG_PATH, "a", encoding="utf-8") as f:
        f.write(f"{datetime.datetime.now()} {msg}\n")

def create_app_tables():
    """创建APP相关表结构"""
    try:
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
        
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()
            
            # 检查APP_Sales表是否已存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='APP_Sales'")
            if cursor.fetchone() is None:
                # 创建APP_Sales表，结构与IOT_Sales和ZERO_Sales保持一致
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS APP_Sales (
                    ID INTEGER PRIMARY KEY AUTOINCREMENT,
                    Copartner_name TEXT,
                    Order_No TEXT,
                    Order_types TEXT,
                    Order_status TEXT,
                    Order_price TEXT,
                    Payment TEXT,
                    Order_time TEXT,
                    Equipment_ID TEXT,
                    Equipment_name TEXT,
                    Branch_name TEXT,
                    Payment_date TEXT,
                    User_name TEXT,
                    Time TEXT,
                    Matched_Order_ID TEXT,
                    OrderTime_dt TEXT,
                    Import_Date TEXT,
                    Import_File TEXT
                )
                ''')
                log_message("成功创建APP_Sales表")
            else:
                log_message("APP_Sales表已存在，无需创建")
            
            conn.commit()
            return True, "APP表结构创建成功"
    
    except Exception as e:
        tb = traceback.format_exc()
        error_msg = f"创建APP表结构时出错: {str(e)}\n{tb}"
        log_message(error_msg)
        return False, error_msg

def main():
    """主函数"""
    print("===== 创建APP表结构 =====")
    success, msg = create_app_tables()
    print(msg)
    
    if success:
        print("\n现在您可以运行'数据库管理工具.py'来更新视图以包含APP销售数据")

if __name__ == "__main__":
    main()