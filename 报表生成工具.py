import os
import sqlite3
import pandas as pd
import datetime
from openpyxl import Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
import win32com.client as win32
import time

# 数据库文件路径
DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"

def create_sales_report(output_path=None, month=3, year=2025):
    """
    创建销售报表，包含IOT和ZERO平台的数据
    
    参数:
    output_path: 输出Excel文件的路径
    month: 要分析的月份 (默认为3月)
    year: 要分析的年份 (默认为2025年)
    """
    if output_path is None:
        output_path = os.path.join(os.path.dirname(DB_PATH), f"销售报表_{year}年{month}月.xlsx")
    
    # 连接数据库
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # 检查表结构
    cursor.execute("PRAGMA table_info(Equipment_ID)")
    equipment_columns = [column[1] for column in cursor.fetchall()]
    print(f"Equipment_ID表的列: {equipment_columns}")
    
    cursor.execute("PRAGMA table_info(IOT_Sales)")
    iot_columns = [column[1] for column in cursor.fetchall()]
    print(f"IOT_Sales表的列: {iot_columns}")
    
    cursor.execute("PRAGMA table_info(ZERO_Sales)")
    zero_columns = [column[1] for column in cursor.fetchall()]
    print(f"ZERO_Sales表的列: {zero_columns}")
    
    # 检查数据库中的日期范围
    cursor.execute("SELECT MIN(Order_time), MAX(Order_time) FROM IOT_Sales")
    iot_date_range = cursor.fetchone()
    print(f"IOT_Sales表的日期范围: {iot_date_range}")
    
    cursor.execute("SELECT MIN(Order_time), MAX(Order_time) FROM ZERO_Sales")
    zero_date_range = cursor.fetchone()
    print(f"ZERO_Sales表的日期范围: {zero_date_range}")
    
    # 如果数据库中有数据，使用数据库中的实际年份
    if iot_date_range[0] is not None:
        try:
            actual_year = int(iot_date_range[0].split('-')[0])
            if actual_year != year:
                print(f"警告: 使用数据库中的实际年份 {actual_year} 替代默认年份 {year}")
                year = actual_year
        except:
            pass
    
    # 构建日期过滤条件
    start_date = f"{year}-{month:02d}-01"
    
    # 计算下个月的第一天作为结束日期
    if month == 12:
        end_date = f"{year+1}-01-01"
    else:
        end_date = f"{year}-{month+1:02d}-01"
    
    print(f"查询日期范围: {start_date} 到 {end_date}")
    
    # 首先获取设备数据
    try:
        equipment_query = """
        SELECT 
            STATE,
            Location,
            Quantity,
            Chair_Serial_No,
            Rental
        FROM Equipment_ID
        """
        equipment_df = pd.read_sql_query(equipment_query, conn)
        print(f"成功读取设备数据: {len(equipment_df)}行")
    except Exception as e:
        print(f"读取设备数据时出错: {str(e)}")
        equipment_df = pd.DataFrame(columns=['STATE', 'Location', 'Quantity', 'Chair_Serial_No', 'Rental'])
    
    # 查询IOT销售数据 - 按日期分组
    iot_query = f"""
    SELECT 
        Equipment_ID,
        Branch_name,
        date(Order_time) as Order_date,
        COALESCE(SUM(Payment), SUM(Order_price), 0) as Rental
    FROM IOT_Sales
    WHERE Order_time >= '{start_date}' AND Order_time < '{end_date}'
    GROUP BY Equipment_ID, Branch_name, date(Order_time)
    ORDER BY Order_date
    """
    
    # 查询ZERO销售数据 - 按日期分组
    zero_query = f"""
    SELECT 
        Equipment_ID,
        Branch_name,
        date(Order_time) as Order_date,
        COALESCE(SUM(Payment), SUM(Order_price), 0) as Rental
    FROM ZERO_Sales
    WHERE Order_time >= '{start_date}' AND Order_time < '{end_date}'
    GROUP BY Equipment_ID, Branch_name, date(Order_time)
    ORDER BY Order_date
    """
    
    # 读取数据
    try:
        iot_df = pd.read_sql_query(iot_query, conn)
        print(f"成功读取IOT数据: {len(iot_df)}行")
        
        # 如果没有数据，尝试不使用日期过滤
        if len(iot_df) == 0:
            print("尝试不使用日期过滤查询IOT数据...")
            iot_query_no_date = """
            SELECT 
                Equipment_ID,
                Branch_name,
                date(Order_time) as Order_date,
                COALESCE(SUM(Payment), SUM(Order_price), 0) as Rental
            FROM IOT_Sales
            GROUP BY Equipment_ID, Branch_name, date(Order_time)
            ORDER BY Order_date
            LIMIT 1000
            """
            iot_df = pd.read_sql_query(iot_query_no_date, conn)
            print(f"不使用日期过滤，成功读取IOT数据: {len(iot_df)}行")
    except Exception as e:
        print(f"读取IOT数据时出错: {str(e)}")
        iot_df = pd.DataFrame(columns=['Equipment_ID', 'Branch_name', 'Order_date', 'Rental'])
    
    try:
        zero_df = pd.read_sql_query(zero_query, conn)
        print(f"成功读取ZERO数据: {len(zero_df)}行")
        
        # 如果没有数据，尝试不使用日期过滤
        if len(zero_df) == 0:
            print("尝试不使用日期过滤查询ZERO数据...")
            zero_query_no_date = """
            SELECT 
                Equipment_ID,
                Branch_name,
                date(Order_time) as Order_date,
                COALESCE(SUM(Payment), SUM(Order_price), 0) as Rental
            FROM ZERO_Sales
            GROUP BY Equipment_ID, Branch_name, date(Order_time)
            ORDER BY Order_date
            LIMIT 1000
            """
            zero_df = pd.read_sql_query(zero_query_no_date, conn)
            print(f"不使用日期过滤，成功读取ZERO数据: {len(zero_df)}行")
    except Exception as e:
        print(f"读取ZERO数据时出错: {str(e)}")
        zero_df = pd.DataFrame(columns=['Equipment_ID', 'Branch_name', 'Order_date', 'Rental'])
    
    # 关闭数据库连接
    conn.close()
    
    # 处理空值
    equipment_df.fillna({'Location': '未知位置', 'STATE': '未知ID', 'Quantity': 0, 'Rental': 0}, inplace=True)
    iot_df.fillna({'Branch_name': '未知位置', 'Equipment_ID': '未知ID', 'Rental': 0}, inplace=True)
    zero_df.fillna({'Branch_name': '未知位置', 'Equipment_ID': '未知ID', 'Rental': 0}, inplace=True)
    
    # 重命名列以便合并
    iot_df.rename(columns={'Equipment_ID': 'Chair_Serial_No', 'Branch_name': 'Sales_Location', 'Order_date': 'DATE'}, inplace=True)
    zero_df.rename(columns={'Equipment_ID': 'Chair_Serial_No', 'Branch_name': 'Sales_Location', 'Order_date': 'DATE'}, inplace=True)
    
    # 添加来源标识
    iot_df['Source'] = 'IOT'
    zero_df['Source'] = 'ZERO'
    
    # 合并销售数据
    sales_df = pd.concat([iot_df, zero_df])
    
    # 将销售数据与设备数据关联
    merged_df = pd.merge(
        equipment_df,
        sales_df,
        on='Chair_Serial_No',
        how='left'
    )
    
    # 处理合并后的空值
    merged_df.fillna({'Rental_y': 0, 'Source': '无销售', 'DATE': f'{year}-{month:02d}-01'}, inplace=True)
    
    # 重命名列，避免歧义
    merged_df.rename(columns={'Rental_x': 'Equipment_Rental', 'Rental_y': 'Sales_Rental'}, inplace=True)
    
    # 创建总销售报表 - 按位置和日期汇总
    location_date_summary = merged_df.groupby(['Location', 'DATE']).agg({
        'Quantity': 'sum',
        'Sales_Rental': 'sum'
    }).reset_index()
    
    # 计算每张椅子的平均销售额
    location_date_summary['Per_Chair'] = location_date_summary['Sales_Rental'] / location_date_summary['Quantity']
    location_date_summary['Per_Chair'] = location_date_summary['Per_Chair'].round(2)
    
    # 创建按位置汇总的报表
    location_summary = merged_df.groupby(['Location']).agg({
        'Quantity': 'sum',
        'Sales_Rental': 'sum'
    }).reset_index()
    
    location_summary['Per_Chair'] = location_summary['Sales_Rental'] / location_summary['Quantity']
    location_summary['Per_Chair'] = location_summary['Per_Chair'].round(2)
    location_summary['DATE'] = '总计'
    
    # 合并日期汇总和总计
    final_summary = pd.concat([location_date_summary, location_summary])
    
    # 创建数据透视表 - 按来源和日期汇总
    pivot_df = merged_df.pivot_table(
        index=['Location', 'DATE'],
        columns=['Source'],
        values=['Sales_Rental'],
        aggfunc='sum',
        fill_value=0
    )
    
    # 重置索引，使其成为普通列
    pivot_df.reset_index(inplace=True)
    
    # 调整列名格式
    pivot_df.columns = [f"{col[0]}_{col[1]}" if col[1] != '' else col[0] for col in pivot_df.columns]
    
    # 更新列名映射
    column_mapping = {
        'Sales_Rental_IOT': f'IOT {month:02d}/{year}',
        'Sales_Rental_ZERO': f'ZERO {month:02d}/{year}',
        'Sales_Rental_无销售': f'无销售 {month:02d}/{year}',
    }
    
    pivot_df.rename(columns=column_mapping, inplace=True)
    
    # 计算总销售额
    pivot_df['Total'] = pivot_df[f'IOT {month:02d}/{year}'] + pivot_df[f'ZERO {month:02d}/{year}']
    
    # 创建Excel工作簿
    wb = Workbook()
    
    # 创建总销售报表工作表
    ws_summary = wb.active
    ws_summary.title = "销售总报表"
    
    # 添加标题
    ws_summary.append(['IOT AND ZERO TOTAL SALES REPORT'])
    ws_summary.append([])  # 空行
    
    # 添加标题行
    day = 1
    ws_summary.append(['Location', 'Rental', 'DATE', 'Quantity', 'Lay', f'IOT {month:02d}/{day:02d}/{str(year)[-2:]}', f'ZERO {month:02d}/{day:02d}/{str(year)[-2:]}', 'Total', 'Per. Chair'])
    
    # 合并数据
    summary_data = pd.merge(
        final_summary,
        pivot_df,
        on=['Location', 'DATE'],
        how='left'
    )
    
    # 填充空值
    summary_data.fillna({
        f'IOT {month:02d}/{year}': 0,
        f'ZERO {month:02d}/{year}': 0,
        'Total': 0
    }, inplace=True)
    
    # 添加Lay列（默认为G）
    summary_data['Lay'] = 'G'
    
    # 重新排序列
    summary_data = summary_data[[
        'Location', 'Equipment_Rental', 'DATE', 'Quantity', 'Lay',
        f'IOT {month:02d}/{year}', f'ZERO {month:02d}/{year}',
        'Total', 'Per_Chair'
    ]]
    
    # 重命名列
    summary_data.rename(columns={'Equipment_Rental': 'Rental'}, inplace=True)
    
    # 添加数据
    for r in dataframe_to_rows(summary_data, index=False, header=False):
        ws_summary.append(r)
    
    # 创建设备信息工作表
    ws_equipment = wb.create_sheet(title="设备信息")
    
    # 添加标题行
    ws_equipment.append(['STATE', '位置', '数量', '设备序列号', '租金'])
    
    # 添加数据
    for r in dataframe_to_rows(equipment_df, index=False, header=False):
        ws_equipment.append(r)
    
    # 创建IOT销售工作表
    ws_iot = wb.create_sheet(title="IOT销售")
    
    # 添加标题行
    ws_iot.append(['设备序列号', '销售位置', '日期', '销售额', '来源'])
    
    # 添加数据
    for r in dataframe_to_rows(iot_df, index=False, header=False):
        ws_iot.append(r)
    
    # 创建ZERO销售工作表
    ws_zero = wb.create_sheet(title="ZERO销售")
    
    # 添加标题行
    ws_zero.append(['设备序列号', '销售位置', '日期', '销售额', '来源'])
    
    # 添加数据
    for r in dataframe_to_rows(zero_df, index=False, header=False):
        ws_zero.append(r)
    
    # 创建合并数据工作表
    ws_merged = wb.create_sheet(title="合并数据")
    
    # 添加标题行
    ws_merged.append(['STATE', '位置', '设备序列号', '设备数量', '设备租金', '日期', '销售位置', '销售额', '来源'])
    
    # 添加数据
    for r in dataframe_to_rows(merged_df[['STATE', 'Location', 'Chair_Serial_No', 'Quantity', 'Equipment_Rental', 'DATE', 'Sales_Location', 'Sales_Rental', 'Source']], index=False, header=False):
        ws_merged.append(r)
    
    # 创建数据透视表工作表
    ws_pivot = wb.create_sheet(title="数据透视表")
    ws_pivot.append(['此工作表将在Excel中自动生成数据透视表'])
    
    # 设置列宽
    for ws in [ws_summary, ws_equipment, ws_iot, ws_zero, ws_merged, ws_pivot]:
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width
    
    # 设置标题样式
    title_cell = ws_summary['A1']
    title_cell.font = Font(bold=True, size=14)
    ws_summary.merge_cells('A1:I1')
    title_cell.alignment = Alignment(horizontal='center')
    
    # 设置表头样式
    header_row = ws_summary[3]
    for cell in header_row:
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="FFD700", end_color="FFD700", fill_type="solid")
    
    # 保存Excel文件
    wb.save(output_path)
    
    print(f"报表已生成: {output_path}")
    return output_path

def create_pivot_tables(excel_path):
    """
    使用Excel COM对象自动创建数据透视表
    
    参数:
    excel_path: Excel文件路径
    """
    try:
        print("正在启动Excel应用程序...")
        excel = win32.gencache.EnsureDispatch('Excel.Application')
        excel.Visible = True
        print("正在打开工作簿...")
        wb = excel.Workbooks.Open(excel_path)
        
        # 获取数据源工作表
        source_sheet = wb.Sheets("合并数据")
        
        # 清空并重新创建数据透视表工作表，避免重叠问题
        try:
            pivot_sheet = wb.Sheets("数据透视表")
            pivot_sheet.Delete()
        except:
            pass
        
        pivot_sheet = wb.Sheets.Add()
        pivot_sheet.Name = "数据透视表"
        
        # 添加标题
        pivot_sheet.Range("A1").Value = "IOT AND ZERO TOTAL SALES REPORT"
        pivot_sheet.Range("A1:G1").MergeCells = True
        pivot_sheet.Range("A1").Font.Bold = True
        pivot_sheet.Range("A1").Font.Size = 14
        pivot_sheet.Range("A1").HorizontalAlignment = win32.constants.xlCenter
        
        # 获取数据范围
        data_range = source_sheet.UsedRange
        
        # 创建数据透视表缓存
        print("正在创建数据透视表...")
        pivot_cache = wb.PivotCaches().Create(
            SourceType=win32.constants.xlDatabase,
            SourceData=data_range,
            Version=win32.constants.xlPivotTableVersion15
        )
        
        # 创建数据透视表 - 按位置和日期汇总
        pivot_table = pivot_cache.CreatePivotTable(
            TableDestination=pivot_sheet.Range("A3"),
            TableName="销售数据透视表"
        )
        
        # 配置数据透视表字段
        pivot_table.PivotFields("Location").Orientation = win32.constants.xlRowField
        pivot_table.PivotFields("Location").Position = 1
        
        pivot_table.PivotFields("DATE").Orientation = win32.constants.xlColumnField
        pivot_table.PivotFields("DATE").Position = 1
        
        # 添加数值字段到数据区域
        try:
            data_field = pivot_table.AddDataField(
                pivot_table.PivotFields("Sales_Rental"),
                "销售额",
                win32.constants.xlSum
            )
        except Exception as e:
            print(f"添加销售额字段失败: {str(e)}")
        
        # 创建第二个数据透视表 - 按来源汇总
        source_pivot_sheet = wb.Sheets.Add()
        source_pivot_sheet.Name = "来源汇总"
        
        source_pivot_sheet.Range("A1").Value = "IOT AND ZERO SOURCE SUMMARY"
        source_pivot_sheet.Range("A1:G1").MergeCells = True
        source_pivot_sheet.Range("A1").Font.Bold = True
        source_pivot_sheet.Range("A1").Font.Size = 14
        source_pivot_sheet.Range("A1").HorizontalAlignment = win32.constants.xlCenter
        
        pivot_cache2 = wb.PivotCaches().Create(
            SourceType=win32.constants.xlDatabase,
            SourceData=data_range,
            Version=win32.constants.xlPivotTableVersion15
        )
        
        pivot_table2 = pivot_cache2.CreatePivotTable(
            TableDestination=source_pivot_sheet.Range("A3"),
            TableName="来源汇总透视表"
        )
        
        # 配置数据透视表字段
        pivot_table2.PivotFields("Location").Orientation = win32.constants.xlRowField
        pivot_table2.PivotFields("Location").Position = 1
        
        pivot_table2.PivotFields("Source").Orientation = win32.constants.xlColumnField
        pivot_table2.PivotFields("Source").Position = 1
        
        # 添加数值字段到数据区域
        try:
            data_field = pivot_table2.AddDataField(
                pivot_table2.PivotFields("Sales_Rental"),
                "销售额",
                win32.constants.xlSum
            )
        except Exception as e:
            print(f"添加销售额字段失败: {str(e)}")
        
        # 创建第三个数据透视表 - 每日销售汇总
        daily_sheet = wb.Sheets.Add()
        daily_sheet.Name = "每日销售汇总"
        
        daily_sheet.Range("A1").Value = "IOT AND ZERO DAILY SALES SUMMARY"
        daily_sheet.Range("A1:G1").MergeCells = True
        daily_sheet.Range("A1").Font.Bold = True
        daily_sheet.Range("A1").Font.Size = 14
        daily_sheet.Range("A1").HorizontalAlignment = win32.constants.xlCenter
        
        pivot_cache3 = wb.PivotCaches().Create(
            SourceType=win32.constants.xlDatabase,
            SourceData=data_range,
            Version=win32.constants.xlPivotTableVersion15
        )
        
        pivot_table3 = pivot_cache3.CreatePivotTable(
            TableDestination=daily_sheet.Range("A3"),
            TableName="每日销售汇总透视表"
        )
        
        # 配置数据透视表字段
        pivot_table3.PivotFields("DATE").Orientation = win32.constants.xlRowField
        pivot_table3.PivotFields("DATE").Position = 1
        
        # 添加数值字段到数据区域
        try:
            data_field = pivot_table3.AddDataField(
                pivot_table3.PivotFields("Sales_Rental"),
                "总销售额",
                win32.constants.xlSum
            )
        except Exception as e:
            print(f"添加销售额字段失败: {str(e)}")
        
        try:
            data_field = pivot_table3.AddDataField(
                pivot_table3.PivotFields("Quantity"),
                "设备数量",
                win32.constants.xlSum
            )
        except Exception as e:
            print(f"添加设备数量字段失败: {str(e)}")
        
        # 保存工作簿
        wb.Save()
        print("数据透视表创建完成并已保存")
        
        return True
    except Exception as e:
        print(f"创建数据透视表时出错: {str(e)}")
        try:
            wb.Save()
        except:
            pass
        return False

def main():
    # 获取当前年月
    now = datetime.datetime.now()
    year = now.year
    month = now.month
    
    # 创建报表
    report_path = create_sales_report(month=month, year=year)
    
    # 自动创建数据透视表
    print("\n正在创建数据透视表...")
    success = create_pivot_tables(os.path.abspath(report_path))
    
    if success:
        print("\n报表和数据透视表已生成完成。")
    else:
        print("\n报表已生成，但数据透视表创建失败。您可以手动创建数据透视表。")
    
    print(f"建议操作:")
    print(f"1. 查看已生成的Excel文件中的'销售总报表'工作表，查看每日销售数据")
    print(f"2. 查看'数据透视表'、'来源汇总'和'每日销售汇总'工作表，了解不同维度的销售情况")
    print(f"3. 如需自定义，可以在'数据'选项卡中，使用'数据透视表'功能进行调整")
    print(f"4. 可以基于数据透视表创建数据透视图以可视化数据")

if __name__ == "__main__":
    main()