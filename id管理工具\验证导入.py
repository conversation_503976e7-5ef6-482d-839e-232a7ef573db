# -*- coding: utf-8 -*-
"""
验证ID管理工具.py中的所有导入是否正确
"""

import os
import sys

def check_file_imports():
    """检查ID管理工具.py中的导入"""
    print("🔍 检查ID管理工具.py中的导入...")
    
    # 检查主程序文件
    main_file = "ID管理工具.py"
    if not os.path.exists(main_file):
        print(f"❌ 主程序文件不存在: {main_file}")
        return False
    
    print(f"✅ 主程序文件存在: {main_file}")
    
    # 检查优化模块导入
    optimization_modules = [
        "constants.py",
        "database_connection_manager.py", 
        "error_handler.py",
        "config_manager.py",
        "excel_import_manager.py"
    ]
    
    print("\n📦 检查优化模块文件:")
    missing_modules = []
    
    for module_file in optimization_modules:
        if os.path.exists(module_file):
            print(f"  ✅ {module_file}")
        else:
            print(f"  ❌ {module_file}")
            missing_modules.append(module_file)
    
    # 检查数据库切换器
    print("\n🗃️ 检查数据库切换器:")
    if os.path.exists("database_switcher.py"):
        print("  ✅ database_switcher.py")
    else:
        print("  ❌ database_switcher.py")
        missing_modules.append("database_switcher.py")
    
    # 检查配置文件
    print("\n⚙️ 检查配置文件:")
    if os.path.exists("db_config.ini"):
        print("  ✅ db_config.ini")
    else:
        print("  ⚠️ db_config.ini (可选)")
    
    if missing_modules:
        print(f"\n❌ 缺少 {len(missing_modules)} 个必需文件:")
        for module in missing_modules:
            print(f"  • {module}")
        return False
    else:
        print("\n✅ 所有必需文件都存在")
        return True

def test_module_imports():
    """测试模块导入"""
    print("\n🧪 测试模块导入...")
    
    modules_to_test = [
        ("constants", "Config"),
        ("database_connection_manager", "init_database_manager"),
        ("error_handler", "handle_error"),
        ("config_manager", "config_manager"),
        ("excel_import_manager", "import_excel_data"),
        ("database_switcher", "show_database_manager")
    ]
    
    success_count = 0
    
    for module_name, test_attr in modules_to_test:
        try:
            module = __import__(module_name)
            if hasattr(module, test_attr):
                print(f"  ✅ {module_name}.{test_attr}")
                success_count += 1
            else:
                print(f"  ⚠️ {module_name} (缺少 {test_attr})")
        except ImportError as e:
            print(f"  ❌ {module_name}: {e}")
        except Exception as e:
            print(f"  💥 {module_name}: {e}")
    
    print(f"\n📊 导入测试结果: {success_count}/{len(modules_to_test)} 成功")
    return success_count == len(modules_to_test)

def check_main_program_structure():
    """检查主程序结构"""
    print("\n🏗️ 检查主程序结构...")
    
    try:
        with open("ID管理工具.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键类和方法
        checks = [
            ("class EquipmentManager", "主要类定义"),
            ("def show_database_manager", "数据库管理方法"),
            ("def refresh_after_db_switch", "数据库切换刷新方法"),
            ("from database_switcher import", "数据库切换器导入"),
            ("🗃️ 数据库管理", "数据库管理按钮"),
        ]
        
        success_count = 0
        
        for check_str, description in checks:
            if check_str in content:
                print(f"  ✅ {description}")
                success_count += 1
            else:
                print(f"  ❌ {description}")
        
        print(f"\n📊 结构检查结果: {success_count}/{len(checks)} 通过")
        return success_count == len(checks)
        
    except Exception as e:
        print(f"  ❌ 读取主程序文件失败: {e}")
        return False

def check_batch_script():
    """检查批处理脚本"""
    print("\n📜 检查批处理脚本:")
    
    if os.path.exists("启动.bat"):
        try:
            with open("启动.bat", 'r', encoding='utf-8') as f:
                content = f.read()
            
            print("  ✅ 启动.bat 存在")
            print("  📋 脚本内容:")
            for i, line in enumerate(content.strip().split('\n'), 1):
                print(f"    {i}: {line}")
            
            # 检查脚本是否正确
            if "python ID管理工具.py" in content:
                print("  ✅ 脚本调用正确的主程序")
                return True
            else:
                print("  ❌ 脚本未调用正确的主程序")
                return False
                
        except Exception as e:
            print(f"  ❌ 读取批处理脚本失败: {e}")
            return False
    else:
        print("  ⚠️ 启动.bat 不存在")
        return False

def main():
    """主验证函数"""
    print("🔬 ID管理工具导入验证")
    print("=" * 50)
    print(f"📁 当前目录: {os.getcwd()}")
    
    tests = [
        ("文件存在性检查", check_file_imports),
        ("模块导入测试", test_module_imports),
        ("主程序结构检查", check_main_program_structure),
        ("批处理脚本检查", check_batch_script),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}通过")
            else:
                failed += 1
                print(f"❌ {test_name}失败")
        except Exception as e:
            failed += 1
            print(f"💥 {test_name}异常: {e}")
    
    # 显示验证结果
    print("\n" + "=" * 50)
    print("📊 验证结果汇总:")
    print(f"  ✅ 通过: {passed}")
    print(f"  ❌ 失败: {failed}")
    print(f"  📈 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有验证通过！")
        print("✅ ID管理工具.py 调用的都是当前文件夹中的脚本")
        print("✅ 批处理脚本正确调用主程序")
        print("✅ 所有必需模块都存在且可导入")
        print("✅ 数据库切换功能已正确集成")
        
        print("\n🚀 使用方法:")
        print("  方式1: python ID管理工具.py")
        print("  方式2: 双击 启动.bat")
        print("  方式3: python 启动ID管理工具.py")
        
    else:
        print("\n⚠️ 部分验证失败，请检查相关问题。")
    
    return failed == 0

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 验证完成，程序可以正常使用！")
        else:
            print("\n❌ 验证发现问题，请修复后再使用。")
    except KeyboardInterrupt:
        print("\n👋 验证被用户中断")
    finally:
        input("\n按回车键退出...")
