# -*- coding: utf-8 -*-
"""
优化模块测试脚本
验证各个优化模块的功能是否正常
"""

import sys
import os
import traceback
import time

def test_constants():
    """测试常量模块"""
    print("🧪 测试常量模块...")
    try:
        from constants import Config
        
        # 测试基本常量访问
        assert hasattr(Config, 'DB'), "缺少DB常量"
        assert hasattr(Config, 'UI'), "缺少UI常量"
        assert hasattr(Config, 'FILE'), "缺少FILE常量"
        
        # 测试具体值
        assert Config.DB.EQUIPMENT_TABLE == "Equipment_ID", "数据库表名错误"
        assert Config.UI.DEFAULT_PAGE_SIZE > 0, "页面大小应该大于0"
        assert len(Config.FILE.EXCEL_EXTENSIONS) > 0, "Excel扩展名列表不能为空"
        
        print("  ✅ 常量模块测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 常量模块测试失败: {e}")
        return False

def test_config_manager():
    """测试配置管理模块"""
    print("🧪 测试配置管理模块...")
    try:
        from config_manager import config_manager, get_config, set_config
        
        # 测试配置读取
        db_path = get_config('database', 'path')
        assert db_path is not None, "数据库路径不能为空"
        
        # 测试配置设置
        test_value = "test_value_123"
        set_config('test', 'key', test_value)
        retrieved_value = get_config('test', 'key')
        assert retrieved_value == test_value, "配置设置/读取失败"
        
        # 测试配置验证
        validation_errors = config_manager.validate_config()
        assert isinstance(validation_errors, dict), "配置验证应该返回字典"
        
        print("  ✅ 配置管理模块测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 配置管理模块测试失败: {e}")
        return False

def test_error_handler():
    """测试错误处理模块"""
    print("🧪 测试错误处理模块...")
    try:
        from error_handler import handle_error, safe_execute, ValidationError
        
        # 测试安全执行
        def test_func():
            return "success"
        
        result = safe_execute(test_func, "测试操作", "default", show_dialog=False)
        assert result == "success", "安全执行正常函数失败"
        
        # 测试异常处理
        def error_func():
            raise ValueError("测试错误")
        
        result = safe_execute(error_func, "测试错误操作", "default", show_dialog=False)
        assert result == "default", "安全执行异常函数失败"
        
        # 测试自定义异常
        try:
            raise ValidationError("test_field", "test_value", "测试验证错误")
        except ValidationError as e:
            assert e.field == "test_field", "ValidationError字段错误"
            assert e.message == "测试验证错误", "ValidationError消息错误"
        
        print("  ✅ 错误处理模块测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 错误处理模块测试失败: {e}")
        return False

def test_database_manager():
    """测试数据库管理模块"""
    print("🧪 测试数据库管理模块...")
    try:
        from database_connection_manager import DatabaseConnectionManager
        from constants import Config
        
        # 创建测试数据库管理器
        db_path = ":memory:"  # 使用内存数据库进行测试
        db_manager = DatabaseConnectionManager(db_path)
        
        # 测试基本连接
        assert db_manager.db_path == db_path, "数据库路径设置错误"
        
        # 测试表信息获取（对于内存数据库，这可能会失败，但不应该崩溃）
        try:
            table_info = db_manager.get_table_info("sqlite_master")
            assert isinstance(table_info, list), "表信息应该返回列表"
        except Exception:
            pass  # 内存数据库可能没有表，这是正常的
        
        print("  ✅ 数据库管理模块测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库管理模块测试失败: {e}")
        return False

def test_excel_import_manager():
    """测试Excel导入管理模块"""
    print("🧪 测试Excel导入管理模块...")
    try:
        from excel_import_manager import ExcelImportManager
        
        # 创建模拟的父窗口
        class MockWindow:
            def __init__(self):
                self.title = "Mock Window"
        
        mock_window = MockWindow()
        
        # 创建导入管理器
        import_manager = ExcelImportManager(mock_window)
        
        # 测试基本属性
        assert import_manager.parent_window == mock_window, "父窗口设置错误"
        assert hasattr(import_manager, 'db_manager'), "缺少数据库管理器属性"
        
        print("  ✅ Excel导入管理模块测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ Excel导入管理模块测试失败: {e}")
        return False

def test_integration():
    """测试模块集成"""
    print("🧪 测试模块集成...")
    try:
        # 测试模块间的协作
        from constants import Config
        from config_manager import get_config
        from error_handler import safe_execute
        
        # 测试配置和常量的协作
        default_page_size = Config.UI.DEFAULT_PAGE_SIZE
        config_page_size = get_config('ui', 'page_size', default_page_size)
        assert isinstance(config_page_size, int), "页面大小应该是整数"
        
        # 测试错误处理和配置的协作
        def config_test():
            return get_config('database', 'path')
        
        result = safe_execute(config_test, "配置测试", show_dialog=False)
        assert result is not None, "配置读取失败"
        
        print("  ✅ 模块集成测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 模块集成测试失败: {e}")
        return False

def test_performance():
    """测试性能改进"""
    print("🧪 测试性能改进...")
    try:
        from database_connection_manager import DatabaseConnectionManager
        
        # 创建内存数据库进行性能测试
        db_manager = DatabaseConnectionManager(":memory:")
        
        # 创建测试表
        db_manager.execute_update("""
            CREATE TABLE test_table (
                id INTEGER PRIMARY KEY,
                name TEXT,
                value INTEGER
            )
        """)
        
        # 测试批量插入性能
        start_time = time.time()
        
        # 插入测试数据
        for i in range(100):
            db_manager.execute_update(
                "INSERT INTO test_table (name, value) VALUES (?, ?)",
                (f"test_{i}", i)
            )
        
        insert_time = time.time() - start_time
        
        # 测试查询性能
        start_time = time.time()
        
        for i in range(10):
            result = db_manager.execute_query("SELECT COUNT(*) FROM test_table")
        
        query_time = time.time() - start_time
        
        print(f"  📊 插入100条记录耗时: {insert_time:.3f}秒")
        print(f"  📊 执行10次查询耗时: {query_time:.3f}秒")
        print("  ✅ 性能测试完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 性能测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🎯 开始优化模块测试")
    print("=" * 50)
    
    tests = [
        ("常量模块", test_constants),
        ("配置管理", test_config_manager),
        ("错误处理", test_error_handler),
        ("数据库管理", test_database_manager),
        ("Excel导入", test_excel_import_manager),
        ("模块集成", test_integration),
        ("性能测试", test_performance)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            traceback.print_exc()
            failed += 1
        
        print()  # 空行分隔
    
    # 显示测试结果
    print("=" * 50)
    print("📊 测试结果汇总:")
    print(f"  ✅ 通过: {passed}")
    print(f"  ❌ 失败: {failed}")
    print(f"  📈 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("🎉 所有测试通过！优化模块工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查相关模块。")
    
    return failed == 0

def main():
    """主函数"""
    print("🔬 ID管理工具优化模块测试")
    print("=" * 50)
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print()
    
    try:
        success = run_all_tests()
        
        if success:
            print("\n✅ 所有测试通过，优化模块可以正常使用！")
            return 0
        else:
            print("\n❌ 部分测试失败，请检查相关问题。")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试过程中发生意外错误: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(1)
    finally:
        input("\n按回车键退出...")
