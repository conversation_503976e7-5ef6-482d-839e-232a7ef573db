# -*- coding: utf-8 -*-
"""
数据库切换管理器
提供数据库切换功能，支持路径记忆和快速切换
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sqlite3
import json
from typing import Optional, List
from config_manager import user_preferences, config_manager
from constants import Config


class DatabaseSwitcher:
    """数据库切换管理器"""
    
    def __init__(self, parent_window, refresh_callback=None):
        self.parent_window = parent_window
        self.refresh_callback = refresh_callback
        self.current_db_path = user_preferences.get_current_database()
    
    def show_database_manager(self):
        """显示数据库管理窗口"""
        # 创建数据库管理窗口
        db_window = tk.Toplevel(self.parent_window)
        db_window.title("数据库管理")
        db_window.geometry("600x500")
        db_window.transient(self.parent_window)
        db_window.grab_set()
        
        # 主框架
        main_frame = ttk.Frame(db_window, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🗃️ 数据库管理", 
                               font=("Segoe UI", 14, "bold"))
        title_label.pack(pady=(0, 15))
        
        # 当前数据库信息
        self._create_current_db_section(main_frame)
        
        # 最近使用的数据库
        self._create_recent_db_section(main_frame)
        
        # 操作按钮
        self._create_action_buttons(main_frame, db_window)
        
        # 居中显示窗口
        self._center_window(db_window)
    
    def _create_current_db_section(self, parent):
        """创建当前数据库信息区域"""
        current_frame = ttk.LabelFrame(parent, text="当前数据库", padding=10)
        current_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 数据库路径
        path_frame = ttk.Frame(current_frame)
        path_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(path_frame, text="路径:", font=("Segoe UI", 9, "bold")).pack(anchor=tk.W)
        
        self.current_path_var = tk.StringVar(value=self.current_db_path)
        path_entry = ttk.Entry(path_frame, textvariable=self.current_path_var, 
                              state="readonly", width=70)
        path_entry.pack(fill=tk.X, pady=(2, 0))
        
        # 数据库状态
        status_frame = ttk.Frame(current_frame)
        status_frame.pack(fill=tk.X)
        
        ttk.Label(status_frame, text="状态:", font=("Segoe UI", 9, "bold")).pack(anchor=tk.W)
        
        self.status_var = tk.StringVar()
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack(anchor=tk.W, pady=(2, 0))
        
        # 更新状态
        self._update_database_status()
    
    def _create_recent_db_section(self, parent):
        """创建最近使用数据库区域"""
        recent_frame = ttk.LabelFrame(parent, text="最近使用的数据库", padding=10)
        recent_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 创建列表框
        list_frame = ttk.Frame(recent_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 列表框和滚动条
        self.recent_listbox = tk.Listbox(list_frame, height=8)
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.recent_listbox.yview)
        self.recent_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.recent_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.recent_listbox.bind("<Double-Button-1>", self._on_recent_db_double_click)
        
        # 加载最近使用的数据库
        self._load_recent_databases()
        
        # 列表操作按钮
        list_btn_frame = ttk.Frame(recent_frame)
        list_btn_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(list_btn_frame, text="切换到选中", 
                  command=self._switch_to_selected).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(list_btn_frame, text="从列表移除", 
                  command=self._remove_from_recent).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(list_btn_frame, text="清空列表", 
                  command=self._clear_recent_list).pack(side=tk.LEFT)
    
    def _create_action_buttons(self, parent, window):
        """创建操作按钮"""
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill=tk.X, pady=(0, 0))
        
        # 左侧按钮
        left_btn_frame = ttk.Frame(btn_frame)
        left_btn_frame.pack(side=tk.LEFT)
        
        ttk.Button(left_btn_frame, text="📁 浏览选择", 
                  command=self._browse_database).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(left_btn_frame, text="🆕 创建新数据库", 
                  command=self._create_new_database).pack(side=tk.LEFT, padx=(0, 10))
        
        # 右侧按钮
        right_btn_frame = ttk.Frame(btn_frame)
        right_btn_frame.pack(side=tk.RIGHT)
        
        ttk.Button(right_btn_frame, text="关闭", 
                  command=window.destroy).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(right_btn_frame, text="刷新", 
                  command=self._refresh_window).pack(side=tk.RIGHT, padx=(10, 0))
    
    def _update_database_status(self):
        """更新数据库状态"""
        try:
            if os.path.exists(self.current_db_path):
                # 尝试连接数据库
                conn = sqlite3.connect(self.current_db_path, timeout=3.0)
                cursor = conn.cursor()
                
                # 检查表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                # 检查记录数
                if any('Equipment_ID' in table[0] for table in tables):
                    cursor.execute("SELECT COUNT(*) FROM Equipment_ID")
                    count = cursor.fetchone()[0]
                    self.status_var.set(f"✅ 连接正常 | {len(tables)} 个表 | {count} 条记录")
                else:
                    self.status_var.set(f"⚠️ 连接正常但缺少主表 | {len(tables)} 个表")
                
                conn.close()
            else:
                self.status_var.set("❌ 文件不存在")
                
        except Exception as e:
            self.status_var.set(f"❌ 连接失败: {str(e)[:50]}...")
    
    def _load_recent_databases(self):
        """加载最近使用的数据库"""
        self.recent_listbox.delete(0, tk.END)
        recent_dbs = user_preferences.get_recent_databases()
        
        for db_path in recent_dbs:
            # 显示简化的路径和状态
            display_text = self._format_db_display(db_path)
            self.recent_listbox.insert(tk.END, display_text)
    
    def _format_db_display(self, db_path: str) -> str:
        """格式化数据库显示文本"""
        # 获取文件名和目录
        filename = os.path.basename(db_path)
        dirname = os.path.dirname(db_path)
        
        # 检查文件状态
        if os.path.exists(db_path):
            try:
                conn = sqlite3.connect(db_path, timeout=1.0)
                conn.close()
                status = "✅"
            except:
                status = "⚠️"
        else:
            status = "❌"
        
        # 缩短路径显示
        if len(dirname) > 40:
            dirname = "..." + dirname[-37:]
        
        return f"{status} {filename} ({dirname})"
    
    def _on_recent_db_double_click(self, event):
        """双击最近使用的数据库"""
        self._switch_to_selected()
    
    def _switch_to_selected(self):
        """切换到选中的数据库"""
        selection = self.recent_listbox.curselection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一个数据库")
            return
        
        recent_dbs = user_preferences.get_recent_databases()
        if selection[0] < len(recent_dbs):
            selected_db = recent_dbs[selection[0]]
            self._switch_database(selected_db)
    
    def _remove_from_recent(self):
        """从最近列表中移除"""
        selection = self.recent_listbox.curselection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一个数据库")
            return
        
        recent_dbs = user_preferences.get_recent_databases()
        if selection[0] < len(recent_dbs):
            removed_db = recent_dbs.pop(selection[0])
            user_preferences.config_manager.set(user_preferences.preferences_section, 
                                               'recent_databases', 
                                               json.dumps(recent_dbs))
            user_preferences.save_preferences()
            self._load_recent_databases()
            messagebox.showinfo("提示", f"已从列表中移除:\n{removed_db}")
    
    def _clear_recent_list(self):
        """清空最近列表"""
        if messagebox.askyesno("确认", "确定要清空最近使用的数据库列表吗？"):
            user_preferences.config_manager.set(user_preferences.preferences_section, 
                                               'recent_databases', '[]')
            user_preferences.save_preferences()
            self._load_recent_databases()
            messagebox.showinfo("提示", "最近使用的数据库列表已清空")
    
    def _browse_database(self):
        """浏览选择数据库"""
        file_path = filedialog.askopenfilename(
            title="选择数据库文件",
            filetypes=[
                ("SQLite数据库", "*.db *.sqlite *.sqlite3"),
                ("所有文件", "*.*")
            ],
            initialdir=os.path.dirname(self.current_db_path)
        )
        
        if file_path:
            self._switch_database(file_path)
    
    def _create_new_database(self):
        """创建新数据库"""
        file_path = filedialog.asksaveasfilename(
            title="创建新数据库",
            defaultextension=".db",
            filetypes=[
                ("SQLite数据库", "*.db"),
                ("所有文件", "*.*")
            ],
            initialdir=os.path.dirname(self.current_db_path)
        )
        
        if file_path:
            try:
                # 创建新数据库并初始化表结构
                self._create_database_structure(file_path)
                self._switch_database(file_path)
                messagebox.showinfo("成功", f"新数据库创建成功:\n{file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"创建数据库失败:\n{e}")
    
    def _create_database_structure(self, db_path: str):
        """创建数据库表结构"""
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建主表（根据您的实际表结构调整）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS Equipment_ID (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                STATE TEXT,
                Location TEXT,
                Quantity INTEGER,
                Chair_Serial_No TEXT UNIQUE,
                Sim_Card_Model TEXT,
                Sim_Card_No TEXT,
                Layer TEXT,
                Company TEXT,
                Effective_From TEXT,
                Effective_To TEXT,
                Rental TEXT,
                SIMCARDID TEXT,
                Import_Date TEXT,
                Last_Updated TEXT,
                CurrentFlag TEXT,
                DATE TEXT
            )
        """)
        
        conn.commit()
        conn.close()
    
    def _switch_database(self, new_db_path: str):
        """切换数据库"""
        try:
            # 验证数据库文件
            if not os.path.exists(new_db_path):
                messagebox.showerror("错误", f"数据库文件不存在:\n{new_db_path}")
                return
            
            # 测试连接
            conn = sqlite3.connect(new_db_path, timeout=3.0)
            conn.close()
            
            # 更新配置
            user_preferences.set_current_database(new_db_path)
            user_preferences.save_preferences()
            
            # 更新当前路径
            self.current_db_path = new_db_path
            self.current_path_var.set(new_db_path)
            
            # 更新状态和列表
            self._update_database_status()
            self._load_recent_databases()
            
            # 刷新主界面
            if self.refresh_callback:
                self.refresh_callback()
            
            messagebox.showinfo("成功", f"数据库切换成功:\n{new_db_path}")
            
        except Exception as e:
            messagebox.showerror("错误", f"切换数据库失败:\n{e}")
    
    def _refresh_window(self):
        """刷新窗口"""
        self._update_database_status()
        self._load_recent_databases()
    
    def _center_window(self, window):
        """居中显示窗口"""
        window.update_idletasks()
        x = (window.winfo_screenwidth() // 2) - (window.winfo_width() // 2)
        y = (window.winfo_screenheight() // 2) - (window.winfo_height() // 2)
        window.geometry(f"+{x}+{y}")


# 便捷函数
def show_database_manager(parent_window, refresh_callback=None):
    """显示数据库管理器"""
    switcher = DatabaseSwitcher(parent_window, refresh_callback)
    switcher.show_database_manager()
