import os
import sqlite3
import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox

def select_database():
    """让用户选择数据库文件"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    file_path = filedialog.askopenfilename(
        title="选择数据库文件",
        filetypes=[("数据库文件", "*.db"), ("SQLite文件", "*.sqlite"), ("所有文件", "*.*")],
        initialdir=r"C:\Users\<USER>\Desktop\Day Report\database"
    )
    
    if not file_path:
        print("未选择数据库文件，程序将退出。")
        return None
    
    return file_path

def select_excel_file():
    """让用户选择Excel文件"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    file_path = filedialog.askopenfilename(
        title="选择Excel文件",
        filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")],
        initialdir=r"C:\Users\<USER>\Desktop\Day Report\数据库导出"
    )
    
    if not file_path:
        print("未选择Excel文件，程序将退出。")
        return None
    
    return file_path

def import_data_from_excel(db_path, excel_path):
    """从Excel文件导入数据到数据库"""
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 读取Excel文件中的工作表
        print(f"正在读取Excel文件: {excel_path}")
        excel_data = pd.ExcelFile(excel_path)
        sheet_names = excel_data.sheet_names
        
        # 检查是否包含所需的工作表
        required_sheets = ["IOT_Sales", "ZERO_Sales"]
        missing_sheets = [sheet for sheet in required_sheets if sheet not in sheet_names]
        
        if missing_sheets:
            print(f"错误: Excel文件中缺少以下工作表: {', '.join(missing_sheets)}")
            conn.close()
            return False
        
        # 删除数据库中所有与sales相关的数据
        print("正在删除数据库中的sales数据...")
        for table in required_sheets:
            try:
                cursor.execute(f"DELETE FROM {table}")
                print(f"已清空表: {table}")
            except sqlite3.OperationalError:
                # 如果表不存在，创建表
                print(f"表 {table} 不存在，将创建新表")
        
        conn.commit()
        
        # 从Excel导入数据
        for sheet_name in required_sheets:
            print(f"正在处理工作表: {sheet_name}")
            df = pd.read_excel(excel_path, sheet_name=sheet_name)
            
            if df.empty:
                print(f"工作表 {sheet_name} 中没有数据")
                continue
            
            # 检查表是否存在，如果不存在则创建
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{sheet_name}'")
            table_exists = cursor.fetchone() is not None
            
            if not table_exists:
                # 创建表
                columns = []
                for col_name, dtype in zip(df.columns, df.dtypes):
                    if 'int' in str(dtype):
                        col_type = 'INTEGER'
                    elif 'float' in str(dtype):
                        col_type = 'REAL'
                    elif 'datetime' in str(dtype):
                        col_type = 'TEXT'
                    else:
                        col_type = 'TEXT'
                    columns.append(f'"{col_name}" {col_type}')
                
                create_table_sql = f"CREATE TABLE {sheet_name} ({', '.join(columns)})"
                cursor.execute(create_table_sql)
                print(f"已创建表: {sheet_name}")
            
            # 将数据插入到表中
            for _, row in df.iterrows():
                # 处理可能的NaN值
                row_data = row.where(pd.notnull(row), None)
                
                # 构建INSERT语句
                placeholders = ', '.join(['?'] * len(row_data))
                columns_str = ', '.join([f'"{col}"' for col in df.columns])
                insert_sql = f"INSERT INTO {sheet_name} ({columns_str}) VALUES ({placeholders})"
                
                cursor.execute(insert_sql, list(row_data))
            
            print(f"已导入 {len(df)} 条记录到表 {sheet_name}")
        
        conn.commit()
        conn.close()
        
        print("数据导入完成！")
        return True
    
    except Exception as e:
        print(f"导入数据时出错: {str(e)}")
        if 'conn' in locals() and conn:
            conn.close()
        return False

def verify_import(db_path):
    """验证导入的数据"""
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        
        print("\n===== 导入验证 =====")
        
        for table in ["IOT_Sales", "ZERO_Sales"]:
            if table in tables:
                # 查询记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                row_count = cursor.fetchone()[0]
                print(f"表 {table} 中有 {row_count} 条记录")
                
                # 显示一些样例数据
                if row_count > 0:
                    cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                    sample_data = cursor.fetchall()
                    
                    # 获取列名
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = [col[1] for col in cursor.fetchall()]
                    
                    print(f"表 {table} 的样例数据:")
                    for i, row in enumerate(sample_data):
                        print(f"  记录 {i+1}:")
                        for j, value in enumerate(row):
                            print(f"    {columns[j]}: {value}")
            else:
                print(f"表 {table} 不存在")
        
        conn.close()
        return True
    
    except Exception as e:
        print(f"验证导入时出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("===== 数据导入工具 =====")
    print("该工具将从Excel文件导入数据到SQLite数据库")
    print("注意: 将会删除数据库中现有的sales相关数据")
    
    # 让用户选择数据库文件
    db_path = select_database()
    if not db_path:
        exit()
    
    # 让用户选择Excel文件，默认为指定位置的文件
    default_excel_path = r"C:\Users\<USER>\Desktop\Day Report\数据库导出\sales_reports_数据库内容.xlsx"
    if os.path.exists(default_excel_path):
        excel_path = default_excel_path
        print(f"已自动选择Excel文件: {excel_path}")
    else:
        excel_path = select_excel_file()
        if not excel_path:
            exit()
    
    # 确认操作
    root = tk.Tk()
    root.withdraw()
    confirm = messagebox.askyesno(
        "确认操作", 
        f"即将从Excel文件导入数据到数据库，并删除现有sales数据。\n\n"
        f"数据库: {db_path}\n"
        f"Excel文件: {excel_path}\n\n"
        f"是否继续?"
    )
    
    if confirm:
        # 导入数据
        if import_data_from_excel(db_path, excel_path):
            # 验证导入
            verify_import(db_path)
            messagebox.showinfo("完成", "数据已成功导入到数据库！")
        else:
            messagebox.showerror("错误", "导入数据时出错，请查看控制台输出。")
    else:
        print("操作已取消。")