#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试ID管理工具的修复功能
主要测试：
1. 撤回功能（Ctrl+Z）
2. 批量编辑功能（只编辑修改的字段）
"""

import sqlite3
import datetime
import os

# 数据库路径
DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"

def test_database_connection():
    """测试数据库连接"""
    try:
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM Equipment_ID")
            count = cursor.fetchone()[0]
            print(f"✓ 数据库连接成功，当前有 {count} 条记录")
            return True
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False

def test_operation_history_table():
    """检查操作历史功能是否正常"""
    try:
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()
            # 检查Logs表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='Logs'")
            if cursor.fetchone():
                print("✓ 日志表存在，操作历史功能可用")
                
                # 查看最近的日志记录
                cursor.execute("SELECT * FROM Logs ORDER BY ID DESC LIMIT 5")
                logs = cursor.fetchall()
                if logs:
                    print(f"✓ 找到 {len(logs)} 条最近的日志记录")
                    for log in logs:
                        print(f"  - {log[1]}: {log[2]} - {log[3]}")
                else:
                    print("! 暂无日志记录")
                return True
            else:
                print("✗ 日志表不存在")
                return False
    except Exception as e:
        print(f"✗ 检查日志表失败: {e}")
        return False

def create_test_record():
    """创建测试记录"""
    try:
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()
            
            # 创建一个测试记录
            test_serial = f"TEST_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
            cursor.execute("""
                INSERT INTO Equipment_ID 
                (STATE, Location, Quantity, Chair_Serial_No, Sim_Card_Model, Effective_From, Last_Updated)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, ("测试状态", "测试位置", 1, test_serial, "测试型号", 
                  datetime.datetime.now().strftime("%Y-%m-%d"),
                  datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
            
            test_id = cursor.lastrowid
            conn.commit()
            
            print(f"✓ 创建测试记录成功，ID: {test_id}, 序列号: {test_serial}")
            return test_id, test_serial
    except Exception as e:
        print(f"✗ 创建测试记录失败: {e}")
        return None, None

def test_edit_record(record_id):
    """测试编辑记录功能"""
    try:
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()
            
            # 获取编辑前的数据
            cursor.execute("SELECT * FROM Equipment_ID WHERE ID=?", (record_id,))
            before_data = cursor.fetchone()
            
            # 编辑记录
            new_location = f"编辑后位置_{datetime.datetime.now().strftime('%H%M%S')}"
            cursor.execute("""
                UPDATE Equipment_ID 
                SET Location=?, Last_Updated=? 
                WHERE ID=?
            """, (new_location, datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), record_id))
            
            # 获取编辑后的数据
            cursor.execute("SELECT * FROM Equipment_ID WHERE ID=?", (record_id,))
            after_data = cursor.fetchone()
            
            conn.commit()
            
            print(f"✓ 编辑记录成功")
            print(f"  编辑前位置: {before_data[2]}")
            print(f"  编辑后位置: {after_data[2]}")
            
            return before_data, after_data
    except Exception as e:
        print(f"✗ 编辑记录失败: {e}")
        return None, None

def cleanup_test_records():
    """清理测试记录"""
    try:
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM Equipment_ID WHERE Chair_Serial_No LIKE 'TEST_%'")
            deleted_count = cursor.rowcount
            conn.commit()
            print(f"✓ 清理了 {deleted_count} 条测试记录")
    except Exception as e:
        print(f"✗ 清理测试记录失败: {e}")

def main():
    """主测试函数"""
    print("=" * 50)
    print("ID管理工具修复功能测试")
    print("=" * 50)
    
    # 1. 测试数据库连接
    print("\n1. 测试数据库连接...")
    if not test_database_connection():
        return
    
    # 2. 测试操作历史功能
    print("\n2. 测试操作历史功能...")
    if not test_operation_history_table():
        return
    
    # 3. 创建测试记录
    print("\n3. 创建测试记录...")
    test_id, test_serial = create_test_record()
    if not test_id:
        return
    
    # 4. 测试编辑功能
    print("\n4. 测试编辑功能...")
    before_data, after_data = test_edit_record(test_id)
    
    # 5. 清理测试记录
    print("\n5. 清理测试记录...")
    cleanup_test_records()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n修复功能说明：")
    print("1. 撤回功能：按 Ctrl+Z 可以撤回上一个操作（编辑、删除等）")
    print("2. 批量编辑：只会更新用户实际修改的字段，不会覆盖其他字段")
    print("3. 操作历史：所有操作都会记录到数据库，支持撤回")
    print("\n请在程序中测试以下操作：")
    print("- 编辑一条记录，然后按 Ctrl+Z 撤回")
    print("- 删除一条记录，然后按 Ctrl+Z 恢复")
    print("- 批量编辑多条记录，只修改部分字段")
    print("=" * 50)

if __name__ == "__main__":
    main()
