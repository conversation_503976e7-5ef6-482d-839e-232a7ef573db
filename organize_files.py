# -*- coding: utf-8 -*-
"""
文件组织脚本
将所有ID管理工具相关文件复制到专门的文件夹中
"""

import os
import shutil
import sys

def organize_files():
    """组织文件到专门的文件夹"""
    
    # 源目录和目标目录
    source_dir = r"C:\Users\<USER>\Desktop\Day Report"
    target_dir = os.path.join(source_dir, "id管理工具")
    
    # 确保目标目录存在
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
        print(f"✅ 创建目录: {target_dir}")
    
    # 需要复制的文件列表
    files_to_copy = [
        # 主程序文件
        "ID管理工具.py",
        
        # 优化模块文件
        "constants.py",
        "database_connection_manager.py", 
        "error_handler.py",
        "config_manager.py",
        "excel_import_manager.py",
        "database_switcher.py",
        
        # 启动和测试脚本
        "启动ID管理工具.py",
        "简单启动.py",
        "快速启动.py",
        "测试启动.py",
        
        # 演示和测试脚本
        "optimization_demo.py",
        "test_optimization.py",
        "test_database_switch.py",
        "database_switch_demo.py",
        
        # 配置文件
        "db_config.ini",
        
        # 文档文件
        "优化使用说明.md",
    ]
    
    # 复制文件
    copied_files = []
    failed_files = []
    
    for filename in files_to_copy:
        source_path = os.path.join(source_dir, filename)
        target_path = os.path.join(target_dir, filename)
        
        try:
            if os.path.exists(source_path):
                shutil.copy2(source_path, target_path)
                copied_files.append(filename)
                print(f"✅ 复制: {filename}")
            else:
                failed_files.append(f"{filename} (文件不存在)")
                print(f"⚠️ 跳过: {filename} (文件不存在)")
                
        except Exception as e:
            failed_files.append(f"{filename} (错误: {e})")
            print(f"❌ 失败: {filename} - {e}")
    
    # 创建启动脚本
    create_startup_script(target_dir)
    
    # 显示结果
    print("\n" + "=" * 60)
    print("📋 文件组织完成")
    print("=" * 60)
    print(f"✅ 成功复制: {len(copied_files)} 个文件")
    print(f"❌ 失败/跳过: {len(failed_files)} 个文件")
    
    if copied_files:
        print("\n📦 已复制的文件:")
        for file in copied_files:
            print(f"  • {file}")
    
    if failed_files:
        print("\n⚠️ 失败/跳过的文件:")
        for file in failed_files:
            print(f"  • {file}")
    
    print(f"\n📁 目标目录: {target_dir}")
    print("\n🎯 使用方法:")
    print(f"  cd \"{target_dir}\"")
    print("  python ID管理工具.py")
    
    return len(copied_files), len(failed_files)

def create_startup_script(target_dir):
    """创建启动脚本"""
    startup_script = """@echo off
echo 🎯 启动ID管理工具
echo ==================
cd /d "%~dp0"
python ID管理工具.py
pause
"""
    
    script_path = os.path.join(target_dir, "启动.bat")
    try:
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(startup_script)
        print(f"✅ 创建启动脚本: 启动.bat")
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {e}")

def check_imports_in_main_file(target_dir):
    """检查主文件中的导入是否正确"""
    main_file = os.path.join(target_dir, "ID管理工具.py")
    
    if not os.path.exists(main_file):
        print("❌ 主文件不存在，无法检查导入")
        return False
    
    print("\n🔍 检查主文件导入...")
    
    # 需要检查的模块
    required_modules = [
        "constants",
        "database_connection_manager", 
        "error_handler",
        "config_manager",
        "excel_import_manager",
        "database_switcher"
    ]
    
    missing_modules = []
    
    for module in required_modules:
        module_file = os.path.join(target_dir, f"{module}.py")
        if not os.path.exists(module_file):
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ 缺少以下模块文件:")
        for module in missing_modules:
            print(f"  • {module}.py")
        return False
    else:
        print("✅ 所有必需的模块文件都存在")
        return True

def main():
    """主函数"""
    print("🔧 ID管理工具文件组织器")
    print("=" * 60)
    
    try:
        # 组织文件
        copied, failed = organize_files()
        
        # 检查导入
        target_dir = r"C:\Users\<USER>\Desktop\Day Report\id管理工具"
        imports_ok = check_imports_in_main_file(target_dir)
        
        # 总结
        print("\n" + "=" * 60)
        print("📊 组织结果总结")
        print("=" * 60)
        
        if copied > 0 and failed == 0 and imports_ok:
            print("🎉 文件组织完全成功！")
            print("✅ 所有文件已复制到专门文件夹")
            print("✅ 所有必需模块都存在")
            print("✅ 可以正常使用")
        elif copied > 0:
            print("⚠️ 文件组织部分成功")
            print(f"✅ 成功复制 {copied} 个文件")
            if failed > 0:
                print(f"❌ {failed} 个文件复制失败")
            if not imports_ok:
                print("❌ 部分必需模块缺失")
        else:
            print("❌ 文件组织失败")
            print("请检查源文件是否存在")
        
        print(f"\n📁 文件位置: C:\\Users\\<USER>\\Desktop\\Day Report\\id管理工具")
        print("🚀 使用方法: 进入文件夹，运行 ID管理工具.py")
        
    except Exception as e:
        print(f"💥 组织过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
