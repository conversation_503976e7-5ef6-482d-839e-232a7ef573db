import os
import shutil
import sqlite3
import datetime
import pandas as pd
import traceback
from glob import glob

DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
REFUND_DIRS = {
    "IOT": r"C:\Users\<USER>\Desktop\Day Report\Refunding\IOT",
    "ZERO": r"C:\Users\<USER>\Desktop\Day Report\Refunding\ZERO"
}
BACKUP_DIR = r"C:\Users\<USER>\Desktop\Day Report\Refunding\Refunding backup"
SUCCESS_LOG = os.path.join(BACKUP_DIR, "refund_success.log")
FAIL_LOG = os.path.join(BACKUP_DIR, "refund_fail.csv")
NOTE_LOG = os.path.join(BACKUP_DIR, "refund_note.log")

REFUND_LIST_COLUMNS = [
    'Transaction Date','Settlement Date','Refund Date','Merchant Ref ID','Transaction ID','Channel','Order ID','Currency','Billing','Actual','Refund','MDR','GST','Status','Refund Fee','Quantity','Reference1','Reference2','PROCESS'
]


def move_file(self, src, dst):
    # 确保目标目录存在
    os.makedirs(os.path.dirname(dst), exist_ok=True)
    
    # 尝试多次移动文件
    max_attempts = 3
    attempt = 0
    success = False
    
    while attempt < max_attempts and not success:
        try:
            # 如果目标文件已存在，先尝试删除
            if os.path.exists(dst):
                try:
                    os.remove(dst)
                except PermissionError:
                    # 如果无法删除，使用随机后缀创建新文件名
                    import random
                    base, ext = os.path.splitext(dst)
                    dst = f"{base}_{random.randint(1000, 9999)}{ext}"
            
            # 尝试使用shutil.copy2代替move，然后手动删除源文件
            shutil.copy2(src, dst)
            success = True
            
            # 尝试删除源文件，但如果失败也不阻止程序继续
            try:
                os.unlink(src)
            except PermissionError:
                self.log_mgr.log_note(f"警告：无法删除源文件 {src}，但已成功复制到 {dst}")
                
        except PermissionError as e:
            attempt += 1
            self.log_mgr.log_note(f"移动文件尝试 {attempt}/{max_attempts} 失败: {e}")
            # 等待一段时间再重试
            import time
            time.sleep(2)
    
    if not success:
        # 所有尝试都失败，记录错误但不中断程序
        self.log_mgr.log_note(f"警告：无法移动文件 {src} 到 {dst}，将继续处理其他文件")
        return False
    return True
class LogManager:
    def __init__(self, success_log, fail_log, note_log):
        self.success_log = success_log
        self.fail_log = fail_log
        self.note_log = note_log
        # 初始化csv失败日志
        if not os.path.exists(self.fail_log):
            with open(self.fail_log, 'w', encoding='utf-8') as f:
                f.write('Transaction Date,Order ID,Refund,报错原因\n')
    def log_success(self, msg):
        with open(self.success_log, "a", encoding="utf-8") as f:
            f.write(f"{datetime.datetime.now()} {msg}\n")
    def log_fail(self, row, reason):
        with open(self.fail_log, "a", encoding="utf-8") as f:
            f.write(f"{row.get('Transaction Date','')},{row.get('Order ID','')},{row.get('Refund','')},{reason}\n")
    def log_note(self, msg):
        with open(self.note_log, "a", encoding="utf-8") as f:
            f.write(f"{datetime.datetime.now()} {msg}\n")
    def clear_logs(self):
        for log in [self.success_log, self.fail_log, self.note_log]:
            if os.path.exists(log):
                os.remove(log)
    def archive_logs(self):
        today = datetime.datetime.now().strftime('%Y%m')
        for log in [self.success_log, self.fail_log, self.note_log]:
            if os.path.exists(log):
                base, ext = os.path.splitext(log)
                archive_name = f"{base}_{today}{ext}"
                shutil.move(log, archive_name)

class BackupManager:
    def __init__(self, backup_dir):
        self.backup_dir = backup_dir
        os.makedirs(self.backup_dir, exist_ok=True)
    def backup_table(self, table_name):
        backup_file = os.path.join(self.backup_dir, f"{table_name}_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        with sqlite3.connect(DB_PATH) as conn:
            df = pd.read_sql(f"SELECT * FROM {table_name}", conn)
            df.to_excel(backup_file, index=False)
        return backup_file
    def restore_table_from_backup(self, table_name):
        files = [f for f in os.listdir(self.backup_dir) if f.startswith(table_name+"_backup") and f.endswith(".xlsx")]
        if not files:
            print(f"未找到{table_name}的备份文件")
            return
        latest = max(files, key=lambda x: os.path.getmtime(os.path.join(self.backup_dir, x)))
        backup_file = os.path.join(self.backup_dir, latest)
        df = pd.read_excel(backup_file, engine='openpyxl')
        with sqlite3.connect(DB_PATH) as conn:
            conn.execute(f"DELETE FROM {table_name}")
            df.to_sql(table_name, conn, if_exists='append', index=False)
        print(f"{table_name}已恢复自{latest}")
    def archive_db(self):
        today = datetime.datetime.now().strftime('%Y%m')
        db_archive = os.path.join(self.backup_dir, f"sales_reports_{today}.db")
        if os.path.exists(DB_PATH):
            shutil.copy(DB_PATH, db_archive)

class RefundProcessor:
    def __init__(self, log_mgr, backup_mgr):
        self.log_mgr = log_mgr
        self.backup_mgr = backup_mgr
    def ensure_folder(self, folder):
        os.makedirs(folder, exist_ok=True)
    def create_refund_list_table(self):
        with sqlite3.connect(DB_PATH) as conn:
            cur = conn.cursor()
            cur.execute('''CREATE TABLE IF NOT EXISTS REFUND_LIST (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                [Transaction Date] TEXT,
                [Settlement Date] TEXT,
                [Refund Date] TEXT,
                [Merchant Ref ID] TEXT,
                [Transaction ID] TEXT,
                [Channel] TEXT,
                [Order ID] TEXT,
                [Currency] TEXT,
                [Billing] REAL,
                [Actual] REAL,
                [Refund] REAL,
                [MDR] REAL,
                [GST] REAL,
                [Status] TEXT,
                [Refund Fee] REAL,
                [Quantity] INTEGER,
                [Reference1] TEXT,
                [Reference2] TEXT,
                [PROCESS] TEXT
            )''')
            cur.close()
            conn.commit()
    def standardize_date(self, date_str):
        if pd.isna(date_str) or not str(date_str).strip():
            return None
        s = str(date_str).strip()
        try:
            return pd.to_datetime(s).strftime("%Y-%m-%d")
        except Exception:
            return s
    def detect_sheet(self, file_path, platform):
        try:
            xl = pd.ExcelFile(file_path, engine='openpyxl')
            if platform in xl.sheet_names:
                return platform
            # 自动检测第一个sheet
            return xl.sheet_names[0]
        except Exception as e:
            return None
    def process_refund_file(self, platform, file_path):
        table_name = f"{platform}_Sales"
        filename = os.path.basename(file_path)
        sheet_name = self.detect_sheet(file_path, platform)
        if not sheet_name:
            self.log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, f"{filename} 无法检测sheet名")
            return False
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
        except Exception as e:
            self.log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, f"{filename} 读取失败: {e}\n{traceback.format_exc()}")
            return False
        df = df.rename(columns=lambda x: x.strip())
        df['PROCESS'] = ''
        with sqlite3.connect(DB_PATH) as conn:
            db = conn.cursor()
            for idx, row in df.iterrows():
                try:
                    transaction_date = self.standardize_date(row.get('Transaction Date',''))
                    order_id = str(row.get('Order ID','')).strip()
                    try:
                        refund_amt = float(row.get('Refund',0)) if str(row.get('Refund','')).strip() else 0
                    except Exception:
                        refund_amt = 0
                    
                    # 首先尝试精确匹配Transaction Date
                    db.execute(f"SELECT * FROM {table_name} WHERE (Order_time=? OR substr(Order_time,1,10)=?)", (transaction_date, transaction_date))
                    candidates = db.fetchall()
                    
                    # 如果没有找到匹配项，且Transaction Date包含时间信息，则扩大搜索范围到前后三小时
                    if not candidates and transaction_date and len(transaction_date) > 10:
                        try:
                            # 解析完整的日期时间
                            dt = pd.to_datetime(transaction_date)
                            date_only = dt.strftime("%Y-%m-%d")
                            
                            # 扩大搜索范围到当天
                            db.execute(f"SELECT * FROM {table_name} WHERE substr(Order_time,1,10)=?", (date_only,))
                            candidates = db.fetchall()
                        except Exception as e:
                            self.log_mgr.log_note(f"扩大时间搜索范围失败: {e}")
                    
                    # 如果Transaction Date只有日期没有时间，或时间解析失败，则搜索当天所有记录
                    if not candidates and transaction_date:
                        try:
                            # 尝试提取日期部分
                            date_only = transaction_date[:10] if len(transaction_date) >= 10 else transaction_date
                            db.execute(f"SELECT * FROM {table_name} WHERE substr(Order_time,1,10)=?", (date_only,))
                            candidates = db.fetchall()
                        except Exception as e:
                            self.log_mgr.log_note(f"按日期搜索失败: {e}")
                    
                    found = False
                    best_match = None
                    smallest_time_diff = float('inf')
                    exact_amount_match = None
                    
                    for c in candidates:
                        col_names = [d[0] for d in db.description]
                        db_id = str(c[col_names.index('Equipment_ID')]) if 'Equipment_ID' in col_names else ''
                        db_no = str(c[col_names.index('Order_No')]) if 'Order_No' in col_names else ''
                        
                        # 匹配Equipment_ID或Order_No
                        if (len(order_id) <= 9 and order_id == db_id) or (len(order_id) > 9 and order_id == db_no):
                            found = True
                            orig_amt = float(c[col_names.index('Order_price')]) if 'Order_price' in col_names else 0
                            
                            # 如果只有一个匹配项，直接使用
                            if len([x for x in candidates if 
                                   (len(order_id) <= 9 and order_id == str(x[col_names.index('Equipment_ID')])) or 
                                   (len(order_id) > 9 and order_id == str(x[col_names.index('Order_No')]))]) == 1:
                                best_match = c
                                break
                            
                            # 检查金额是否精确匹配
                            if abs(orig_amt - refund_amt) < 0.01:
                                exact_amount_match = c
                            
                            # 计算时间差
                            try:
                                db_time = pd.to_datetime(c[col_names.index('Order_time')])
                                tx_time = pd.to_datetime(transaction_date)
                                time_diff = abs((db_time - tx_time).total_seconds())
                                
                                if time_diff < smallest_time_diff:
                                    smallest_time_diff = time_diff
                                    best_match = c
                            except Exception:
                                # 如果时间比较失败，仍然保留这个候选项
                                if best_match is None:
                                    best_match = c
                    
                    # 优先使用金额精确匹配的记录
                    if exact_amount_match:
                        best_match = exact_amount_match
                    
                    if best_match:
                        col_names = [d[0] for d in db.description]
                        orig_amt = float(best_match[col_names.index('Order_price')]) if 'Order_price' in col_names else 0
                        new_amt = orig_amt - refund_amt
                        
                        self.log_mgr.log_note(f"{filename} 第{idx+1}行: 准备操作表 '{table_name}', rowid: {best_match[0]}, 匹配详情: {best_match}, 订单ID: {order_id}, 原始金额: {orig_amt}, 退款金额: {refund_amt}, 计算后新金额: {new_amt}")

                        try:
                            if new_amt <= 0:
                                cursor = db.execute(f"DELETE FROM {table_name} WHERE rowid=?", (best_match[0],))
                                process_status = '已删除'
                            else:
                                cursor = db.execute(f"UPDATE {table_name} SET Order_price=? WHERE rowid=?", (new_amt, best_match[0]))
                                process_status = '已退款'
                            
                            affected_rows = cursor.rowcount
                            self.log_mgr.log_note(f"{filename} 第{idx+1}行: {process_status} 操作执行完毕，影响行数: {affected_rows}")

                            if affected_rows > 0:
                                row['PROCESS'] = process_status
                                self.log_mgr.log_success(f"{filename} 第{idx+1}行 退款成功，状态:{process_status}")
                                conn.commit()
                                self.log_mgr.log_note(f"{filename} 第{idx+1}行: 事务已提交.")

                                # 提交后验证
                                verify_cursor = db.execute(f"SELECT * FROM {table_name} WHERE rowid=?", (best_match[0],))
                                verify_result = verify_cursor.fetchone()
                                if process_status == '已删除':
                                    if not verify_result:
                                        self.log_mgr.log_note(f"{filename} 第{idx+1}行: 提交后验证删除成功，记录已不存在.")
                                    else:
                                        self.log_mgr.log_fail(row, f"{filename} 第{idx+1}行: 提交后验证删除失败！记录仍然存在: {verify_result}")
                                elif process_status == '已退款':
                                    if verify_result:
                                        updated_price_in_db = verify_result[col_names.index('Order_price')]
                                        if abs(updated_price_in_db - new_amt) < 0.01:
                                            self.log_mgr.log_note(f"{filename} 第{idx+1}行: 提交后验证退款成功. 数据库新价格: {updated_price_in_db}, 预期新价格: {new_amt}")
                                        else:
                                            self.log_mgr.log_fail(row, f"{filename} 第{idx+1}行: 提交后验证退款金额不匹配！数据库新价格: {updated_price_in_db}, 预期新价格: {new_amt}")
                                    else:
                                        self.log_mgr.log_fail(row, f"{filename} 第{idx+1}行: 提交后验证退款失败！记录意外不存在.")
                            else:
                                self.log_mgr.log_fail(row, f"{filename} 第{idx+1}行: {process_status} 操作未影响任何行. 可能rowid不匹配或记录已被修改/删除.")
                                row['PROCESS'] = '操作未生效'
                                # 即使未影响行，也尝试提交，以处理可能的先前未提交的DDL等（虽然此处不太可能）
                                # conn.commit() # 或者选择回滚 conn.rollback()
                                # self.log_mgr.log_note(f"{filename} 第{idx+1}行: 由于未影响行，事务已回滚或按原计划提交.")

                        except sqlite3.Error as e: # 更具体地捕获SQLite错误
                            conn.rollback()
                            self.log_mgr.log_fail(row, f"{filename} 第{idx+1}行 退款操作数据库SQLite错误: {e}\n{traceback.format_exc()}")
                            row['PROCESS'] = '数据库操作失败'
                        except Exception as e:
                            conn.rollback()
                            self.log_mgr.log_fail(row, f"{filename} 第{idx+1}行 退款操作一般错误: {e}\n{traceback.format_exc()}")
                            row['PROCESS'] = '操作失败'
                    else:
                        row['PROCESS'] = '未找到'
                        self.log_mgr.log_fail(row, f"{filename} 第{idx+1}行 未找到匹配数据")
                    
                    insert_row = [row.get(col, '') for col in REFUND_LIST_COLUMNS]
                    db.execute(f"INSERT INTO REFUND_LIST ({','.join(['['+col+']' for col in REFUND_LIST_COLUMNS])}) VALUES ({','.join(['?']*len(REFUND_LIST_COLUMNS))})", insert_row)
                    conn.commit()  # 确保每次插入后提交
                except Exception as e:
                    conn.rollback()  # 如果出错，回滚当前操作
                    row['PROCESS'] = '失败'
                    self.log_mgr.log_fail(row, f"{filename} 第{idx+1}行 处理异常: {e}\n{traceback.format_exc()}")
            db.close()
        return True
    def move_file(self, src, dst):
        # 确保目标目录存在
        os.makedirs(os.path.dirname(dst), exist_ok=True)
        
        # 尝试多次移动文件
        max_attempts = 3
        attempt = 0
        success = False
        
        while attempt < max_attempts and not success:
            try:
                # 如果目标文件已存在，先尝试删除
                if os.path.exists(dst):
                    try:
                        os.remove(dst)
                    except PermissionError:
                        # 如果无法删除，使用随机后缀创建新文件名
                        import random
                        base, ext = os.path.splitext(dst)
                        dst = f"{base}_{random.randint(1000, 9999)}{ext}"
                
                # 尝试使用shutil.copy2代替move，然后手动删除源文件
                shutil.copy2(src, dst)
                success = True
                
                # 尝试删除源文件，但如果失败也不阻止程序继续
                try:
                    os.unlink(src)
                except PermissionError:
                    self.log_mgr.log_note(f"警告：无法删除源文件 {src}，但已成功复制到 {dst}")
                    
            except PermissionError as e:
                attempt += 1
                self.log_mgr.log_note(f"移动文件尝试 {attempt}/{max_attempts} 失败: {e}")
                # 等待一段时间再重试
                import time
                time.sleep(2)
        
        if not success:
            # 所有尝试都失败，记录错误但不中断程序
            self.log_mgr.log_note(f"警告：无法移动文件 {src} 到 {dst}，将继续处理其他文件")
            return False
        return True
    def clean_old_logs(self):
        self.log_mgr.clear_logs()
    def archive_logs_and_db(self):
        self.log_mgr.archive_logs()
        self.backup_mgr.archive_db()
    def compress_and_delete_old_refund_list(self):
        # 按月份导出REFUND_LIST并删除历史数据
        with sqlite3.connect(DB_PATH) as conn:
            now = datetime.datetime.now()
            month_str = now.strftime('%Y-%m')
            df = pd.read_sql(f"SELECT * FROM REFUND_LIST WHERE substr([Transaction Date],1,7)=?", conn, params=(month_str,))
            if not df.empty:
                export_path = os.path.join(BACKUP_DIR, f"REFUND_LIST_{month_str}.xlsx")
                df.to_excel(export_path, index=False)
                conn.execute(f"DELETE FROM REFUND_LIST WHERE substr([Transaction Date],1,7)=?", (month_str,))
                conn.commit()
                self.log_mgr.log_note(f"已归档并删除{month_str}的REFUND_LIST数据")



def show_statistics():
    """显示REFUND_LIST处理统计信息"""
    try:
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT PROCESS, COUNT(*) as count 
                FROM REFUND_LIST 
                GROUP BY PROCESS
            """)
            results = cursor.fetchall()
            
            print("\n=== REFUND_LIST 处理统计 ===")
            total = 0
            for status, count in results:
                print(f"{status}: {count} 条")
                total += count
            print(f"总计: {total} 条")
            print("=" * 30)
    except Exception as e:
        print(f"获取统计信息失败: {e}")

def process_refunds_from_db():
    """处理数据库中未找到的退款记录"""
    log_mgr = LogManager(SUCCESS_LOG, FAIL_LOG, NOTE_LOG)
    backup_mgr = BackupManager(BACKUP_DIR)
    processor = RefundProcessor(log_mgr, backup_mgr)
    
    try:
        # 备份数据库表
        for plat in REFUND_DIRS:
            backup_mgr.backup_table(f"{plat}_Sales")
        backup_mgr.backup_table("REFUND_LIST")
        
        with sqlite3.connect(DB_PATH) as conn:
            conn.execute("PRAGMA foreign_keys = OFF")
            # 启用扩展功能以获取更多错误信息
            conn.execute("PRAGMA recursive_triggers = ON")
            conn.execute("PRAGMA journal_mode = WAL")
            cursor = conn.cursor()
            
            # 获取未找到的记录
            cursor.execute("""
                SELECT rowid, [Transaction Date], [Order ID], Refund 
                FROM REFUND_LIST 
                WHERE PROCESS = '未找到'
            """)
            records = cursor.fetchall()
            
            if not records:
                print("没有需要处理的未找到记录")
                return
            
            print(f"找到 {len(records)} 条未找到的记录，开始处理...")
            
            success_count = 0
            fail_count = 0
            
            for rowid, trans_date, order_id, refund_amount in records:
                try:
                    # 开始事务
                    conn.execute("BEGIN TRANSACTION")
                    
                    # 标准化日期
                    std_date = processor.standardize_date(trans_date)
                    if not std_date:
                        log_mgr.log_fail({'Transaction Date': trans_date, 'Order ID': order_id, 'Refund': refund_amount}, 
                                        "日期格式无效")
                        fail_count += 1
                        conn.rollback()
                        continue
                    
                    # 在IOT_Sales和ZERO_Sales中查找匹配记录
                    found = False
                    for table in ['IOT_Sales', 'ZERO_Sales']:
                        # 精确匹配前先验证记录是否存在
                        cursor.execute(f"""
                            SELECT COUNT(*) FROM {table}
                            WHERE (Equipment_ID = ? OR Order_No = ?) 
                            AND date([Transaction Date]) = date(?)
                        """, (order_id, order_id, std_date))
                        
                        count = cursor.fetchone()[0]
                        if count == 0:
                            log_mgr.log_note(f"在{table}中未找到匹配记录: ID={order_id}, 日期={std_date}")
                            continue  # 如果没有匹配记录，尝试下一个表
                        
                        # 精确匹配
                        cursor.execute(f"""
                            SELECT rowid, Order_price FROM {table}
                            WHERE (Equipment_ID = ? OR Order_No = ?) 
                            AND date([Transaction Date]) = date(?)
                        """, (order_id, order_id, std_date))
                        
                        matches = cursor.fetchall()
                        
                        if matches:
                            # 如果有多个匹配，选择金额最接近的
                            best_match = min(matches, key=lambda x: abs(float(x[1]) - float(refund_amount)))
                            match_rowid, current_price = best_match
                            
                            # 再次验证记录是否存在（防止并发操作）
                            cursor.execute(f"SELECT rowid FROM {table} WHERE rowid = ?", (match_rowid,))
                            if not cursor.fetchone():
                                log_mgr.log_fail({'Transaction Date': trans_date, 'Order ID': order_id, 'Refund': refund_amount}, 
                                                f"在{table}中找到匹配记录，但在操作前记录已被删除或修改")
                                continue
                            
                            new_amount = float(current_price) - float(refund_amount)
                            
                            try:
                                if new_amount <= 0.01:
                                    # 删除记录
                                    cursor.execute(f"DELETE FROM {table} WHERE rowid = ?", (match_rowid,))
                                    affected_rows = cursor.rowcount
                                    
                                    if affected_rows > 0:
                                        # 验证删除
                                        cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE rowid = ?", (match_rowid,))
                                        if cursor.fetchone()[0] == 0:
                                            cursor.execute("UPDATE REFUND_LIST SET PROCESS = '已删除' WHERE rowid = ?", (rowid,))
                                            conn.commit()
                                            log_mgr.log_success({'Transaction Date': trans_date, 'Order ID': order_id, 'Refund': refund_amount}, 
                                                               f"从{table}删除记录，原价格: {current_price}")
                                            success_count += 1
                                            found = True
                                            break
                                        else:
                                            log_mgr.log_fail({'Transaction Date': trans_date, 'Order ID': order_id, 'Refund': refund_amount}, 
                                                            f"从{table}删除记录失败，验证显示记录仍然存在")
                                            conn.rollback()
                                    else:
                                        log_mgr.log_fail({'Transaction Date': trans_date, 'Order ID': order_id, 'Refund': refund_amount}, 
                                                        f"从{table}删除记录失败，操作未影响任何行，rowid: {match_rowid}")
                                        conn.rollback()
                                else:
                                    # 更新价格
                                    cursor.execute(f"UPDATE {table} SET Order_price = ? WHERE rowid = ?", (new_amount, match_rowid))
                                    affected_rows = cursor.rowcount
                                    
                                    if affected_rows > 0:
                                        # 验证更新
                                        cursor.execute(f"SELECT Order_price FROM {table} WHERE rowid = ?", (match_rowid,))
                                        result = cursor.fetchone()
                                        if result and abs(float(result[0]) - new_amount) < 0.01:
                                            cursor.execute("UPDATE REFUND_LIST SET PROCESS = '已退款' WHERE rowid = ?", (rowid,))
                                            conn.commit()
                                            log_mgr.log_success({'Transaction Date': trans_date, 'Order ID': order_id, 'Refund': refund_amount}, 
                                                               f"在{table}更新价格，原价格: {current_price}, 新价格: {new_amount}")
                                            success_count += 1
                                            found = True
                                            break
                                        else:
                                            log_mgr.log_fail({'Transaction Date': trans_date, 'Order ID': order_id, 'Refund': refund_amount}, 
                                                            f"在{table}更新价格后验证失败，期望价格: {new_amount}, 实际价格: {result[0] if result else 'None'}")
                                            conn.rollback()
                                    else:
                                        # 如果更新失败，记录详细信息
                                        log_mgr.log_fail({'Transaction Date': trans_date, 'Order ID': order_id, 'Refund': refund_amount}, 
                                                        f"在{table}更新价格失败，操作未影响任何行，rowid: {match_rowid}, 原价格: {current_price}, 尝试新价格: {new_amount}")
                                        conn.rollback()
                            except sqlite3.Error as e:
                                log_mgr.log_fail({'Transaction Date': trans_date, 'Order ID': order_id, 'Refund': refund_amount}, 
                                                f"在{table}操作时发生SQLite错误: {e}")
                                conn.rollback()
                    
                    if not found:
                        log_mgr.log_fail({'Transaction Date': trans_date, 'Order ID': order_id, 'Refund': refund_amount}, 
                                        "在IOT_Sales和ZERO_Sales中均未找到匹配记录或操作失败")
                        fail_count += 1
                        conn.rollback()  # 如果未找到匹配记录，回滚事务
                        continue
                except Exception as e:
                    log_mgr.log_fail({'Transaction Date': trans_date, 'Order ID': order_id, 'Refund': refund_amount}, 
                                    f"处理异常: {e}")
                    fail_count += 1
                    conn.rollback()
                    log_mgr.log_note(f"回滚事务: rowid={rowid}, order_id={order_id}, 异常: {e}\n{traceback.format_exc()}")
            
            print(f"\n处理完成！成功: {success_count}, 失败: {fail_count}")
            
            # 处理完成后，显示详细统计信息
            cursor.execute("""
                SELECT PROCESS, COUNT(*) as count 
                FROM REFUND_LIST 
                GROUP BY PROCESS
            """)
            results = cursor.fetchall()
            
            print("\n=== 处理结果统计 ===")
            for status, count in results:
                print(f"{status}: {count} 条")
            print("=" * 20)
            
    except Exception as e:
        print(f"处理数据库退款记录失败: {e}")
        log_mgr.log_note(f"处理数据库退款记录异常: {e}\n{traceback.format_exc()}")
        try:
            conn.rollback()
            log_mgr.log_note("已回滚所有未完成的事务")
        except Exception as rollback_error:
            log_mgr.log_note(f"回滚事务失败: {rollback_error}")

def main():
    log_mgr = LogManager(SUCCESS_LOG, FAIL_LOG, NOTE_LOG)
    backup_mgr = BackupManager(BACKUP_DIR)
    processor = RefundProcessor(log_mgr, backup_mgr)
    
    while True:
        print("\n=== 退款处理系统 ===")
        print("1. 处理Excel退款文件")
        print("2. 处理数据库中未找到的记录")
        print("4. 恢复数据库备份")
        print("5. 管理备份文件")
        print("6. 查看处理统计")
        print("7. 清理日志")
        print("8. 归档日志和数据库")
        print("9. 按月归档REFUND_LIST")
        print("0. 退出")
        print("=" * 25)
        
        try:
            choice = input("请选择操作 (0-9): ").strip()
        except Exception as e:
            print(f"输入异常: {e}")
            continue
        
        try:
            if choice == '0':
                print("退出系统")
                break
            elif choice == '1':
                # 处理Excel退款文件
                processor.create_refund_list_table()
                for plat, folder in REFUND_DIRS.items():
                    table_name = f"{plat}_Sales"
                    backup_mgr.backup_table(table_name)
                    processor.ensure_folder(os.path.join(folder, "已处理"))
                    processor.ensure_folder(os.path.join(folder, "需人工检查"))
                    files = [f for f in os.listdir(folder) if f.lower().endswith(('.xls','.xlsx'))]
                    total = len(files)
                    
                    if total == 0:
                        print(f"{plat}: 没有找到需要处理的Excel文件")
                        continue
                        
                    print(f"{plat}: 找到 {total} 个文件需要处理")
                    
                    for idx, fname in enumerate(files):
                        fpath = os.path.join(folder, fname)
                        try:
                            ok = processor.process_refund_file(plat, fpath)
                            target_dir = "已处理" if ok else "需人工检查"
                            dst = os.path.join(folder, target_dir, fname)
                            move_success = processor.move_file(fpath, dst)
                            if not move_success:
                                log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, 
                                                f"{fname} 文件移动失败，可能被其他程序占用，请手动移动到{target_dir}文件夹")
                            print(f"{plat}：正在处理第{idx+1}/{total}个文件：{fname}")
                        except Exception as e:
                            log_mgr.log_fail({'Transaction Date':'','Order ID':'','Refund':''}, f"{fname} 主流程异常: {e}\n{traceback.format_exc()}")
                            try:
                                dst = os.path.join(folder, "需人工检查", fname)
                                processor.move_file(fpath, dst)
                            except Exception:
                                pass
                backup_mgr.backup_table("REFUND_LIST")
                print("Excel文件处理完成！")
                
            elif choice == '2':
                # 处理数据库中未找到的记录
                process_refunds_from_db()
                
            elif choice == '3':
                # 清空处理状态
                if input("确认清空所有已处理记录的状态？(y/N): ").lower() == 'y':
                    clear_process_status()
                else:
                    print("操作已取消")
                    
            elif choice == '4':
                # 恢复数据库备份
                print("\n选择要恢复的表:")
                print("1. IOT_Sales")
                print("2. ZERO_Sales")
                print("3. REFUND_LIST")
                print("4. 全部表")
                
                restore_choice = input("请选择 (1-4): ").strip()
                
                if restore_choice == '1':
                    backup_mgr.restore_table_from_backup("IOT_Sales")
                elif restore_choice == '2':
                    backup_mgr.restore_table_from_backup("ZERO_Sales")
                elif restore_choice == '3':
                    backup_mgr.restore_table_from_backup("REFUND_LIST")
                elif restore_choice == '4':
                    for plat in REFUND_DIRS:
                        backup_mgr.restore_table_from_backup(f"{plat}_Sales")
                    backup_mgr.restore_table_from_backup("REFUND_LIST")
                else:
                    print("无效选择")
                    
            elif choice == '5':
                # 管理备份文件
                print("\n=== 备份文件管理 ===")
                print("1. 查看备份列表")
                print("2. 恢复指定备份")
                
                backup_choice = input("请选择 (1-2): ").strip()
                
                if backup_choice == '1':
                    backup_mgr.list_backups()
                elif backup_choice == '2':
                    table_name = input("请输入表名 (IOT_Sales/ZERO_Sales/REFUND_LIST): ").strip()
                    if table_name in ['IOT_Sales', 'ZERO_Sales', 'REFUND_LIST']:
                        backup_mgr.restore_table_from_backup(table_name)
                    else:
                        print("无效的表名")
                        
            elif choice == '6':
                # 查看处理统计
                show_statistics()
                
            elif choice == '7':
                # 清理日志
                processor.clean_old_logs()
                print("日志已清理")
                
            elif choice == '8':
                # 归档日志和数据库
                processor.archive_logs_and_db()
                print("日志和数据库已归档")
                
            elif choice == '9':
                # 按月归档REFUND_LIST
                processor.compress_and_delete_old_refund_list()
                print("REFUND_LIST已按月归档并删除")
                
            else:
                print("无效输入，请重新选择")
                
        except Exception as e:
            log_mgr.log_note(f"操作异常: {e}\n{traceback.format_exc()}")
            print(f"操作异常: {e}")
if __name__ == "__main__":
    main()