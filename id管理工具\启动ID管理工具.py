# -*- coding: utf-8 -*-
"""
ID管理工具启动脚本
确保程序能正常启动，处理各种依赖问题
"""

import sys
import os
import traceback

def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    required_modules = [
        'tkinter',
        'sqlite3', 
        'pandas',
        'datetime',
        'configparser',
        'threading',
        'time',
        'queue',
        'concurrent.futures'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"  ❌ {module}")
    
    if missing_modules:
        print(f"\n⚠️ 缺少以下模块: {', '.join(missing_modules)}")
        print("请安装缺少的模块后重试")
        return False
    
    print("✅ 所有依赖项检查通过")
    return True

def check_optimization_modules():
    """检查优化模块"""
    print("\n🚀 检查优化模块...")
    
    optimization_modules = [
        'constants',
        'database_connection_manager',
        'error_handler',
        'config_manager',
        'excel_import_manager'
    ]
    
    available_modules = []
    
    for module in optimization_modules:
        try:
            __import__(module)
            available_modules.append(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ⚠️ {module} - {e}")
    
    if available_modules:
        print(f"✅ 优化模块可用: {len(available_modules)}/{len(optimization_modules)}")
        return True
    else:
        print("ℹ️ 优化模块不可用，将使用原始功能")
        return False

def check_database():
    """检查数据库"""
    print("\n💾 检查数据库...")
    
    # 默认数据库路径
    db_path = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"
    
    # 检查数据库目录
    db_dir = os.path.dirname(db_path)
    if not os.path.exists(db_dir):
        print(f"  📁 创建数据库目录: {db_dir}")
        try:
            os.makedirs(db_dir, exist_ok=True)
            print("  ✅ 数据库目录创建成功")
        except Exception as e:
            print(f"  ❌ 创建数据库目录失败: {e}")
            return False
    else:
        print(f"  ✅ 数据库目录存在: {db_dir}")
    
    # 检查数据库文件
    if os.path.exists(db_path):
        print(f"  ✅ 数据库文件存在: {db_path}")
    else:
        print(f"  ℹ️ 数据库文件不存在，将在首次运行时创建")
    
    return True

def start_application():
    """启动应用程序"""
    print("\n🚀 启动ID管理工具...")
    
    try:
        # 导入主程序
        print("  📦 导入主程序模块...")
        import ID管理工具
        
        print("  🎯 初始化应用程序...")
        # 启动主程序
        ID管理工具.main()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n📋 错误详情:")
        traceback.print_exc()
        return False
    
    return True

def show_startup_info():
    """显示启动信息"""
    print("=" * 60)
    print("🎯 ID管理工具 - 优化版")
    print("=" * 60)
    print("版本: 2.0 (模块化架构)")
    print("作者: AI Assistant")
    print("日期: 2024")
    print("=" * 60)

def show_help():
    """显示帮助信息"""
    print("\n📚 使用说明:")
    print("1. 直接运行此脚本启动ID管理工具")
    print("2. 如果优化模块可用，将自动启用新功能")
    print("3. 如果优化模块不可用，将使用原始功能")
    print("4. 数据库文件将在首次运行时自动创建")
    print("\n🔧 故障排除:")
    print("- 如果启动失败，请检查Python版本 (建议3.7+)")
    print("- 确保所有依赖模块已安装")
    print("- 检查数据库目录权限")
    print("- 查看错误详情进行诊断")

def main():
    """主函数"""
    show_startup_info()
    
    # 检查系统环境
    print(f"🐍 Python版本: {sys.version}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 执行检查
    checks = [
        ("基础依赖", check_dependencies),
        ("优化模块", check_optimization_modules), 
        ("数据库环境", check_database)
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name}检查失败: {e}")
            all_passed = False
    
    if not all_passed:
        print("\n⚠️ 部分检查未通过，但程序仍可能正常运行")
        response = input("是否继续启动? (y/n): ")
        if response.lower() != 'y':
            print("👋 启动已取消")
            return
    
    # 启动应用程序
    if start_application():
        print("✅ 应用程序启动成功")
    else:
        print("❌ 应用程序启动失败")
        show_help()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n💥 意外错误: {e}")
        traceback.print_exc()
        show_help()
    finally:
        input("\n按回车键退出...")
