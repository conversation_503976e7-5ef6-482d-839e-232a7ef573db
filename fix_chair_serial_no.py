import sqlite3
import re

DB_PATH = r"C:\Users\<USER>\Desktop\Day Report\database\sales_reports.db"

def fix_chair_serial_no():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    # 查询所有含.0的Equipment_ID
    cursor.execute("""
        SELECT rowid, Equipment_ID FROM IOT_Sales
        WHERE Equipment_ID LIKE '_________.0'
    """)
    rows = cursor.fetchall()
    pattern = re.compile(r'^(\d{9})\.0$')
    count = 0
    for rowid, serial in rows:
        m = pattern.match(str(serial))
        if m:
            new_serial = m.group(1)
            cursor.execute(
                "UPDATE IOT_Sales SET Equipment_ID = ? WHERE rowid = ?",
                (new_serial, rowid)
            )
            count += 1
    conn.commit()
    conn.close()
    print(f"已修正 {count} 条Equipment_ID 字段。")

if __name__ == "__main__":
    fix_chair_serial_no()