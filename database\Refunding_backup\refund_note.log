2025-05-28 10:40:38.848082 信息: Equipment_ID 603011216 使用 Transaction Date '2025-05-18' (含时间) 进行精确匹配。
2025-05-28 10:57:22.122162 信息: Equipment_ID 603010757 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 10:57:22.193979 信息: Equipment_ID 603011008 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 10:57:22.246122 信息: Equipment_ID 603010751 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 11:27:20.010217 信息: Equipment_ID 603011216 使用 Transaction Date '2025-05-18' (含时间) 进行精确匹配。
2025-05-28 11:27:20.073768 temp_refund_20250528112613.xlsx 第1行 删除成功验证: 记录已不存在
2025-05-28 11:30:12.930877 信息: Equipment_ID 603010757 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 11:30:12.976494 temp_refund_20250528112858.xlsx 第1行 删除成功验证: 记录已不存在
2025-05-28 11:30:12.986019 信息: Equipment_ID 603011008 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 11:30:13.033121 temp_refund_20250528112858.xlsx 第2行 删除成功验证: 记录已不存在
2025-05-28 11:30:13.107165 信息: Equipment_ID 603010751 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 11:30:13.158450 temp_refund_20250528112858.xlsx 第3行 删除成功验证: 记录已不存在
2025-05-28 11:32:32.522735 信息: Equipment_ID 603011171 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 11:32:32.572935 temp_refund_20250528113126.xlsx 第1行 删除成功验证: 记录已不存在
2025-05-28 11:32:32.592550 信息: Equipment_ID 603011203 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 11:32:32.650048 temp_refund_20250528113126.xlsx 第2行 删除成功验证: 记录已不存在
2025-05-28 11:32:32.658780 信息: Equipment_ID 603010434 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 11:32:32.702688 temp_refund_20250528113126.xlsx 第3行 删除成功验证: 记录已不存在
2025-05-28 11:32:32.709992 信息: Equipment_ID 603011110 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 11:32:32.758616 temp_refund_20250528113126.xlsx 第4行 删除成功验证: 记录已不存在
2025-05-28 11:32:32.766141 信息: Equipment_ID 603011237 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 11:32:32.818061 temp_refund_20250528113126.xlsx 第5行 删除成功验证: 记录已不存在
2025-05-28 11:32:32.825784 信息: Equipment_ID 603010276 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 11:32:32.872835 temp_refund_20250528113126.xlsx 第6行 删除成功验证: 记录已不存在
2025-05-28 11:32:32.880383 信息: Equipment_ID 603010276 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 11:32:32.922334 temp_refund_20250528113126.xlsx 第7行 删除成功验证: 记录已不存在
2025-05-28 11:32:32.929522 信息: Equipment_ID 603010876 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 11:32:32.980348 temp_refund_20250528113126.xlsx 第8行 删除成功验证: 记录已不存在
2025-05-28 11:32:32.987942 信息: Equipment_ID 603010434 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 11:32:33.028809 temp_refund_20250528113126.xlsx 第9行 删除成功验证: 记录已不存在
2025-05-28 11:32:33.035544 信息: Equipment_ID 603011110 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 11:32:33.078195 temp_refund_20250528113126.xlsx 第10行 删除成功验证: 记录已不存在
2025-05-28 11:32:33.085671 信息: Order No. 2025052019381924791955388690432 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 11:32:33.130371 temp_refund_20250528113126.xlsx 第11行 删除成功验证: 记录已不存在
2025-05-28 11:47:53.761518 信息: Equipment_ID 603010757 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 11:47:53.805850 temp_refund_20250528114648.xlsx 第1行 删除成功验证: 记录已不存在
2025-05-28 11:47:53.806141 temp_refund_20250528114648.xlsx 第1行 事务已提交，状态: 已删除
2025-05-28 11:47:53.815178 temp_refund_20250528114648.xlsx 第1行 REFUND_LIST插入记录已提交
2025-05-28 11:47:53.816029 信息: Equipment_ID 603011008 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 11:47:53.862218 temp_refund_20250528114648.xlsx 第2行 删除成功验证: 记录已不存在
2025-05-28 11:47:53.862495 temp_refund_20250528114648.xlsx 第2行 事务已提交，状态: 已删除
2025-05-28 11:47:53.870954 temp_refund_20250528114648.xlsx 第2行 REFUND_LIST插入记录已提交
2025-05-28 11:47:53.872303 信息: Equipment_ID 603010751 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 11:47:53.917431 temp_refund_20250528114648.xlsx 第3行 删除成功验证: 记录已不存在
2025-05-28 11:47:53.917684 temp_refund_20250528114648.xlsx 第3行 事务已提交，状态: 已删除
2025-05-28 11:47:53.925392 temp_refund_20250528114648.xlsx 第3行 REFUND_LIST插入记录已提交
2025-05-28 12:02:17.643970 信息: Equipment_ID 603010757 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 12:02:17.707144 temp_refund_20250528120107.xlsx 第1行 删除成功验证: 记录已不存在
2025-05-28 12:02:17.707408 temp_refund_20250528120107.xlsx 第1行 事务已提交，状态: 已删除
2025-05-28 12:02:17.707732 temp_refund_20250528120107.xlsx 第1行 验证成功：记录已从数据库中移除
2025-05-28 12:02:17.724211 temp_refund_20250528120107.xlsx 第1行 REFUND_LIST插入记录已提交
2025-05-28 12:02:17.725713 temp_refund_20250528120107.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:02:17.731049 信息: Equipment_ID 603011008 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 12:02:17.789232 temp_refund_20250528120107.xlsx 第2行 删除成功验证: 记录已不存在
2025-05-28 12:02:17.789627 temp_refund_20250528120107.xlsx 第2行 事务已提交，状态: 已删除
2025-05-28 12:02:17.789980 temp_refund_20250528120107.xlsx 第2行 验证成功：记录已从数据库中移除
2025-05-28 12:02:17.801932 temp_refund_20250528120107.xlsx 第2行 REFUND_LIST插入记录已提交
2025-05-28 12:02:17.802747 temp_refund_20250528120107.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:02:17.803866 信息: Equipment_ID 603010751 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 12:02:17.872650 temp_refund_20250528120107.xlsx 第3行 删除成功验证: 记录已不存在
2025-05-28 12:02:17.872912 temp_refund_20250528120107.xlsx 第3行 事务已提交，状态: 已删除
2025-05-28 12:02:17.873118 temp_refund_20250528120107.xlsx 第3行 验证成功：记录已从数据库中移除
2025-05-28 12:02:17.879571 temp_refund_20250528120107.xlsx 第3行 REFUND_LIST插入记录已提交
2025-05-28 12:02:17.879933 temp_refund_20250528120107.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:23:26.444240 信息: Equipment_ID 603010757 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 12:23:26.515563 temp_refund_20250528122218.xlsx 第1行 删除成功验证: 记录已不存在
2025-05-28 12:23:26.515894 temp_refund_20250528122218.xlsx 第1行 事务已提交，状态: 已删除
2025-05-28 12:23:26.516185 temp_refund_20250528122218.xlsx 第1行 验证成功：记录已从数据库中移除
2025-05-28 12:23:26.526109 temp_refund_20250528122218.xlsx 第1行 REFUND_LIST插入记录已提交
2025-05-28 12:23:26.526595 temp_refund_20250528122218.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:23:26.527705 信息: Equipment_ID 603011008 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 12:23:26.580980 temp_refund_20250528122218.xlsx 第2行 删除成功验证: 记录已不存在
2025-05-28 12:23:26.581272 temp_refund_20250528122218.xlsx 第2行 事务已提交，状态: 已删除
2025-05-28 12:23:26.581501 temp_refund_20250528122218.xlsx 第2行 验证成功：记录已从数据库中移除
2025-05-28 12:23:26.587873 temp_refund_20250528122218.xlsx 第2行 REFUND_LIST插入记录已提交
2025-05-28 12:23:26.588336 temp_refund_20250528122218.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:23:26.589202 信息: Equipment_ID 603010751 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 12:23:26.642672 temp_refund_20250528122218.xlsx 第3行 删除成功验证: 记录已不存在
2025-05-28 12:23:26.643011 temp_refund_20250528122218.xlsx 第3行 事务已提交，状态: 已删除
2025-05-28 12:23:26.643306 temp_refund_20250528122218.xlsx 第3行 验证成功：记录已从数据库中移除
2025-05-28 12:23:26.650032 temp_refund_20250528122218.xlsx 第3行 REFUND_LIST插入记录已提交
2025-05-28 12:23:26.650465 temp_refund_20250528122218.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:31:11.648240 信息: Equipment_ID 603010757 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 12:31:11.750693 数据库验证: 删除前后 IOT_Sales 表总金额为 2076114.15
2025-05-28 12:31:11.805517 数据库验证: 删除后后 IOT_Sales 表总金额为 2076114.15
2025-05-28 12:31:11.806022 数据库验证: 预期金额变化 1.0, 实际金额变化 0.0
2025-05-28 12:31:11.806217 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:31:11.847127 temp_refund_20250528123006.xlsx 第1行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:31:11.904323 temp_refund_20250528123006.xlsx 第1行 尝试通过Order_No=2025051911091924301420400013312删除记录
2025-05-28 12:31:11.994273 temp_refund_20250528123006.xlsx 第1行 最终删除验证通过: 记录已成功删除
2025-05-28 12:31:11.994497 temp_refund_20250528123006.xlsx 第1行 事务已提交，状态: 已删除
2025-05-28 12:31:11.994716 temp_refund_20250528123006.xlsx 第1行 验证成功：记录已从数据库中移除
2025-05-28 12:31:12.001456 temp_refund_20250528123006.xlsx 第1行 REFUND_LIST插入记录已提交
2025-05-28 12:31:12.001848 temp_refund_20250528123006.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:31:12.002601 信息: Equipment_ID 603011008 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 12:31:12.089704 数据库验证: 删除前后 IOT_Sales 表总金额为 2076113.15
2025-05-28 12:31:12.134722 数据库验证: 删除后后 IOT_Sales 表总金额为 2076113.15
2025-05-28 12:31:12.134946 数据库验证: 预期金额变化 1.0, 实际金额变化 0.0
2025-05-28 12:31:12.135227 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:31:12.178371 temp_refund_20250528123006.xlsx 第2行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:31:12.224885 temp_refund_20250528123006.xlsx 第2行 尝试通过Order_No=2025051912091924316358401126400删除记录
2025-05-28 12:31:12.310722 temp_refund_20250528123006.xlsx 第2行 最终删除验证通过: 记录已成功删除
2025-05-28 12:31:12.310941 temp_refund_20250528123006.xlsx 第2行 事务已提交，状态: 已删除
2025-05-28 12:31:12.311230 temp_refund_20250528123006.xlsx 第2行 验证成功：记录已从数据库中移除
2025-05-28 12:31:12.318430 temp_refund_20250528123006.xlsx 第2行 REFUND_LIST插入记录已提交
2025-05-28 12:31:12.318774 temp_refund_20250528123006.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:31:12.319573 信息: Equipment_ID 603010751 使用 Transaction Date '2025-05-19' (含时间) 进行精确匹配。
2025-05-28 12:31:12.419169 数据库验证: 删除前后 IOT_Sales 表总金额为 2076112.15
2025-05-28 12:31:12.467917 数据库验证: 删除后后 IOT_Sales 表总金额为 2076112.15
2025-05-28 12:31:12.468141 数据库验证: 预期金额变化 1.0, 实际金额变化 0.0
2025-05-28 12:31:12.468362 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:31:12.511485 temp_refund_20250528123006.xlsx 第3行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:31:12.571990 temp_refund_20250528123006.xlsx 第3行 尝试通过Order_No=2025051912171924318458497527808删除记录
2025-05-28 12:31:12.661628 temp_refund_20250528123006.xlsx 第3行 最终删除验证通过: 记录已成功删除
2025-05-28 12:31:12.661836 temp_refund_20250528123006.xlsx 第3行 事务已提交，状态: 已删除
2025-05-28 12:31:12.662105 temp_refund_20250528123006.xlsx 第3行 验证成功：记录已从数据库中移除
2025-05-28 12:31:12.668808 temp_refund_20250528123006.xlsx 第3行 REFUND_LIST插入记录已提交
2025-05-28 12:31:12.669335 temp_refund_20250528123006.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:33:04.048751 信息: Equipment_ID 603011171 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 12:33:04.131264 数据库验证: 删除前后 IOT_Sales 表总金额为 2076111.15
2025-05-28 12:33:04.170159 数据库验证: 删除后后 IOT_Sales 表总金额为 2076111.15
2025-05-28 12:33:04.170372 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-28 12:33:04.170598 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:33:04.216341 temp_refund_20250528123159.xlsx 第1行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:33:04.273947 temp_refund_20250528123159.xlsx 第1行 尝试通过Order_No=2025052021011924812842729009152删除记录
2025-05-28 12:33:04.377543 temp_refund_20250528123159.xlsx 第1行 严重警告: 多次尝试后记录仍未被删除！
2025-05-28 12:33:04.377767 temp_refund_20250528123159.xlsx 第1行 事务已提交，状态: 已删除
2025-05-28 12:33:04.378067 temp_refund_20250528123159.xlsx 第1行 验证成功：记录已从数据库中移除
2025-05-28 12:33:04.385064 temp_refund_20250528123159.xlsx 第1行 REFUND_LIST插入记录已提交
2025-05-28 12:33:04.386195 temp_refund_20250528123159.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:33:04.387417 信息: Equipment_ID 603011203 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 12:33:04.482001 数据库验证: 删除前后 IOT_Sales 表总金额为 2076106.15
2025-05-28 12:33:04.525258 数据库验证: 删除后后 IOT_Sales 表总金额为 2076106.15
2025-05-28 12:33:04.525719 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-28 12:33:04.525942 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:33:04.564479 temp_refund_20250528123159.xlsx 第2行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:33:04.608721 temp_refund_20250528123159.xlsx 第2行 尝试通过Order_No=2025052020531924810617503936512删除记录
2025-05-28 12:33:04.691412 temp_refund_20250528123159.xlsx 第2行 严重警告: 多次尝试后记录仍未被删除！
2025-05-28 12:33:04.691668 temp_refund_20250528123159.xlsx 第2行 事务已提交，状态: 已删除
2025-05-28 12:33:04.691886 temp_refund_20250528123159.xlsx 第2行 验证成功：记录已从数据库中移除
2025-05-28 12:33:04.698869 temp_refund_20250528123159.xlsx 第2行 REFUND_LIST插入记录已提交
2025-05-28 12:33:04.699329 temp_refund_20250528123159.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:33:04.700166 信息: Equipment_ID 603010434 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 12:33:04.796583 数据库验证: 删除前后 IOT_Sales 表总金额为 2076101.15
2025-05-28 12:33:04.844703 数据库验证: 删除后后 IOT_Sales 表总金额为 2076101.15
2025-05-28 12:33:04.845082 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-28 12:33:04.845465 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:33:04.892665 temp_refund_20250528123159.xlsx 第3行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:33:04.947751 temp_refund_20250528123159.xlsx 第3行 尝试通过Order_No=2025052020201924802325813719040删除记录
2025-05-28 12:33:05.043692 temp_refund_20250528123159.xlsx 第3行 严重警告: 多次尝试后记录仍未被删除！
2025-05-28 12:33:05.044006 temp_refund_20250528123159.xlsx 第3行 事务已提交，状态: 已删除
2025-05-28 12:33:05.044295 temp_refund_20250528123159.xlsx 第3行 验证成功：记录已从数据库中移除
2025-05-28 12:33:05.051798 temp_refund_20250528123159.xlsx 第3行 REFUND_LIST插入记录已提交
2025-05-28 12:33:05.052199 temp_refund_20250528123159.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:33:05.053094 信息: Equipment_ID 603011110 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 12:33:05.160760 数据库验证: 删除前后 IOT_Sales 表总金额为 2076096.15
2025-05-28 12:33:05.209602 数据库验证: 删除后后 IOT_Sales 表总金额为 2076096.15
2025-05-28 12:33:05.209843 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-28 12:33:05.210118 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:33:05.256770 temp_refund_20250528123159.xlsx 第4行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:33:05.314593 temp_refund_20250528123159.xlsx 第4行 尝试通过Order_No=2025052020011924797576494247936删除记录
2025-05-28 12:33:05.406939 temp_refund_20250528123159.xlsx 第4行 严重警告: 多次尝试后记录仍未被删除！
2025-05-28 12:33:05.407196 temp_refund_20250528123159.xlsx 第4行 事务已提交，状态: 已删除
2025-05-28 12:33:05.407498 temp_refund_20250528123159.xlsx 第4行 验证成功：记录已从数据库中移除
2025-05-28 12:33:05.414019 temp_refund_20250528123159.xlsx 第4行 REFUND_LIST插入记录已提交
2025-05-28 12:33:05.414437 temp_refund_20250528123159.xlsx 第4行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:33:05.415305 信息: Equipment_ID 603011237 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 12:33:05.509936 数据库验证: 删除前后 IOT_Sales 表总金额为 2076091.15
2025-05-28 12:33:05.552885 数据库验证: 删除后后 IOT_Sales 表总金额为 2076091.15
2025-05-28 12:33:05.553130 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-28 12:33:05.553370 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:33:05.598480 temp_refund_20250528123159.xlsx 第5行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:33:05.656175 temp_refund_20250528123159.xlsx 第5行 尝试通过Order_No=2025052019531924795629619965952删除记录
2025-05-28 12:33:05.739695 temp_refund_20250528123159.xlsx 第5行 严重警告: 多次尝试后记录仍未被删除！
2025-05-28 12:33:05.739910 temp_refund_20250528123159.xlsx 第5行 事务已提交，状态: 已删除
2025-05-28 12:33:05.740202 temp_refund_20250528123159.xlsx 第5行 验证成功：记录已从数据库中移除
2025-05-28 12:33:05.746404 temp_refund_20250528123159.xlsx 第5行 REFUND_LIST插入记录已提交
2025-05-28 12:33:05.746738 temp_refund_20250528123159.xlsx 第5行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:33:05.747527 信息: Equipment_ID 603010276 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 12:33:05.851149 数据库验证: 删除前后 IOT_Sales 表总金额为 2076086.15
2025-05-28 12:33:05.896589 数据库验证: 删除后后 IOT_Sales 表总金额为 2076086.15
2025-05-28 12:33:05.896802 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-28 12:33:05.897015 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:33:05.940137 temp_refund_20250528123159.xlsx 第6行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:33:05.986641 temp_refund_20250528123159.xlsx 第6行 尝试通过Order_No=2025052018391924777056855977984删除记录
2025-05-28 12:33:06.073028 temp_refund_20250528123159.xlsx 第6行 严重警告: 多次尝试后记录仍未被删除！
2025-05-28 12:33:06.073260 temp_refund_20250528123159.xlsx 第6行 事务已提交，状态: 已删除
2025-05-28 12:33:06.073477 temp_refund_20250528123159.xlsx 第6行 验证成功：记录已从数据库中移除
2025-05-28 12:33:06.082660 temp_refund_20250528123159.xlsx 第6行 REFUND_LIST插入记录已提交
2025-05-28 12:33:06.083017 temp_refund_20250528123159.xlsx 第6行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:33:06.083778 信息: Equipment_ID 603010276 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 12:33:06.179539 数据库验证: 删除前后 IOT_Sales 表总金额为 2076081.15
2025-05-28 12:33:06.217977 数据库验证: 删除后后 IOT_Sales 表总金额为 2076081.15
2025-05-28 12:33:06.218372 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-28 12:33:06.218840 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:33:06.261177 temp_refund_20250528123159.xlsx 第7行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:33:06.312331 temp_refund_20250528123159.xlsx 第7行 尝试通过Order_No=2025052018351924776043906396160删除记录
2025-05-28 12:33:06.407472 temp_refund_20250528123159.xlsx 第7行 严重警告: 多次尝试后记录仍未被删除！
2025-05-28 12:33:06.407704 temp_refund_20250528123159.xlsx 第7行 事务已提交，状态: 已删除
2025-05-28 12:33:06.407894 temp_refund_20250528123159.xlsx 第7行 验证成功：记录已从数据库中移除
2025-05-28 12:33:06.414677 temp_refund_20250528123159.xlsx 第7行 REFUND_LIST插入记录已提交
2025-05-28 12:33:06.415035 temp_refund_20250528123159.xlsx 第7行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:33:06.415828 信息: Equipment_ID 603010876 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 12:33:06.508201 数据库验证: 删除前后 IOT_Sales 表总金额为 2076076.15
2025-05-28 12:33:06.549216 数据库验证: 删除后后 IOT_Sales 表总金额为 2076076.15
2025-05-28 12:33:06.549571 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-28 12:33:06.549752 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:33:06.595533 temp_refund_20250528123159.xlsx 第8行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:33:06.643089 temp_refund_20250528123159.xlsx 第8行 尝试通过Order_No=2025052016451924748231522709504删除记录
2025-05-28 12:33:06.722569 temp_refund_20250528123159.xlsx 第8行 严重警告: 多次尝试后记录仍未被删除！
2025-05-28 12:33:06.722765 temp_refund_20250528123159.xlsx 第8行 事务已提交，状态: 已删除
2025-05-28 12:33:06.723054 temp_refund_20250528123159.xlsx 第8行 验证成功：记录已从数据库中移除
2025-05-28 12:33:06.730512 temp_refund_20250528123159.xlsx 第8行 REFUND_LIST插入记录已提交
2025-05-28 12:33:06.730983 temp_refund_20250528123159.xlsx 第8行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:33:06.731815 信息: Equipment_ID 603010434 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 12:33:06.818071 数据库验证: 删除前后 IOT_Sales 表总金额为 2076071.15
2025-05-28 12:33:06.857992 数据库验证: 删除后后 IOT_Sales 表总金额为 2076071.15
2025-05-28 12:33:06.858403 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-28 12:33:06.858794 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:33:06.915162 temp_refund_20250528123159.xlsx 第9行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:33:06.988944 temp_refund_20250528123159.xlsx 第9行 尝试通过Order_No=2025052020161924801300323168256删除记录
2025-05-28 12:33:07.093028 temp_refund_20250528123159.xlsx 第9行 严重警告: 多次尝试后记录仍未被删除！
2025-05-28 12:33:07.093357 temp_refund_20250528123159.xlsx 第9行 事务已提交，状态: 已删除
2025-05-28 12:33:07.093720 temp_refund_20250528123159.xlsx 第9行 验证成功：记录已从数据库中移除
2025-05-28 12:33:07.100648 temp_refund_20250528123159.xlsx 第9行 REFUND_LIST插入记录已提交
2025-05-28 12:33:07.100999 temp_refund_20250528123159.xlsx 第9行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:33:07.102194 信息: Equipment_ID 603011110 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 12:33:07.200359 数据库验证: 删除前后 IOT_Sales 表总金额为 2076066.15
2025-05-28 12:33:07.265799 数据库验证: 删除后后 IOT_Sales 表总金额为 2076066.15
2025-05-28 12:33:07.266079 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-28 12:33:07.266318 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:33:07.320983 temp_refund_20250528123159.xlsx 第10行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:33:07.375333 temp_refund_20250528123159.xlsx 第10行 尝试通过Order_No=2025052013171924695933211701248删除记录
2025-05-28 12:33:07.483299 temp_refund_20250528123159.xlsx 第10行 严重警告: 多次尝试后记录仍未被删除！
2025-05-28 12:33:07.483574 temp_refund_20250528123159.xlsx 第10行 事务已提交，状态: 已删除
2025-05-28 12:33:07.483849 temp_refund_20250528123159.xlsx 第10行 验证成功：记录已从数据库中移除
2025-05-28 12:33:07.491247 temp_refund_20250528123159.xlsx 第10行 REFUND_LIST插入记录已提交
2025-05-28 12:33:07.491645 temp_refund_20250528123159.xlsx 第10行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-28 12:33:07.492440 信息: Order No. 2025052019381924791955388690432 使用 Transaction Date '2025-05-20' (含时间) 进行精确匹配。
2025-05-28 12:33:07.574748 数据库验证: 删除前后 IOT_Sales 表总金额为 2076061.15
2025-05-28 12:33:07.628722 数据库验证: 删除后后 IOT_Sales 表总金额为 2076061.15
2025-05-28 12:33:07.629068 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-28 12:33:07.629310 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-28 12:33:07.673693 temp_refund_20250528123159.xlsx 第11行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-28 12:33:07.728933 temp_refund_20250528123159.xlsx 第11行 尝试通过Order_No=2025052019381924791955388690432删除记录
2025-05-28 12:33:07.817019 temp_refund_20250528123159.xlsx 第11行 严重警告: 多次尝试后记录仍未被删除！
2025-05-28 12:33:07.817294 temp_refund_20250528123159.xlsx 第11行 事务已提交，状态: 已删除
2025-05-28 12:33:07.817525 temp_refund_20250528123159.xlsx 第11行 验证成功：记录已从数据库中移除
2025-05-28 12:33:07.824450 temp_refund_20250528123159.xlsx 第11行 REFUND_LIST插入记录已提交
2025-05-28 12:33:07.824853 temp_refund_20250528123159.xlsx 第11行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 11:30:43.357497 信息: Equipment_ID 603011134 使用 Transaction Date '2025-05-21' (含时间) 进行精确匹配。
2025-05-29 11:30:43.453547 数据库验证: 更新前 - temp_refund_20250529112935.xlsx 第1行后 IOT_Sales 表总金额为 2207031.67
2025-05-29 11:30:43.502674 数据库验证: 更新后 - temp_refund_20250529112935.xlsx 第1行后 IOT_Sales 表总金额为 2207031.67
2025-05-29 11:30:43.502963 数据库验证: 预期金额变化 -10.0, 实际金额变化 0.0
2025-05-29 11:30:43.503233 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-05-29 11:30:43.503476 temp_refund_20250529112935.xlsx 第1行 更新验证失败: 期望=10.0, 实际=NULL
2025-05-29 11:30:43.503864 temp_refund_20250529112935.xlsx 第1行 事务已提交，状态: 已退款
2025-05-29 11:30:43.510274 temp_refund_20250529112935.xlsx 第1行 REFUND_LIST插入记录已提交
2025-05-29 11:30:43.510686 temp_refund_20250529112935.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 11:30:43.511550 信息: Equipment_ID 603011197 使用 Transaction Date '2025-05-21' (含时间) 进行精确匹配。
2025-05-29 11:30:43.598073 数据库验证: 更新前 - temp_refund_20250529112935.xlsx 第2行后 IOT_Sales 表总金额为 2207031.67
2025-05-29 11:30:43.638908 数据库验证: 更新后 - temp_refund_20250529112935.xlsx 第2行后 IOT_Sales 表总金额为 2207031.67
2025-05-29 11:30:43.639123 数据库验证: 预期金额变化 -10.0, 实际金额变化 0.0
2025-05-29 11:30:43.639334 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-05-29 11:30:43.639565 temp_refund_20250529112935.xlsx 第2行 更新验证失败: 期望=10.0, 实际=NULL
2025-05-29 11:30:43.639779 temp_refund_20250529112935.xlsx 第2行 事务已提交，状态: 已退款
2025-05-29 11:30:43.646688 temp_refund_20250529112935.xlsx 第2行 REFUND_LIST插入记录已提交
2025-05-29 11:30:43.647092 temp_refund_20250529112935.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 11:30:43.647890 信息: Equipment_ID 603010440 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 11:30:43.747332 数据库验证: 更新前 - temp_refund_20250529112935.xlsx 第3行后 IOT_Sales 表总金额为 2207031.67
2025-05-29 11:30:43.812818 数据库验证: 更新后 - temp_refund_20250529112935.xlsx 第3行后 IOT_Sales 表总金额为 2207031.67
2025-05-29 11:30:43.813201 数据库验证: 预期金额变化 -5.0, 实际金额变化 0.0
2025-05-29 11:30:43.813468 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-05-29 11:30:43.813790 temp_refund_20250529112935.xlsx 第3行 更新验证失败: 期望=5.0, 实际=NULL
2025-05-29 11:30:43.814142 temp_refund_20250529112935.xlsx 第3行 事务已提交，状态: 已退款
2025-05-29 11:30:43.821954 temp_refund_20250529112935.xlsx 第3行 REFUND_LIST插入记录已提交
2025-05-29 11:30:43.822892 temp_refund_20250529112935.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 11:30:43.825072 信息: Equipment_ID 603010375 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 11:30:43.935513 数据库验证: 删除前后 IOT_Sales 表总金额为 2207031.67
2025-05-29 11:30:43.992009 数据库验证: 删除后后 IOT_Sales 表总金额为 2207031.67
2025-05-29 11:30:43.992401 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-29 11:30:43.992665 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 11:30:44.045393 temp_refund_20250529112935.xlsx 第4行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 11:30:44.118982 temp_refund_20250529112935.xlsx 第4行 尝试通过Order_No=2025052519161926598130736361472删除记录
2025-05-29 11:30:44.234777 temp_refund_20250529112935.xlsx 第4行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 11:30:44.235072 temp_refund_20250529112935.xlsx 第4行 事务已提交，状态: 已删除
2025-05-29 11:30:44.235341 temp_refund_20250529112935.xlsx 第4行 验证成功：记录已从数据库中移除
2025-05-29 11:30:44.242219 temp_refund_20250529112935.xlsx 第4行 REFUND_LIST插入记录已提交
2025-05-29 11:30:44.242699 temp_refund_20250529112935.xlsx 第4行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 11:30:44.243689 信息: Equipment_ID 603011176 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 11:30:44.348358 数据库验证: 删除前后 IOT_Sales 表总金额为 2207026.67
2025-05-29 11:30:44.391578 数据库验证: 删除后后 IOT_Sales 表总金额为 2207026.67
2025-05-29 11:30:44.391836 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-29 11:30:44.392060 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 11:30:44.434229 temp_refund_20250529112935.xlsx 第5行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 11:30:44.490974 temp_refund_20250529112935.xlsx 第5行 尝试通过Order_No=2025052521011926624761236484096删除记录
2025-05-29 11:30:44.592869 temp_refund_20250529112935.xlsx 第5行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 11:30:44.593161 temp_refund_20250529112935.xlsx 第5行 事务已提交，状态: 已删除
2025-05-29 11:30:44.593364 temp_refund_20250529112935.xlsx 第5行 验证成功：记录已从数据库中移除
2025-05-29 11:30:44.600305 temp_refund_20250529112935.xlsx 第5行 REFUND_LIST插入记录已提交
2025-05-29 11:30:44.600677 temp_refund_20250529112935.xlsx 第5行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 11:30:44.601597 信息: Equipment_ID 603011176 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 11:30:44.700659 数据库验证: 删除前后 IOT_Sales 表总金额为 2207021.67
2025-05-29 11:30:44.753994 数据库验证: 删除后后 IOT_Sales 表总金额为 2207021.67
2025-05-29 11:30:44.754217 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-29 11:30:44.754376 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 11:30:44.814317 temp_refund_20250529112935.xlsx 第6行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 11:30:44.885216 temp_refund_20250529112935.xlsx 第6行 尝试通过Order_No=2025052520481926621367474122752删除记录
2025-05-29 11:30:44.992919 temp_refund_20250529112935.xlsx 第6行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 11:30:44.993169 temp_refund_20250529112935.xlsx 第6行 事务已提交，状态: 已删除
2025-05-29 11:30:44.993444 temp_refund_20250529112935.xlsx 第6行 验证成功：记录已从数据库中移除
2025-05-29 11:30:45.000310 temp_refund_20250529112935.xlsx 第6行 REFUND_LIST插入记录已提交
2025-05-29 11:30:45.000681 temp_refund_20250529112935.xlsx 第6行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 11:31:00.499648 信息: Equipment_ID 603010822 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 11:31:00.519917 数据库验证: 删除前后 ZERO_Sales 表总金额为 458089.61
2025-05-29 11:31:00.529864 数据库验证: 删除后后 ZERO_Sales 表总金额为 458089.61
2025-05-29 11:31:00.530097 数据库验证: 预期金额变化 10.0, 实际金额变化 0.0
2025-05-29 11:31:00.530334 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 11:31:00.539752 temp_refund_20250529113046.xlsx 第1行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 11:31:00.554965 temp_refund_20250529113046.xlsx 第1行 尝试通过Order_No=2025052521151926628260561088512删除记录
2025-05-29 11:31:00.578990 temp_refund_20250529113046.xlsx 第1行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 11:31:00.579213 temp_refund_20250529113046.xlsx 第1行 事务已提交，状态: 已删除
2025-05-29 11:31:00.579405 temp_refund_20250529113046.xlsx 第1行 验证成功：记录已从数据库中移除
2025-05-29 11:31:00.585742 temp_refund_20250529113046.xlsx 第1行 REFUND_LIST插入记录已提交
2025-05-29 11:31:00.586202 temp_refund_20250529113046.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:28:28.254300 信息: Equipment_ID 603011134 使用 Transaction Date '2025-05-21' (含时间) 进行精确匹配。
2025-05-29 14:28:28.455589 数据库验证: 更新前 - temp_refund_20250529142706.xlsx 第1行后 IOT_Sales 表总金额为 2207031.67
2025-05-29 14:28:28.554263 数据库验证: 更新后 - temp_refund_20250529142706.xlsx 第1行后 IOT_Sales 表总金额为 2207031.67
2025-05-29 14:28:28.554718 数据库验证: 预期金额变化 -10.0, 实际金额变化 0.0
2025-05-29 14:28:28.554948 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-05-29 14:28:28.555262 temp_refund_20250529142706.xlsx 第1行 更新验证失败: 期望=10.0, 实际=NULL
2025-05-29 14:28:28.555574 temp_refund_20250529142706.xlsx 第1行 事务已提交，状态: 已退款
2025-05-29 14:28:28.569138 temp_refund_20250529142706.xlsx 第1行 REFUND_LIST插入记录已提交
2025-05-29 14:28:28.570843 temp_refund_20250529142706.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:28:28.572972 信息: Equipment_ID 603011197 使用 Transaction Date '2025-05-21' (含时间) 进行精确匹配。
2025-05-29 14:28:28.740973 数据库验证: 更新前 - temp_refund_20250529142706.xlsx 第2行后 IOT_Sales 表总金额为 2207031.67
2025-05-29 14:28:28.827486 数据库验证: 更新后 - temp_refund_20250529142706.xlsx 第2行后 IOT_Sales 表总金额为 2207031.67
2025-05-29 14:28:28.828225 数据库验证: 预期金额变化 -10.0, 实际金额变化 0.0
2025-05-29 14:28:28.828615 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-05-29 14:28:28.829036 temp_refund_20250529142706.xlsx 第2行 更新验证失败: 期望=10.0, 实际=NULL
2025-05-29 14:28:28.829886 temp_refund_20250529142706.xlsx 第2行 事务已提交，状态: 已退款
2025-05-29 14:28:28.851147 temp_refund_20250529142706.xlsx 第2行 REFUND_LIST插入记录已提交
2025-05-29 14:28:28.851604 temp_refund_20250529142706.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:28:28.852585 信息: Equipment_ID 603010440 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 14:28:29.021259 数据库验证: 更新前 - temp_refund_20250529142706.xlsx 第3行后 IOT_Sales 表总金额为 2207031.67
2025-05-29 14:28:29.078086 数据库验证: 更新后 - temp_refund_20250529142706.xlsx 第3行后 IOT_Sales 表总金额为 2207031.67
2025-05-29 14:28:29.078298 数据库验证: 预期金额变化 -5.0, 实际金额变化 0.0
2025-05-29 14:28:29.078581 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-05-29 14:28:29.078849 temp_refund_20250529142706.xlsx 第3行 更新验证失败: 期望=5.0, 实际=NULL
2025-05-29 14:28:29.079030 temp_refund_20250529142706.xlsx 第3行 事务已提交，状态: 已退款
2025-05-29 14:28:29.093954 temp_refund_20250529142706.xlsx 第3行 REFUND_LIST插入记录已提交
2025-05-29 14:28:29.094621 temp_refund_20250529142706.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:28:29.100597 信息: Equipment_ID 603010375 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 14:28:29.213860 数据库验证: 删除前后 IOT_Sales 表总金额为 2207031.67
2025-05-29 14:28:29.271378 数据库验证: 删除后后 IOT_Sales 表总金额为 2207031.67
2025-05-29 14:28:29.271624 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-29 14:28:29.271876 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 14:28:29.320974 temp_refund_20250529142706.xlsx 第4行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 14:28:29.399692 temp_refund_20250529142706.xlsx 第4行 尝试通过Order_No=2025052519161926598130736361472删除记录
2025-05-29 14:28:29.514688 temp_refund_20250529142706.xlsx 第4行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 14:28:29.514902 temp_refund_20250529142706.xlsx 第4行 事务已提交，状态: 已删除
2025-05-29 14:28:29.515087 temp_refund_20250529142706.xlsx 第4行 验证成功：记录已从数据库中移除
2025-05-29 14:28:29.521503 temp_refund_20250529142706.xlsx 第4行 REFUND_LIST插入记录已提交
2025-05-29 14:28:29.521845 temp_refund_20250529142706.xlsx 第4行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:28:29.523406 信息: Equipment_ID 603011176 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 14:28:29.648131 数据库验证: 删除前后 IOT_Sales 表总金额为 2207026.67
2025-05-29 14:28:29.718898 数据库验证: 删除后后 IOT_Sales 表总金额为 2207026.67
2025-05-29 14:28:29.719264 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-29 14:28:29.719560 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 14:28:29.777767 temp_refund_20250529142706.xlsx 第5行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 14:28:29.854168 temp_refund_20250529142706.xlsx 第5行 尝试通过Order_No=2025052521011926624761236484096删除记录
2025-05-29 14:28:30.042834 temp_refund_20250529142706.xlsx 第5行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 14:28:30.043407 temp_refund_20250529142706.xlsx 第5行 事务已提交，状态: 已删除
2025-05-29 14:28:30.044341 temp_refund_20250529142706.xlsx 第5行 验证成功：记录已从数据库中移除
2025-05-29 14:28:30.054841 temp_refund_20250529142706.xlsx 第5行 REFUND_LIST插入记录已提交
2025-05-29 14:28:30.055692 temp_refund_20250529142706.xlsx 第5行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:28:30.059389 信息: Equipment_ID 603011176 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 14:28:30.273282 数据库验证: 删除前后 IOT_Sales 表总金额为 2207021.67
2025-05-29 14:28:30.377975 数据库验证: 删除后后 IOT_Sales 表总金额为 2207021.67
2025-05-29 14:28:30.378505 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-29 14:28:30.378837 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 14:28:30.487643 temp_refund_20250529142706.xlsx 第6行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 14:28:30.614285 temp_refund_20250529142706.xlsx 第6行 尝试通过Order_No=2025052520481926621367474122752删除记录
2025-05-29 14:28:30.769284 temp_refund_20250529142706.xlsx 第6行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 14:28:30.769622 temp_refund_20250529142706.xlsx 第6行 事务已提交，状态: 已删除
2025-05-29 14:28:30.770024 temp_refund_20250529142706.xlsx 第6行 验证成功：记录已从数据库中移除
2025-05-29 14:28:30.777916 temp_refund_20250529142706.xlsx 第6行 REFUND_LIST插入记录已提交
2025-05-29 14:28:30.778649 temp_refund_20250529142706.xlsx 第6行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:40:28.314106 信息: Equipment_ID 603011134 使用 Transaction Date '2025-05-21' (含时间) 进行精确匹配。
2025-05-29 14:40:28.452251 数据库验证: 更新前 - temp_refund_20250529143917.xlsx 第1行后 IOT_Sales 表总金额为 2184678.17
2025-05-29 14:40:28.514447 数据库验证: 更新后 - temp_refund_20250529143917.xlsx 第1行后 IOT_Sales 表总金额为 2184678.17
2025-05-29 14:40:28.515876 数据库验证: 预期金额变化 -10.0, 实际金额变化 0.0
2025-05-29 14:40:28.516207 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-05-29 14:40:28.516632 temp_refund_20250529143917.xlsx 第1行 更新验证失败: 期望=10.0, 实际=NULL
2025-05-29 14:40:28.517070 temp_refund_20250529143917.xlsx 第1行 事务已提交，状态: 已退款
2025-05-29 14:40:28.532715 temp_refund_20250529143917.xlsx 第1行 REFUND_LIST插入记录已提交
2025-05-29 14:40:28.533194 temp_refund_20250529143917.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:40:28.534291 信息: Equipment_ID 603011197 使用 Transaction Date '2025-05-21' (含时间) 进行精确匹配。
2025-05-29 14:40:28.664443 数据库验证: 更新前 - temp_refund_20250529143917.xlsx 第2行后 IOT_Sales 表总金额为 2184678.17
2025-05-29 14:40:28.724696 数据库验证: 更新后 - temp_refund_20250529143917.xlsx 第2行后 IOT_Sales 表总金额为 2184678.17
2025-05-29 14:40:28.725049 数据库验证: 预期金额变化 -10.0, 实际金额变化 0.0
2025-05-29 14:40:28.725217 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-05-29 14:40:28.725484 temp_refund_20250529143917.xlsx 第2行 更新验证失败: 期望=10.0, 实际=NULL
2025-05-29 14:40:28.725735 temp_refund_20250529143917.xlsx 第2行 事务已提交，状态: 已退款
2025-05-29 14:40:28.731798 temp_refund_20250529143917.xlsx 第2行 REFUND_LIST插入记录已提交
2025-05-29 14:40:28.732143 temp_refund_20250529143917.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:40:28.732912 信息: Equipment_ID 603010440 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 14:40:28.866137 数据库验证: 更新前 - temp_refund_20250529143917.xlsx 第3行后 IOT_Sales 表总金额为 2184678.17
2025-05-29 14:40:28.928716 数据库验证: 更新后 - temp_refund_20250529143917.xlsx 第3行后 IOT_Sales 表总金额为 2184678.17
2025-05-29 14:40:28.928963 数据库验证: 预期金额变化 -5.0, 实际金额变化 0.0
2025-05-29 14:40:28.929170 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-05-29 14:40:28.929510 temp_refund_20250529143917.xlsx 第3行 更新验证失败: 期望=5.0, 实际=NULL
2025-05-29 14:40:28.929759 temp_refund_20250529143917.xlsx 第3行 事务已提交，状态: 已退款
2025-05-29 14:40:28.937267 temp_refund_20250529143917.xlsx 第3行 REFUND_LIST插入记录已提交
2025-05-29 14:40:28.938066 temp_refund_20250529143917.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:40:28.939990 信息: Equipment_ID 603010375 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 14:40:29.092402 数据库验证: 删除前后 IOT_Sales 表总金额为 2184678.17
2025-05-29 14:40:29.170513 数据库验证: 删除后后 IOT_Sales 表总金额为 2184678.17
2025-05-29 14:40:29.170754 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-29 14:40:29.170955 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 14:40:29.236875 temp_refund_20250529143917.xlsx 第4行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 14:40:29.319405 temp_refund_20250529143917.xlsx 第4行 尝试通过Order_No=2025052519161926598130736361472删除记录
2025-05-29 14:40:29.428648 temp_refund_20250529143917.xlsx 第4行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 14:40:29.428860 temp_refund_20250529143917.xlsx 第4行 事务已提交，状态: 已删除
2025-05-29 14:40:29.429229 temp_refund_20250529143917.xlsx 第4行 验证成功：记录已从数据库中移除
2025-05-29 14:40:29.436607 temp_refund_20250529143917.xlsx 第4行 REFUND_LIST插入记录已提交
2025-05-29 14:40:29.437010 temp_refund_20250529143917.xlsx 第4行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:40:29.437925 信息: Equipment_ID 603011176 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 14:40:29.547067 数据库验证: 删除前后 IOT_Sales 表总金额为 2184673.17
2025-05-29 14:40:29.638687 数据库验证: 删除后后 IOT_Sales 表总金额为 2184673.17
2025-05-29 14:40:29.639313 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-29 14:40:29.639564 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 14:40:29.701863 temp_refund_20250529143917.xlsx 第5行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 14:40:29.760294 temp_refund_20250529143917.xlsx 第5行 尝试通过Order_No=2025052521011926624761236484096删除记录
2025-05-29 14:40:29.864029 temp_refund_20250529143917.xlsx 第5行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 14:40:29.864253 temp_refund_20250529143917.xlsx 第5行 事务已提交，状态: 已删除
2025-05-29 14:40:29.864436 temp_refund_20250529143917.xlsx 第5行 验证成功：记录已从数据库中移除
2025-05-29 14:40:29.870920 temp_refund_20250529143917.xlsx 第5行 REFUND_LIST插入记录已提交
2025-05-29 14:40:29.871556 temp_refund_20250529143917.xlsx 第5行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:40:29.872806 信息: Equipment_ID 603011176 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 14:40:29.982532 数据库验证: 删除前后 IOT_Sales 表总金额为 2184668.17
2025-05-29 14:40:30.031735 数据库验证: 删除后后 IOT_Sales 表总金额为 2184668.17
2025-05-29 14:40:30.031962 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-29 14:40:30.032157 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 14:40:30.091799 temp_refund_20250529143917.xlsx 第6行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 14:40:30.163679 temp_refund_20250529143917.xlsx 第6行 尝试通过Order_No=2025052520481926621367474122752删除记录
2025-05-29 14:40:30.273993 temp_refund_20250529143917.xlsx 第6行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 14:40:30.274372 temp_refund_20250529143917.xlsx 第6行 事务已提交，状态: 已删除
2025-05-29 14:40:30.274894 temp_refund_20250529143917.xlsx 第6行 验证成功：记录已从数据库中移除
2025-05-29 14:40:30.283259 temp_refund_20250529143917.xlsx 第6行 REFUND_LIST插入记录已提交
2025-05-29 14:40:30.284192 temp_refund_20250529143917.xlsx 第6行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:46:36.503254 信息: Equipment_ID 603011134 使用 Transaction Date '2025-05-21' (含时间) 进行精确匹配。
2025-05-29 14:46:36.596184 数据库验证: 更新前 - temp_refund_20250529144520.xlsx 第1行后 IOT_Sales 表总金额为 2184663.17
2025-05-29 14:46:36.649830 数据库验证: 更新后 - temp_refund_20250529144520.xlsx 第1行后 IOT_Sales 表总金额为 2184663.17
2025-05-29 14:46:36.650065 数据库验证: 预期金额变化 -10.0, 实际金额变化 0.0
2025-05-29 14:46:36.650247 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-05-29 14:46:36.650556 temp_refund_20250529144520.xlsx 第1行 更新验证失败: 期望=10.0, 实际=NULL
2025-05-29 14:46:36.650779 temp_refund_20250529144520.xlsx 第1行 事务已提交，状态: 已退款
2025-05-29 14:46:36.665867 temp_refund_20250529144520.xlsx 第1行 REFUND_LIST插入记录已提交
2025-05-29 14:46:36.666292 temp_refund_20250529144520.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:46:36.667147 信息: Equipment_ID 603011197 使用 Transaction Date '2025-05-21' (含时间) 进行精确匹配。
2025-05-29 14:46:36.757608 数据库验证: 更新前 - temp_refund_20250529144520.xlsx 第2行后 IOT_Sales 表总金额为 2184663.17
2025-05-29 14:46:36.810809 数据库验证: 更新后 - temp_refund_20250529144520.xlsx 第2行后 IOT_Sales 表总金额为 2184663.17
2025-05-29 14:46:36.811441 数据库验证: 预期金额变化 -10.0, 实际金额变化 0.0
2025-05-29 14:46:36.811737 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-05-29 14:46:36.812056 temp_refund_20250529144520.xlsx 第2行 更新验证失败: 期望=10.0, 实际=NULL
2025-05-29 14:46:36.812363 temp_refund_20250529144520.xlsx 第2行 事务已提交，状态: 已退款
2025-05-29 14:46:36.819541 temp_refund_20250529144520.xlsx 第2行 REFUND_LIST插入记录已提交
2025-05-29 14:46:36.820029 temp_refund_20250529144520.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:46:36.821155 信息: Equipment_ID 603010440 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 14:46:36.914995 数据库验证: 更新前 - temp_refund_20250529144520.xlsx 第3行后 IOT_Sales 表总金额为 2184663.17
2025-05-29 14:46:36.961583 数据库验证: 更新后 - temp_refund_20250529144520.xlsx 第3行后 IOT_Sales 表总金额为 2184663.17
2025-05-29 14:46:36.961900 数据库验证: 预期金额变化 -5.0, 实际金额变化 0.0
2025-05-29 14:46:36.962165 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-05-29 14:46:36.962396 temp_refund_20250529144520.xlsx 第3行 更新验证失败: 期望=5.0, 实际=NULL
2025-05-29 14:46:36.962765 temp_refund_20250529144520.xlsx 第3行 事务已提交，状态: 已退款
2025-05-29 14:46:36.970499 temp_refund_20250529144520.xlsx 第3行 REFUND_LIST插入记录已提交
2025-05-29 14:46:36.970998 temp_refund_20250529144520.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:46:36.971814 信息: Equipment_ID 603010375 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 14:46:37.112179 数据库验证: 删除前后 IOT_Sales 表总金额为 2184663.17
2025-05-29 14:46:37.191703 数据库验证: 删除后后 IOT_Sales 表总金额为 2184663.17
2025-05-29 14:46:37.192204 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-29 14:46:37.192581 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 14:46:37.261361 temp_refund_20250529144520.xlsx 第4行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 14:46:37.345688 temp_refund_20250529144520.xlsx 第4行 尝试通过Order_No=2025052518461926590635330564096删除记录
2025-05-29 14:46:37.455780 temp_refund_20250529144520.xlsx 第4行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 14:46:37.456050 temp_refund_20250529144520.xlsx 第4行 事务已提交，状态: 已删除
2025-05-29 14:46:37.456338 temp_refund_20250529144520.xlsx 第4行 验证成功：记录已从数据库中移除
2025-05-29 14:46:37.462881 temp_refund_20250529144520.xlsx 第4行 REFUND_LIST插入记录已提交
2025-05-29 14:46:37.463259 temp_refund_20250529144520.xlsx 第4行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:46:37.463999 信息: Equipment_ID 603011176 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 14:46:37.566301 数据库验证: 删除前后 IOT_Sales 表总金额为 2184658.17
2025-05-29 14:46:37.614152 数据库验证: 删除后后 IOT_Sales 表总金额为 2184658.17
2025-05-29 14:46:37.614412 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-29 14:46:37.614589 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 14:46:37.666132 temp_refund_20250529144520.xlsx 第5行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 14:46:37.723857 temp_refund_20250529144520.xlsx 第5行 尝试通过Order_No=2025052519581926608719198089216删除记录
2025-05-29 14:46:37.825843 temp_refund_20250529144520.xlsx 第5行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 14:46:37.826095 temp_refund_20250529144520.xlsx 第5行 事务已提交，状态: 已删除
2025-05-29 14:46:37.826336 temp_refund_20250529144520.xlsx 第5行 验证成功：记录已从数据库中移除
2025-05-29 14:46:37.833129 temp_refund_20250529144520.xlsx 第5行 REFUND_LIST插入记录已提交
2025-05-29 14:46:37.833482 temp_refund_20250529144520.xlsx 第5行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:46:37.834233 信息: Equipment_ID 603011176 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 14:46:37.931468 数据库验证: 删除前后 IOT_Sales 表总金额为 2184653.17
2025-05-29 14:46:37.978338 数据库验证: 删除后后 IOT_Sales 表总金额为 2184653.17
2025-05-29 14:46:37.978550 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-05-29 14:46:37.978717 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 14:46:38.036541 temp_refund_20250529144520.xlsx 第6行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 14:46:38.117894 temp_refund_20250529144520.xlsx 第6行 尝试通过Order_No=2025052519481926606415690526720删除记录
2025-05-29 14:46:38.244594 temp_refund_20250529144520.xlsx 第6行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 14:46:38.245044 temp_refund_20250529144520.xlsx 第6行 事务已提交，状态: 已删除
2025-05-29 14:46:38.246035 temp_refund_20250529144520.xlsx 第6行 验证成功：记录已从数据库中移除
2025-05-29 14:46:38.256514 temp_refund_20250529144520.xlsx 第6行 REFUND_LIST插入记录已提交
2025-05-29 14:46:38.257181 temp_refund_20250529144520.xlsx 第6行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-29 14:59:24.366763 信息: Equipment_ID 603010822 使用 Transaction Date '2025-05-25' (含时间) 进行精确匹配。
2025-05-29 14:59:24.387399 数据库验证: 删除前后 ZERO_Sales 表总金额为 458089.61
2025-05-29 14:59:24.399792 数据库验证: 删除后后 ZERO_Sales 表总金额为 458089.61
2025-05-29 14:59:24.400095 数据库验证: 预期金额变化 10.0, 实际金额变化 0.0
2025-05-29 14:59:24.400385 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-05-29 14:59:24.413099 temp_refund_20250529145911.xlsx 第1行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-05-29 14:59:24.428856 temp_refund_20250529145911.xlsx 第1行 尝试通过Order_No=2025052521151926628260561088512删除记录
2025-05-29 14:59:24.448495 temp_refund_20250529145911.xlsx 第1行 严重警告: 多次尝试后记录仍未被删除！
2025-05-29 14:59:24.448755 temp_refund_20250529145911.xlsx 第1行 事务已提交，状态: 已删除
2025-05-29 14:59:24.449073 temp_refund_20250529145911.xlsx 第1行 验证成功：记录已从数据库中移除
2025-05-29 14:59:24.455434 temp_refund_20250529145911.xlsx 第1行 REFUND_LIST插入记录已提交
2025-05-29 14:59:24.455898 temp_refund_20250529145911.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-05-30 11:12:26.481189 信息: Equipment_ID 603011169 使用 Transaction Date '2025-05-08' (含时间) 进行精确匹配。
2025-05-30 11:12:26.570816 数据库验证: 更新前 - temp_refund_20250530111124.xlsx 第1行后 IOT_Sales 表总金额为 2228832.19
2025-05-30 11:12:26.611152 数据库验证: 更新后 - temp_refund_20250530111124.xlsx 第1行后 IOT_Sales 表总金额为 2228832.19
2025-05-30 11:12:26.611449 数据库验证: 预期金额变化 -5.0, 实际金额变化 0.0
2025-05-30 11:12:26.611685 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-05-30 11:12:26.611891 temp_refund_20250530111124.xlsx 第1行 更新验证失败: 期望=5.0, 实际=NULL
2025-05-30 11:12:26.612079 temp_refund_20250530111124.xlsx 第1行 事务已提交，状态: 已退款
2025-05-30 11:12:26.618392 temp_refund_20250530111124.xlsx 第1行 REFUND_LIST插入记录已提交
2025-05-30 11:12:26.618738 temp_refund_20250530111124.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-04 11:53:02.285216 信息: Equipment_ID 603011396 使用 Transaction Date '2025-05-27' (含时间) 进行精确匹配。
2025-06-04 11:53:02.382960 数据库验证: 删除前后 IOT_Sales 表总金额为 2398364.28
2025-06-04 11:53:02.427551 数据库验证: 删除后后 IOT_Sales 表总金额为 2398364.28
2025-06-04 11:53:02.427750 数据库验证: 预期金额变化 1.0, 实际金额变化 0.0
2025-06-04 11:53:02.427930 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-04 11:53:02.479351 temp_refund_20250604115150.xlsx 第1行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-04 11:53:02.538878 temp_refund_20250604115150.xlsx 第1行 尝试通过Order_No=2025052711191927203059393949696删除记录
2025-06-04 11:53:02.641838 temp_refund_20250604115150.xlsx 第1行 最终删除验证通过: 记录已成功删除
2025-06-04 11:53:02.642121 temp_refund_20250604115150.xlsx 第1行 事务已提交，状态: 已删除
2025-06-04 11:53:02.642389 temp_refund_20250604115150.xlsx 第1行 验证成功：记录已从数据库中移除
2025-06-04 11:53:02.648808 temp_refund_20250604115150.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-04 11:53:02.649200 temp_refund_20250604115150.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-04 11:53:02.649951 信息: Equipment_ID 603010100 使用 Transaction Date '2025-05-27' (含时间) 进行精确匹配。
2025-06-04 11:53:02.764097 数据库验证: 删除前后 IOT_Sales 表总金额为 2398363.28
2025-06-04 11:53:02.824949 数据库验证: 删除后后 IOT_Sales 表总金额为 2398363.28
2025-06-04 11:53:02.825255 数据库验证: 预期金额变化 1.0, 实际金额变化 0.0
2025-06-04 11:53:02.825457 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-04 11:53:02.883526 temp_refund_20250604115150.xlsx 第2行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-04 11:53:02.949901 temp_refund_20250604115150.xlsx 第2行 尝试通过Order_No=2025052711061927199615102808064删除记录
2025-06-04 11:53:03.061332 temp_refund_20250604115150.xlsx 第2行 最终删除验证通过: 记录已成功删除
2025-06-04 11:53:03.061605 temp_refund_20250604115150.xlsx 第2行 事务已提交，状态: 已删除
2025-06-04 11:53:03.061979 temp_refund_20250604115150.xlsx 第2行 验证成功：记录已从数据库中移除
2025-06-04 11:53:03.069099 temp_refund_20250604115150.xlsx 第2行 REFUND_LIST插入记录已提交
2025-06-04 11:53:03.069550 temp_refund_20250604115150.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-04 11:53:03.070417 信息: Order No. 2025052617501926939003483385856 使用 Transaction Date '2025-05-26' (含时间) 进行精确匹配。
2025-06-04 11:53:03.171992 数据库验证: 删除前后 IOT_Sales 表总金额为 2398362.28
2025-06-04 11:53:03.219970 数据库验证: 删除后后 IOT_Sales 表总金额为 2398362.28
2025-06-04 11:53:03.220236 数据库验证: 预期金额变化 10.0, 实际金额变化 0.0
2025-06-04 11:53:03.220401 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-04 11:53:03.271027 temp_refund_20250604115150.xlsx 第3行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-04 11:53:03.335289 temp_refund_20250604115150.xlsx 第3行 尝试通过Order_No=2025052617501926939003483385856删除记录
2025-06-04 11:53:03.431629 temp_refund_20250604115150.xlsx 第3行 严重警告: 多次尝试后记录仍未被删除！
2025-06-04 11:53:03.431928 temp_refund_20250604115150.xlsx 第3行 事务已提交，状态: 已删除
2025-06-04 11:53:03.432274 temp_refund_20250604115150.xlsx 第3行 验证成功：记录已从数据库中移除
2025-06-04 11:53:03.439032 temp_refund_20250604115150.xlsx 第3行 REFUND_LIST插入记录已提交
2025-06-04 11:53:03.439470 temp_refund_20250604115150.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:19.642850 信息: Equipment_ID 603010743 使用 Transaction Date '2025-05-30' (含时间) 进行精确匹配。
2025-06-06 09:11:19.737148 数据库验证: 删除前后 IOT_Sales 表总金额为 2444132.14
2025-06-06 09:11:19.780545 数据库验证: 删除后后 IOT_Sales 表总金额为 2444132.14
2025-06-06 09:11:19.780776 数据库验证: 预期金额变化 15.0, 实际金额变化 0.0
2025-06-06 09:11:19.780908 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-06 09:11:19.824847 temp_refund_20250606091001.xlsx 第1行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-06 09:11:19.872841 temp_refund_20250606091001.xlsx 第1行 尝试通过Order_No=2025053016261928367377577734144删除记录
2025-06-06 09:11:19.961583 temp_refund_20250606091001.xlsx 第1行 严重警告: 多次尝试后记录仍未被删除！
2025-06-06 09:11:19.961790 temp_refund_20250606091001.xlsx 第1行 事务已提交，状态: 已删除
2025-06-06 09:11:19.962104 temp_refund_20250606091001.xlsx 第1行 验证成功：记录已从数据库中移除
2025-06-06 09:11:19.968594 temp_refund_20250606091001.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-06 09:11:19.968983 temp_refund_20250606091001.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:19.969751 信息: Equipment_ID 603010750 使用 Transaction Date '2025-05-31' (含时间) 进行精确匹配。
2025-06-06 09:11:20.065188 数据库验证: 删除前后 IOT_Sales 表总金额为 2444117.14
2025-06-06 09:11:20.115829 数据库验证: 删除后后 IOT_Sales 表总金额为 2444117.14
2025-06-06 09:11:20.116160 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-06 09:11:20.116357 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-06 09:11:20.168745 temp_refund_20250606091001.xlsx 第2行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-06 09:11:20.230559 temp_refund_20250606091001.xlsx 第2行 尝试通过Order_No=2025053112301928670311821668352删除记录
2025-06-06 09:11:20.340327 temp_refund_20250606091001.xlsx 第2行 严重警告: 多次尝试后记录仍未被删除！
2025-06-06 09:11:20.340702 temp_refund_20250606091001.xlsx 第2行 事务已提交，状态: 已删除
2025-06-06 09:11:20.340961 temp_refund_20250606091001.xlsx 第2行 验证成功：记录已从数据库中移除
2025-06-06 09:11:20.346948 temp_refund_20250606091001.xlsx 第2行 REFUND_LIST插入记录已提交
2025-06-06 09:11:20.347263 temp_refund_20250606091001.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:20.347999 信息: Equipment_ID 603010743 使用 Transaction Date '2025-05-31' (含时间) 进行精确匹配。
2025-06-06 09:11:20.474195 数据库验证: 删除前后 IOT_Sales 表总金额为 2444112.14
2025-06-06 09:11:20.532245 数据库验证: 删除后后 IOT_Sales 表总金额为 2444112.14
2025-06-06 09:11:20.532459 数据库验证: 预期金额变化 10.0, 实际金额变化 0.0
2025-06-06 09:11:20.532680 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-06 09:11:20.587054 temp_refund_20250606091001.xlsx 第3行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-06 09:11:20.645787 temp_refund_20250606091001.xlsx 第3行 尝试通过Order_No=2025053115541928721723108683776删除记录
2025-06-06 09:11:20.751257 temp_refund_20250606091001.xlsx 第3行 严重警告: 多次尝试后记录仍未被删除！
2025-06-06 09:11:20.751721 temp_refund_20250606091001.xlsx 第3行 事务已提交，状态: 已删除
2025-06-06 09:11:20.752330 temp_refund_20250606091001.xlsx 第3行 验证成功：记录已从数据库中移除
2025-06-06 09:11:20.759514 temp_refund_20250606091001.xlsx 第3行 REFUND_LIST插入记录已提交
2025-06-06 09:11:20.759904 temp_refund_20250606091001.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:20.760819 信息: Equipment_ID 603010605 使用 Transaction Date '2025-06-01' (含时间) 进行精确匹配。
2025-06-06 09:11:20.874289 数据库验证: 删除前后 IOT_Sales 表总金额为 2444102.14
2025-06-06 09:11:20.921241 数据库验证: 删除后后 IOT_Sales 表总金额为 2444102.14
2025-06-06 09:11:20.921532 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-06 09:11:20.921678 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-06 09:11:20.971714 temp_refund_20250606091001.xlsx 第4行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-06 09:11:21.030806 temp_refund_20250606091001.xlsx 第4行 尝试通过Order_No=2025060114221929061024036089856删除记录
2025-06-06 09:11:21.148321 temp_refund_20250606091001.xlsx 第4行 严重警告: 多次尝试后记录仍未被删除！
2025-06-06 09:11:21.148554 temp_refund_20250606091001.xlsx 第4行 事务已提交，状态: 已删除
2025-06-06 09:11:21.148779 temp_refund_20250606091001.xlsx 第4行 验证成功：记录已从数据库中移除
2025-06-06 09:11:21.155383 temp_refund_20250606091001.xlsx 第4行 REFUND_LIST插入记录已提交
2025-06-06 09:11:21.155764 temp_refund_20250606091001.xlsx 第4行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:21.156577 信息: Equipment_ID 603010605 使用 Transaction Date '2025-06-01' (含时间) 进行精确匹配。
2025-06-06 09:11:21.268904 数据库验证: 删除前后 IOT_Sales 表总金额为 2444097.14
2025-06-06 09:11:21.322577 数据库验证: 删除后后 IOT_Sales 表总金额为 2444097.14
2025-06-06 09:11:21.322907 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-06 09:11:21.323197 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-06 09:11:21.372170 temp_refund_20250606091001.xlsx 第5行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-06 09:11:21.427699 temp_refund_20250606091001.xlsx 第5行 尝试通过Order_No=2025060114181929059873806938112删除记录
2025-06-06 09:11:21.551941 temp_refund_20250606091001.xlsx 第5行 严重警告: 多次尝试后记录仍未被删除！
2025-06-06 09:11:21.552239 temp_refund_20250606091001.xlsx 第5行 事务已提交，状态: 已删除
2025-06-06 09:11:21.552523 temp_refund_20250606091001.xlsx 第5行 验证成功：记录已从数据库中移除
2025-06-06 09:11:21.558165 temp_refund_20250606091001.xlsx 第5行 REFUND_LIST插入记录已提交
2025-06-06 09:11:21.558484 temp_refund_20250606091001.xlsx 第5行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:21.559204 信息: Equipment_ID 603010605 使用 Transaction Date '2025-06-01' (含时间) 进行精确匹配。
2025-06-06 09:11:21.664044 数据库验证: 更新前 - temp_refund_20250606091001.xlsx 第6行后 IOT_Sales 表总金额为 2444092.14
2025-06-06 09:11:21.713109 数据库验证: 更新后 - temp_refund_20250606091001.xlsx 第6行后 IOT_Sales 表总金额为 2444092.14
2025-06-06 09:11:21.713344 数据库验证: 预期金额变化 -5.0, 实际金额变化 0.0
2025-06-06 09:11:21.713672 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-06-06 09:11:21.713935 temp_refund_20250606091001.xlsx 第6行 更新验证失败: 期望=5.0, 实际=NULL
2025-06-06 09:11:21.714139 temp_refund_20250606091001.xlsx 第6行 事务已提交，状态: 已退款
2025-06-06 09:11:21.720647 temp_refund_20250606091001.xlsx 第6行 REFUND_LIST插入记录已提交
2025-06-06 09:11:21.720976 temp_refund_20250606091001.xlsx 第6行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:21.721737 信息: Equipment_ID 603010743 使用 Transaction Date '2025-06-01' (含时间) 进行精确匹配。
2025-06-06 09:11:21.821264 数据库验证: 删除前后 IOT_Sales 表总金额为 2444092.14
2025-06-06 09:11:21.869647 数据库验证: 删除后后 IOT_Sales 表总金额为 2444092.14
2025-06-06 09:11:21.869863 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-06 09:11:21.870091 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-06 09:11:21.914179 temp_refund_20250606091001.xlsx 第7行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-06 09:11:21.962184 temp_refund_20250606091001.xlsx 第7行 尝试通过Order_No=2025060122211929181607356854272删除记录
2025-06-06 09:11:22.062708 temp_refund_20250606091001.xlsx 第7行 严重警告: 多次尝试后记录仍未被删除！
2025-06-06 09:11:22.062926 temp_refund_20250606091001.xlsx 第7行 事务已提交，状态: 已删除
2025-06-06 09:11:22.063124 temp_refund_20250606091001.xlsx 第7行 验证成功：记录已从数据库中移除
2025-06-06 09:11:22.069804 temp_refund_20250606091001.xlsx 第7行 REFUND_LIST插入记录已提交
2025-06-06 09:11:22.070144 temp_refund_20250606091001.xlsx 第7行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:22.070891 信息: Equipment_ID 603010188 使用 Transaction Date '2025-06-01' (含时间) 进行精确匹配。
2025-06-06 09:11:22.161475 数据库验证: 删除前后 IOT_Sales 表总金额为 2444087.14
2025-06-06 09:11:22.205593 数据库验证: 删除后后 IOT_Sales 表总金额为 2444087.14
2025-06-06 09:11:22.205819 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-06 09:11:22.206054 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-06 09:11:22.259372 temp_refund_20250606091001.xlsx 第8行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-06 09:11:22.320103 temp_refund_20250606091001.xlsx 第8行 尝试通过Order_No=2025060122011929176602977562624删除记录
2025-06-06 09:11:22.419718 temp_refund_20250606091001.xlsx 第8行 严重警告: 多次尝试后记录仍未被删除！
2025-06-06 09:11:22.419957 temp_refund_20250606091001.xlsx 第8行 事务已提交，状态: 已删除
2025-06-06 09:11:22.420294 temp_refund_20250606091001.xlsx 第8行 验证成功：记录已从数据库中移除
2025-06-06 09:11:22.426458 temp_refund_20250606091001.xlsx 第8行 REFUND_LIST插入记录已提交
2025-06-06 09:11:22.426779 temp_refund_20250606091001.xlsx 第8行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:22.427504 信息: Equipment_ID 603010438 使用 Transaction Date '2025-06-01' (含时间) 进行精确匹配。
2025-06-06 09:11:22.526613 数据库验证: 删除前后 IOT_Sales 表总金额为 2444082.14
2025-06-06 09:11:22.575509 数据库验证: 删除后后 IOT_Sales 表总金额为 2444082.14
2025-06-06 09:11:22.575867 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-06 09:11:22.576075 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-06 09:11:22.629022 temp_refund_20250606091001.xlsx 第9行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-06 09:11:22.682810 temp_refund_20250606091001.xlsx 第9行 尝试通过Order_No=2025060121131929164391550873600删除记录
2025-06-06 09:11:22.792466 temp_refund_20250606091001.xlsx 第9行 严重警告: 多次尝试后记录仍未被删除！
2025-06-06 09:11:22.792756 temp_refund_20250606091001.xlsx 第9行 事务已提交，状态: 已删除
2025-06-06 09:11:22.793128 temp_refund_20250606091001.xlsx 第9行 验证成功：记录已从数据库中移除
2025-06-06 09:11:22.799616 temp_refund_20250606091001.xlsx 第9行 REFUND_LIST插入记录已提交
2025-06-06 09:11:22.799981 temp_refund_20250606091001.xlsx 第9行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:22.800769 信息: Equipment_ID 603010375 使用 Transaction Date '2025-06-01' (含时间) 进行精确匹配。
2025-06-06 09:11:22.900634 数据库验证: 删除前后 IOT_Sales 表总金额为 2444077.14
2025-06-06 09:11:22.948909 数据库验证: 删除后后 IOT_Sales 表总金额为 2444077.14
2025-06-06 09:11:22.949119 数据库验证: 预期金额变化 10.0, 实际金额变化 0.0
2025-06-06 09:11:22.949288 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-06 09:11:23.003510 temp_refund_20250606091001.xlsx 第10行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-06 09:11:23.064514 temp_refund_20250606091001.xlsx 第10行 尝试通过Order_No=2025060119401929141014740135936删除记录
2025-06-06 09:11:23.160000 temp_refund_20250606091001.xlsx 第10行 严重警告: 多次尝试后记录仍未被删除！
2025-06-06 09:11:23.160206 temp_refund_20250606091001.xlsx 第10行 事务已提交，状态: 已删除
2025-06-06 09:11:23.160463 temp_refund_20250606091001.xlsx 第10行 验证成功：记录已从数据库中移除
2025-06-06 09:11:23.167143 temp_refund_20250606091001.xlsx 第10行 REFUND_LIST插入记录已提交
2025-06-06 09:11:23.167566 temp_refund_20250606091001.xlsx 第10行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:23.168353 信息: Order No. 2025060119061929132545337257984 使用 Transaction Date '2025-06-01' (含时间) 进行精确匹配。
2025-06-06 09:11:23.268612 数据库验证: 删除前后 IOT_Sales 表总金额为 2444067.14
2025-06-06 09:11:23.320600 数据库验证: 删除后后 IOT_Sales 表总金额为 2444067.14
2025-06-06 09:11:23.320898 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-06 09:11:23.321151 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-06 09:11:23.368981 temp_refund_20250606091001.xlsx 第11行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-06 09:11:23.434975 temp_refund_20250606091001.xlsx 第11行 尝试通过Order_No=2025060119061929132545337257984删除记录
2025-06-06 09:11:23.538007 temp_refund_20250606091001.xlsx 第11行 严重警告: 多次尝试后记录仍未被删除！
2025-06-06 09:11:23.538520 temp_refund_20250606091001.xlsx 第11行 事务已提交，状态: 已删除
2025-06-06 09:11:23.538959 temp_refund_20250606091001.xlsx 第11行 验证成功：记录已从数据库中移除
2025-06-06 09:11:23.651454 temp_refund_20250606091001.xlsx 第11行 REFUND_LIST插入记录已提交
2025-06-06 09:11:23.651830 temp_refund_20250606091001.xlsx 第11行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:23.652600 信息: Equipment_ID 603010480 使用 Transaction Date '2025-06-01' (含时间) 进行精确匹配。
2025-06-06 09:11:23.747962 数据库验证: 删除前后 IOT_Sales 表总金额为 2444062.14
2025-06-06 09:11:23.798541 数据库验证: 删除后后 IOT_Sales 表总金额为 2444062.14
2025-06-06 09:11:23.798790 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-06 09:11:23.799001 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-06 09:11:23.845899 temp_refund_20250606091001.xlsx 第12行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-06 09:11:23.894427 temp_refund_20250606091001.xlsx 第12行 尝试通过Order_No=2025060119401929140871592734720删除记录
2025-06-06 09:11:23.984843 temp_refund_20250606091001.xlsx 第12行 严重警告: 多次尝试后记录仍未被删除！
2025-06-06 09:11:23.985055 temp_refund_20250606091001.xlsx 第12行 事务已提交，状态: 已删除
2025-06-06 09:11:23.985261 temp_refund_20250606091001.xlsx 第12行 验证成功：记录已从数据库中移除
2025-06-06 09:11:23.990860 temp_refund_20250606091001.xlsx 第12行 REFUND_LIST插入记录已提交
2025-06-06 09:11:23.991215 temp_refund_20250606091001.xlsx 第12行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:40.142146 信息: Equipment_ID 603010828 使用 Transaction Date '2025-05-31' (含时间) 进行精确匹配。
2025-06-06 09:11:40.168794 数据库验证: 删除前后 ZERO_Sales 表总金额为 471074.31
2025-06-06 09:11:40.178279 数据库验证: 删除后后 ZERO_Sales 表总金额为 471074.31
2025-06-06 09:11:40.178669 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-06 09:11:40.179026 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-06 09:11:40.193897 temp_refund_20250606091126.xlsx 第1行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-06 09:11:40.216665 temp_refund_20250606091126.xlsx 第1行 尝试通过Order_No=2025053120171928788044605353984删除记录
2025-06-06 09:11:40.243818 temp_refund_20250606091126.xlsx 第1行 严重警告: 多次尝试后记录仍未被删除！
2025-06-06 09:11:40.244108 temp_refund_20250606091126.xlsx 第1行 事务已提交，状态: 已删除
2025-06-06 09:11:40.244372 temp_refund_20250606091126.xlsx 第1行 验证成功：记录已从数据库中移除
2025-06-06 09:11:40.251121 temp_refund_20250606091126.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-06 09:11:40.251833 temp_refund_20250606091126.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-06 09:11:40.253882 信息: Equipment_ID 603010905 使用 Transaction Date '2025-05-31' (含时间) 进行精确匹配。
2025-06-06 09:11:40.278783 数据库验证: 删除前后 ZERO_Sales 表总金额为 471069.31
2025-06-06 09:11:40.295783 数据库验证: 删除后后 ZERO_Sales 表总金额为 471069.31
2025-06-06 09:11:40.296079 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-06 09:11:40.296396 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-06 09:11:40.307797 temp_refund_20250606091126.xlsx 第2行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-06 09:11:40.331449 temp_refund_20250606091126.xlsx 第2行 尝试通过Order_No=2025053120101928786205935726592删除记录
2025-06-06 09:11:40.360574 temp_refund_20250606091126.xlsx 第2行 严重警告: 多次尝试后记录仍未被删除！
2025-06-06 09:11:40.360818 temp_refund_20250606091126.xlsx 第2行 事务已提交，状态: 已删除
2025-06-06 09:11:40.361169 temp_refund_20250606091126.xlsx 第2行 验证成功：记录已从数据库中移除
2025-06-06 09:11:40.367643 temp_refund_20250606091126.xlsx 第2行 REFUND_LIST插入记录已提交
2025-06-06 09:11:40.368305 temp_refund_20250606091126.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-09 14:29:23.652850 信息: Equipment_ID 603010982 使用 Transaction Date '2025-05-31' (含时间) 进行精确匹配。
2025-06-09 14:29:23.757247 数据库验证: 删除前后 IOT_Sales 表总金额为 2502014.53
2025-06-09 14:29:23.809222 数据库验证: 删除后后 IOT_Sales 表总金额为 2502014.53
2025-06-09 14:29:23.809434 数据库验证: 预期金额变化 1.0, 实际金额变化 0.0
2025-06-09 14:29:23.809562 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-09 14:29:23.864562 temp_refund_20250609142804.xlsx 第1行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-09 14:29:23.918423 temp_refund_20250609142804.xlsx 第1行 尝试通过Order_No=2025053110071928634463315095552删除记录
2025-06-09 14:29:24.023234 temp_refund_20250609142804.xlsx 第1行 最终删除验证通过: 记录已成功删除
2025-06-09 14:29:24.023573 temp_refund_20250609142804.xlsx 第1行 事务已提交，状态: 已删除
2025-06-09 14:29:24.023794 temp_refund_20250609142804.xlsx 第1行 验证成功：记录已从数据库中移除
2025-06-09 14:29:24.031041 temp_refund_20250609142804.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-09 14:29:24.031546 temp_refund_20250609142804.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-09 14:29:24.032384 信息: Equipment_ID 603011162 使用 Transaction Date '2025-06-01' (含时间) 进行精确匹配。
2025-06-09 14:29:24.131690 数据库验证: 更新前 - temp_refund_20250609142804.xlsx 第2行后 IOT_Sales 表总金额为 2502013.53
2025-06-09 14:29:24.179994 数据库验证: 更新后 - temp_refund_20250609142804.xlsx 第2行后 IOT_Sales 表总金额为 2502013.53
2025-06-09 14:29:24.180230 数据库验证: 预期金额变化 -10.0, 实际金额变化 0.0
2025-06-09 14:29:24.180376 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-06-09 14:29:24.180601 temp_refund_20250609142804.xlsx 第2行 更新验证失败: 期望=10.0, 实际=NULL
2025-06-09 14:29:24.180790 temp_refund_20250609142804.xlsx 第2行 事务已提交，状态: 已退款
2025-06-09 14:29:24.187215 temp_refund_20250609142804.xlsx 第2行 REFUND_LIST插入记录已提交
2025-06-09 14:29:24.187547 temp_refund_20250609142804.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-09 14:29:24.188256 信息: Equipment_ID 603011351 使用 Transaction Date '2025-05-31' (含时间) 进行精确匹配。
2025-06-09 14:29:24.284164 数据库验证: 删除前后 IOT_Sales 表总金额为 2502013.53
2025-06-09 14:29:24.334063 数据库验证: 删除后后 IOT_Sales 表总金额为 2502013.53
2025-06-09 14:29:24.334344 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-09 14:29:24.334490 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-09 14:29:24.389311 temp_refund_20250609142804.xlsx 第3行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-09 14:29:24.450428 temp_refund_20250609142804.xlsx 第3行 尝试通过Order_No=2025053116511928736196989087744删除记录
2025-06-09 14:29:24.553895 temp_refund_20250609142804.xlsx 第3行 严重警告: 多次尝试后记录仍未被删除！
2025-06-09 14:29:24.554145 temp_refund_20250609142804.xlsx 第3行 事务已提交，状态: 已删除
2025-06-09 14:29:24.554382 temp_refund_20250609142804.xlsx 第3行 验证成功：记录已从数据库中移除
2025-06-09 14:29:24.560671 temp_refund_20250609142804.xlsx 第3行 REFUND_LIST插入记录已提交
2025-06-09 14:29:24.561133 temp_refund_20250609142804.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-09 14:29:24.562050 信息: Order No. 2025060217381929472720621858816 使用 Transaction Date '2025-06-02' (含时间) 进行精确匹配。
2025-06-09 14:29:24.656658 数据库验证: 删除前后 IOT_Sales 表总金额为 2502008.53
2025-06-09 14:29:24.708532 数据库验证: 删除后后 IOT_Sales 表总金额为 2502008.53
2025-06-09 14:29:24.708733 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-09 14:29:24.708874 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-09 14:29:24.752569 temp_refund_20250609142804.xlsx 第4行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-09 14:29:24.812713 temp_refund_20250609142804.xlsx 第4行 尝试通过Order_No=2025060217381929472720621858816删除记录
2025-06-09 14:29:24.915517 temp_refund_20250609142804.xlsx 第4行 严重警告: 多次尝试后记录仍未被删除！
2025-06-09 14:29:24.915706 temp_refund_20250609142804.xlsx 第4行 事务已提交，状态: 已删除
2025-06-09 14:29:24.915905 temp_refund_20250609142804.xlsx 第4行 验证成功：记录已从数据库中移除
2025-06-09 14:29:24.921249 temp_refund_20250609142804.xlsx 第4行 REFUND_LIST插入记录已提交
2025-06-09 14:29:24.921570 temp_refund_20250609142804.xlsx 第4行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-09 14:29:24.922274 信息: Order No. 2025060217441929474201559298048 使用 Transaction Date '2025-06-02' (含时间) 进行精确匹配。
2025-06-09 14:29:25.022117 数据库验证: 删除前后 IOT_Sales 表总金额为 2502003.53
2025-06-09 14:29:25.088570 数据库验证: 删除后后 IOT_Sales 表总金额为 2502003.53
2025-06-09 14:29:25.088844 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-09 14:29:25.089016 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-09 14:29:25.152122 temp_refund_20250609142804.xlsx 第5行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-09 14:29:25.219437 temp_refund_20250609142804.xlsx 第5行 尝试通过Order_No=2025060217441929474201559298048删除记录
2025-06-09 14:29:25.333099 temp_refund_20250609142804.xlsx 第5行 严重警告: 多次尝试后记录仍未被删除！
2025-06-09 14:29:25.333324 temp_refund_20250609142804.xlsx 第5行 事务已提交，状态: 已删除
2025-06-09 14:29:25.333567 temp_refund_20250609142804.xlsx 第5行 验证成功：记录已从数据库中移除
2025-06-09 14:29:25.340145 temp_refund_20250609142804.xlsx 第5行 REFUND_LIST插入记录已提交
2025-06-09 14:29:25.340813 temp_refund_20250609142804.xlsx 第5行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-09 14:29:25.341827 信息: Order No. 2025060218071929480035785830400 使用 Transaction Date '2025-06-02' (含时间) 进行精确匹配。
2025-06-09 14:29:25.450881 数据库验证: 删除前后 IOT_Sales 表总金额为 2501998.53
2025-06-09 14:29:25.503664 数据库验证: 删除后后 IOT_Sales 表总金额为 2501998.53
2025-06-09 14:29:25.503903 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-09 14:29:25.504022 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-09 14:29:25.552594 temp_refund_20250609142804.xlsx 第6行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-09 14:29:25.624293 temp_refund_20250609142804.xlsx 第6行 尝试通过Order_No=2025060218071929480035785830400删除记录
2025-06-09 14:29:25.742930 temp_refund_20250609142804.xlsx 第6行 严重警告: 多次尝试后记录仍未被删除！
2025-06-09 14:29:25.743296 temp_refund_20250609142804.xlsx 第6行 事务已提交，状态: 已删除
2025-06-09 14:29:25.743579 temp_refund_20250609142804.xlsx 第6行 验证成功：记录已从数据库中移除
2025-06-09 14:29:25.751956 temp_refund_20250609142804.xlsx 第6行 REFUND_LIST插入记录已提交
2025-06-09 14:29:25.752405 temp_refund_20250609142804.xlsx 第6行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-09 14:30:52.869791 信息: Equipment_ID 603010369 使用 Transaction Date '2025-05-23' (含时间) 进行精确匹配。
2025-06-09 14:30:52.982559 数据库验证: 删除前后 IOT_Sales 表总金额为 2501993.53
2025-06-09 14:30:53.043633 数据库验证: 删除后后 IOT_Sales 表总金额为 2501993.53
2025-06-09 14:30:53.043995 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-09 14:30:53.044129 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-09 14:30:53.102374 temp_refund_20250609142928.xlsx 第1行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-09 14:30:53.170944 temp_refund_20250609142928.xlsx 第1行 尝试通过Order_No=2025052318251925860639725121536删除记录
2025-06-09 14:30:53.279946 temp_refund_20250609142928.xlsx 第1行 严重警告: 多次尝试后记录仍未被删除！
2025-06-09 14:30:53.280209 temp_refund_20250609142928.xlsx 第1行 事务已提交，状态: 已删除
2025-06-09 14:30:53.280423 temp_refund_20250609142928.xlsx 第1行 验证成功：记录已从数据库中移除
2025-06-09 14:30:53.287145 temp_refund_20250609142928.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-09 14:30:53.287706 temp_refund_20250609142928.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-09 14:30:53.288666 信息: Order No. 2025052902441927798214605991936 使用 Transaction Date '2025-05-29' (含时间) 进行精确匹配。
2025-06-09 14:30:53.405477 数据库验证: 删除前后 IOT_Sales 表总金额为 2501988.53
2025-06-09 14:30:53.456774 数据库验证: 删除后后 IOT_Sales 表总金额为 2501988.53
2025-06-09 14:30:53.457068 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-09 14:30:53.457260 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-09 14:30:53.513530 temp_refund_20250609142928.xlsx 第2行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-09 14:30:53.579433 temp_refund_20250609142928.xlsx 第2行 尝试通过Order_No=2025052902441927798214605991936删除记录
2025-06-09 14:30:53.699182 temp_refund_20250609142928.xlsx 第2行 严重警告: 多次尝试后记录仍未被删除！
2025-06-09 14:30:53.699563 temp_refund_20250609142928.xlsx 第2行 事务已提交，状态: 已删除
2025-06-09 14:30:53.699857 temp_refund_20250609142928.xlsx 第2行 验证成功：记录已从数据库中移除
2025-06-09 14:30:53.706429 temp_refund_20250609142928.xlsx 第2行 REFUND_LIST插入记录已提交
2025-06-09 14:30:53.706874 temp_refund_20250609142928.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-10 10:08:05.538782 信息: Equipment_ID 603010646 使用 Transaction Date '2025-06-03' (含时间) 进行精确匹配。
2025-06-10 10:08:05.661900 数据库验证: 删除前后 IOT_Sales 表总金额为 2530426.19
2025-06-10 10:08:05.710888 数据库验证: 删除后后 IOT_Sales 表总金额为 2530426.19
2025-06-10 10:08:05.711125 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-10 10:08:05.711442 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-10 10:08:05.768570 temp_refund_20250610100652.xlsx 第1行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-10 10:08:05.838001 temp_refund_20250610100652.xlsx 第1行 尝试通过Order_No=2025060321291929893295395762176删除记录
2025-06-10 10:08:05.945889 temp_refund_20250610100652.xlsx 第1行 严重警告: 多次尝试后记录仍未被删除！
2025-06-10 10:08:05.946189 temp_refund_20250610100652.xlsx 第1行 事务已提交，状态: 已删除
2025-06-10 10:08:05.946500 temp_refund_20250610100652.xlsx 第1行 验证成功：记录已从数据库中移除
2025-06-10 10:08:05.953643 temp_refund_20250610100652.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-10 10:08:05.954378 temp_refund_20250610100652.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-10 10:08:05.955397 信息: Equipment_ID 603010571 使用 Transaction Date '2025-06-04' (含时间) 进行精确匹配。
2025-06-10 10:08:06.063792 数据库验证: 删除前后 IOT_Sales 表总金额为 2530421.19
2025-06-10 10:08:06.114613 数据库验证: 删除后后 IOT_Sales 表总金额为 2530421.19
2025-06-10 10:08:06.114945 数据库验证: 预期金额变化 1.0, 实际金额变化 0.0
2025-06-10 10:08:06.115197 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-10 10:08:06.173313 temp_refund_20250610100652.xlsx 第2行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-10 10:08:06.228516 temp_refund_20250610100652.xlsx 第2行 尝试通过Order_No=2025060416401930182937064042496删除记录
2025-06-10 10:08:06.339486 temp_refund_20250610100652.xlsx 第2行 严重警告: 多次尝试后记录仍未被删除！
2025-06-10 10:08:06.339715 temp_refund_20250610100652.xlsx 第2行 事务已提交，状态: 已删除
2025-06-10 10:08:06.340157 temp_refund_20250610100652.xlsx 第2行 验证成功：记录已从数据库中移除
2025-06-10 10:08:06.347332 temp_refund_20250610100652.xlsx 第2行 REFUND_LIST插入记录已提交
2025-06-10 10:08:06.347883 temp_refund_20250610100652.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-10 10:08:06.348623 信息: Equipment_ID 603010299 使用 Transaction Date '2025-06-04' (含时间) 进行精确匹配。
2025-06-10 10:08:06.447082 数据库验证: 删除前后 IOT_Sales 表总金额为 2530420.19
2025-06-10 10:08:06.495793 数据库验证: 删除后后 IOT_Sales 表总金额为 2530420.19
2025-06-10 10:08:06.496009 数据库验证: 预期金额变化 1.0, 实际金额变化 0.0
2025-06-10 10:08:06.496226 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-10 10:08:06.550422 temp_refund_20250610100652.xlsx 第3行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-10 10:08:06.616233 temp_refund_20250610100652.xlsx 第3行 尝试通过Order_No=2025060416031930173538593468416删除记录
2025-06-10 10:08:06.738774 temp_refund_20250610100652.xlsx 第3行 严重警告: 多次尝试后记录仍未被删除！
2025-06-10 10:08:06.739164 temp_refund_20250610100652.xlsx 第3行 事务已提交，状态: 已删除
2025-06-10 10:08:06.739656 temp_refund_20250610100652.xlsx 第3行 验证成功：记录已从数据库中移除
2025-06-10 10:08:06.746848 temp_refund_20250610100652.xlsx 第3行 REFUND_LIST插入记录已提交
2025-06-10 10:08:06.747666 temp_refund_20250610100652.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-10 10:08:06.748841 信息: Equipment_ID 603010314 使用 Transaction Date '2025-06-04' (含时间) 进行精确匹配。
2025-06-10 10:08:06.857491 数据库验证: 删除前后 IOT_Sales 表总金额为 2530419.19
2025-06-10 10:08:06.905191 数据库验证: 删除后后 IOT_Sales 表总金额为 2530419.19
2025-06-10 10:08:06.905435 数据库验证: 预期金额变化 1.0, 实际金额变化 0.0
2025-06-10 10:08:06.905758 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-10 10:08:06.971524 temp_refund_20250610100652.xlsx 第4行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-10 10:08:07.037744 temp_refund_20250610100652.xlsx 第4行 尝试通过Order_No=2025060416011930173119846739968删除记录
2025-06-10 10:08:07.138748 temp_refund_20250610100652.xlsx 第4行 严重警告: 多次尝试后记录仍未被删除！
2025-06-10 10:08:07.139042 temp_refund_20250610100652.xlsx 第4行 事务已提交，状态: 已删除
2025-06-10 10:08:07.139460 temp_refund_20250610100652.xlsx 第4行 验证成功：记录已从数据库中移除
2025-06-10 10:08:07.147243 temp_refund_20250610100652.xlsx 第4行 REFUND_LIST插入记录已提交
2025-06-10 10:08:07.148260 temp_refund_20250610100652.xlsx 第4行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-10 10:08:07.149356 信息: Equipment_ID 603011321 使用 Transaction Date '2025-06-04' (含时间) 进行精确匹配。
2025-06-10 10:08:07.249501 数据库验证: 删除前后 IOT_Sales 表总金额为 2530418.19
2025-06-10 10:08:07.302877 数据库验证: 删除后后 IOT_Sales 表总金额为 2530418.19
2025-06-10 10:08:07.303079 数据库验证: 预期金额变化 1.0, 实际金额变化 0.0
2025-06-10 10:08:07.303354 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-10 10:08:07.363471 temp_refund_20250610100652.xlsx 第5行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-10 10:08:07.434595 temp_refund_20250610100652.xlsx 第5行 尝试通过Order_No=2025060415551930171475838955520删除记录
2025-06-10 10:08:07.553530 temp_refund_20250610100652.xlsx 第5行 严重警告: 多次尝试后记录仍未被删除！
2025-06-10 10:08:07.553753 temp_refund_20250610100652.xlsx 第5行 事务已提交，状态: 已删除
2025-06-10 10:08:07.554058 temp_refund_20250610100652.xlsx 第5行 验证成功：记录已从数据库中移除
2025-06-10 10:08:07.561156 temp_refund_20250610100652.xlsx 第5行 REFUND_LIST插入记录已提交
2025-06-10 10:08:07.561750 temp_refund_20250610100652.xlsx 第5行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-10 10:08:07.562503 信息: Equipment_ID 603010504 使用 Transaction Date '2025-06-03' (含时间) 进行精确匹配。
2025-06-10 10:08:07.677402 数据库验证: 删除前后 IOT_Sales 表总金额为 2530417.19
2025-06-10 10:08:07.740010 数据库验证: 删除后后 IOT_Sales 表总金额为 2530417.19
2025-06-10 10:08:07.740292 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-10 10:08:07.740710 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-10 10:08:07.793579 temp_refund_20250610100652.xlsx 第6行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-10 10:08:07.861688 temp_refund_20250610100652.xlsx 第6行 尝试通过Order_No=2025060320021929871191493373952删除记录
2025-06-10 10:08:07.979444 temp_refund_20250610100652.xlsx 第6行 严重警告: 多次尝试后记录仍未被删除！
2025-06-10 10:08:07.979729 temp_refund_20250610100652.xlsx 第6行 事务已提交，状态: 已删除
2025-06-10 10:08:07.980046 temp_refund_20250610100652.xlsx 第6行 验证成功：记录已从数据库中移除
2025-06-10 10:08:07.987394 temp_refund_20250610100652.xlsx 第6行 REFUND_LIST插入记录已提交
2025-06-10 10:08:07.988053 temp_refund_20250610100652.xlsx 第6行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-10 10:08:07.988874 信息: Equipment_ID 603011072 使用 Transaction Date '2025-05-09' (含时间) 进行精确匹配。
2025-06-10 10:08:08.085866 数据库验证: 删除前后 IOT_Sales 表总金额为 2530412.19
2025-06-10 10:08:08.161178 数据库验证: 删除后后 IOT_Sales 表总金额为 2530412.19
2025-06-10 10:08:08.161499 数据库验证: 预期金额变化 10.0, 实际金额变化 0.0
2025-06-10 10:08:08.162074 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-10 10:08:08.220541 temp_refund_20250610100652.xlsx 第7行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-10 10:08:08.287933 temp_refund_20250610100652.xlsx 第7行 尝试通过Order_No=2025050918241920786986532663296删除记录
2025-06-10 10:08:08.387396 temp_refund_20250610100652.xlsx 第7行 严重警告: 多次尝试后记录仍未被删除！
2025-06-10 10:08:08.387608 temp_refund_20250610100652.xlsx 第7行 事务已提交，状态: 已删除
2025-06-10 10:08:08.387959 temp_refund_20250610100652.xlsx 第7行 验证成功：记录已从数据库中移除
2025-06-10 10:08:08.394764 temp_refund_20250610100652.xlsx 第7行 REFUND_LIST插入记录已提交
2025-06-10 10:08:08.395422 temp_refund_20250610100652.xlsx 第7行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-10 10:08:08.396417 信息: Equipment_ID 603010069 使用 Transaction Date '2025-05-24' (含时间) 进行精确匹配。
2025-06-10 10:08:08.497562 数据库验证: 删除前后 IOT_Sales 表总金额为 2530402.19
2025-06-10 10:08:08.546898 数据库验证: 删除后后 IOT_Sales 表总金额为 2530402.19
2025-06-10 10:08:08.547147 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-10 10:08:08.547548 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-10 10:08:08.599169 temp_refund_20250610100652.xlsx 第8行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-10 10:08:08.667582 temp_refund_20250610100652.xlsx 第8行 尝试通过Order_No=2025052419441926242969195180032删除记录
2025-06-10 10:08:08.790337 temp_refund_20250610100652.xlsx 第8行 严重警告: 多次尝试后记录仍未被删除！
2025-06-10 10:08:08.790848 temp_refund_20250610100652.xlsx 第8行 事务已提交，状态: 已删除
2025-06-10 10:08:08.791400 temp_refund_20250610100652.xlsx 第8行 验证成功：记录已从数据库中移除
2025-06-10 10:08:08.799911 temp_refund_20250610100652.xlsx 第8行 REFUND_LIST插入记录已提交
2025-06-10 10:08:08.800904 temp_refund_20250610100652.xlsx 第8行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-10 10:08:08.802108 信息: Equipment_ID 603011344 使用 Transaction Date '2025-06-04' (含时间) 进行精确匹配。
2025-06-10 10:08:08.921818 数据库验证: 删除前后 IOT_Sales 表总金额为 2530397.19
2025-06-10 10:08:08.990947 数据库验证: 删除后后 IOT_Sales 表总金额为 2530397.19
2025-06-10 10:08:08.991189 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-10 10:08:08.991442 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-10 10:08:09.038477 temp_refund_20250610100652.xlsx 第9行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-10 10:08:09.113420 temp_refund_20250610100652.xlsx 第9行 尝试通过Order_No=2025060422581930278087253618688删除记录
2025-06-10 10:08:09.222993 temp_refund_20250610100652.xlsx 第9行 严重警告: 多次尝试后记录仍未被删除！
2025-06-10 10:08:09.223257 temp_refund_20250610100652.xlsx 第9行 事务已提交，状态: 已删除
2025-06-10 10:08:09.223563 temp_refund_20250610100652.xlsx 第9行 验证成功：记录已从数据库中移除
2025-06-10 10:08:09.231127 temp_refund_20250610100652.xlsx 第9行 REFUND_LIST插入记录已提交
2025-06-10 10:08:09.232086 temp_refund_20250610100652.xlsx 第9行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-10 10:08:09.233434 信息: Equipment_ID 603010584 使用 Transaction Date '2025-06-04' (含时间) 进行精确匹配。
2025-06-10 10:08:09.380947 数据库验证: 删除前后 IOT_Sales 表总金额为 2530392.19
2025-06-10 10:08:09.436751 数据库验证: 删除后后 IOT_Sales 表总金额为 2530392.19
2025-06-10 10:08:09.437035 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-10 10:08:09.437410 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-10 10:08:09.497262 temp_refund_20250610100652.xlsx 第10行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-10 10:08:09.588815 temp_refund_20250610100652.xlsx 第10行 尝试通过Order_No=2025060419261930224632191315968删除记录
2025-06-10 10:08:09.745132 temp_refund_20250610100652.xlsx 第10行 严重警告: 多次尝试后记录仍未被删除！
2025-06-10 10:08:09.745428 temp_refund_20250610100652.xlsx 第10行 事务已提交，状态: 已删除
2025-06-10 10:08:09.745882 temp_refund_20250610100652.xlsx 第10行 验证成功：记录已从数据库中移除
2025-06-10 10:08:09.752626 temp_refund_20250610100652.xlsx 第10行 REFUND_LIST插入记录已提交
2025-06-10 10:08:09.753390 temp_refund_20250610100652.xlsx 第10行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-11 11:13:25.345102 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:13:25.346169 数据库连接测试成功
2025-06-11 11:13:25.347261 信息: Equipment_ID TEST001 使用 Transaction Date '2024-01-15' (含时间) 进行精确匹配。
2025-06-11 11:13:25.397509 信息: Equipment_ID TEST001 在 Transaction Date '2024-01-15' 无精确时间，尝试匹配当天所有记录。
2025-06-11 11:13:25.453789 警告: 警告: Equipment_ID TEST001 在日期 2024-01-15 未找到金额匹配的记录 (已忽略时间)。退款金额: 25.0
2025-06-11 11:13:25.454010 弹窗请求: 弹窗提示需求: Equipment_ID TEST001 在日期 2024-01-15 未找到可操作记录 (金额不匹配)。
2025-06-11 11:13:25.454294 测试退款文件.xlsx 第1行 标记为未找到
2025-06-11 11:13:25.454589 测试退款文件.xlsx 第1行 '未找到'状态已更新到数据库
2025-06-11 11:13:25.460581 测试退款文件.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-11 11:13:25.461095 测试退款文件.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-11 11:14:16.910100 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:14:16.911371 数据库连接测试成功
2025-06-11 11:14:16.912731 信息: Equipment_ID TEST001 使用 Transaction Date '2025-06-11' (含时间) 进行精确匹配。
2025-06-11 11:14:17.008497 信息: Equipment_ID TEST001 在 Transaction Date '2025-06-11' 无精确时间，尝试匹配当天所有记录。
2025-06-11 11:14:17.079119 警告: 警告: Equipment_ID TEST001 在日期 2025-06-11 未找到金额匹配的记录 (已忽略时间)。退款金额: 25.0
2025-06-11 11:14:17.079368 弹窗请求: 弹窗提示需求: Equipment_ID TEST001 在日期 2025-06-11 未找到可操作记录 (金额不匹配)。
2025-06-11 11:14:17.079737 测试退款文件.xlsx 第1行 标记为未找到
2025-06-11 11:14:17.080586 测试退款文件.xlsx 第1行 '未找到'状态已更新到数据库
2025-06-11 11:14:17.087125 测试退款文件.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-11 11:14:17.088022 测试退款文件.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-11 11:14:28.692840 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:14:28.694105 数据库连接测试成功
2025-06-11 11:14:28.696024 信息: Equipment_ID TEST001 使用 Transaction Date '2025-06-11' (含时间) 进行精确匹配。
2025-06-11 11:14:28.748917 信息: Equipment_ID TEST001 在 Transaction Date '2025-06-11' 无精确时间，尝试匹配当天所有记录。
2025-06-11 11:14:28.808414 警告: 警告: Equipment_ID TEST001 在日期 2025-06-11 未找到金额匹配的记录 (已忽略时间)。退款金额: 25.0
2025-06-11 11:14:28.808616 弹窗请求: 弹窗提示需求: Equipment_ID TEST001 在日期 2025-06-11 未找到可操作记录 (金额不匹配)。
2025-06-11 11:14:28.808860 测试退款文件.xlsx 第1行 标记为未找到
2025-06-11 11:14:28.809133 测试退款文件.xlsx 第1行 '未找到'状态已更新到数据库
2025-06-11 11:14:28.823114 测试退款文件.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-11 11:14:28.823764 测试退款文件.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-11 11:18:08.728209 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:18:08.729346 数据库连接测试成功
2025-06-11 11:18:08.730526 信息: Equipment_ID TEST001 使用 Transaction Date '2025-06-11' (含时间) 进行精确匹配。
2025-06-11 11:18:08.785212 信息: Equipment_ID TEST001 在 Transaction Date '2025-06-11' 无精确时间，尝试匹配当天所有记录。
2025-06-11 11:18:08.847708 警告: 警告: Equipment_ID TEST001 在日期 2025-06-11 未找到金额匹配的记录 (已忽略时间)。退款金额: 25.0
2025-06-11 11:18:08.847910 弹窗请求: 弹窗提示需求: Equipment_ID TEST001 在日期 2025-06-11 未找到可操作记录 (金额不匹配)。
2025-06-11 11:18:08.848164 测试退款文件.xlsx 第1行 标记为未找到
2025-06-11 11:18:08.848436 测试退款文件.xlsx 第1行 '未找到'状态已更新到数据库
2025-06-11 11:18:08.854122 测试退款文件.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-11 11:18:08.855609 测试退款文件.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-11 11:20:26.982942 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:20:26.984051 数据库连接测试成功
2025-06-11 11:20:26.985344 信息: Equipment_ID TEST001 使用 Transaction Date '2025-06-11' (含时间) 进行精确匹配。
2025-06-11 11:20:27.039002 信息: Equipment_ID TEST001 在 Transaction Date '2025-06-11' 无精确时间，尝试匹配当天所有记录。
2025-06-11 11:20:27.100819 警告: 警告: Equipment_ID TEST001 在日期 2025-06-11 未找到金额匹配的记录 (已忽略时间)。退款金额: 25.0
2025-06-11 11:20:27.101050 弹窗请求: 弹窗提示需求: Equipment_ID TEST001 在日期 2025-06-11 未找到可操作记录 (金额不匹配)。
2025-06-11 11:20:27.101318 测试退款文件.xlsx 第1行 标记为未找到
2025-06-11 11:20:27.101641 测试退款文件.xlsx 第1行 '未找到'状态已更新到数据库
2025-06-11 11:20:27.107811 测试退款文件.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-11 11:20:27.108415 测试退款文件.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-11 11:21:07.712036 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:21:07.712957 数据库连接测试成功
2025-06-11 11:21:07.713809 信息: Equipment_ID TEST001 使用 Transaction Date '2025-06-11' (含时间) 进行精确匹配。
2025-06-11 11:21:07.766264 信息: Equipment_ID TEST001 在 Transaction Date '2025-06-11' 无精确时间，尝试匹配当天所有记录。
2025-06-11 11:21:07.830112 警告: 警告: Equipment_ID TEST001 在日期 2025-06-11 未找到金额匹配的记录 (已忽略时间)。退款金额: 25.0
2025-06-11 11:21:07.836388 弹窗请求: 弹窗提示需求: Equipment_ID TEST001 在日期 2025-06-11 未找到可操作记录 (金额不匹配)。
2025-06-11 11:21:07.836764 测试退款文件.xlsx 第1行 标记为未找到
2025-06-11 11:21:07.837149 测试退款文件.xlsx 第1行 '未找到'状态已更新到数据库
2025-06-11 11:21:07.843080 测试退款文件.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-11 11:21:07.843729 测试退款文件.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-11 11:21:58.917531 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:21:58.918369 数据库连接测试成功
2025-06-11 11:21:58.919162 信息: Equipment_ID TEST001 使用 Transaction Date '2025-06-11' (含时间) 进行精确匹配。
2025-06-11 11:21:58.971502 信息: Equipment_ID TEST001 在 Transaction Date '2025-06-11' 无精确时间，尝试匹配当天所有记录。
2025-06-11 11:21:59.029397 警告: 警告: Equipment_ID TEST001 在日期 2025-06-11 未找到金额匹配的记录 (已忽略时间)。退款金额: 25.0
2025-06-11 11:21:59.029606 弹窗请求: 弹窗提示需求: Equipment_ID TEST001 在日期 2025-06-11 未找到可操作记录 (金额不匹配)。
2025-06-11 11:21:59.029879 测试退款文件.xlsx 第1行 标记为未找到
2025-06-11 11:21:59.030189 测试退款文件.xlsx 第1行 '未找到'状态已更新到数据库
2025-06-11 11:21:59.046596 测试退款文件.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-11 11:21:59.047561 测试退款文件.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-11 11:27:39.213574 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:27:39.214835 数据库连接测试成功
2025-06-11 11:27:39.270094 测试退款文件.xlsx 第1行 处理异常: cannot access local variable 'is_equipment_id_search' where it is not associated with a value
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Day report 3\Refund_process_修复版.py", line 426, in process_refund_file
    if is_equipment_id_search:
       ^^^^^^^^^^^^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'is_equipment_id_search' where it is not associated with a value

2025-06-11 11:27:39.270228 测试退款文件.xlsx 第1行 事务已回滚
2025-06-11 11:29:17.800590 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:29:17.802547 数据库连接测试成功
2025-06-11 11:29:17.863993 测试退款文件.xlsx 第1行 处理异常: cannot access local variable 'exact_query_condition' where it is not associated with a value
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\Day report 3\Refund_process_修复版.py", line 465, in process_refund_file
    if exact_query_condition:
       ^^^^^^^^^^^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'exact_query_condition' where it is not associated with a value

2025-06-11 11:29:17.864182 测试退款文件.xlsx 第1行 事务已回滚
2025-06-11 11:30:41.156259 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:30:41.157260 数据库连接测试成功
2025-06-11 11:30:41.258754 数据库验证: 更新前 - 测试退款文件.xlsx 第1行后 IOT_Sales 表总金额为 2530437.19
2025-06-11 11:30:41.259119 更新操作影响行数: 0
2025-06-11 11:30:41.259250 警告: 警告: 更新操作没有影响任何行，可能更新失败
2025-06-11 11:30:41.304175 数据库验证: 更新后 - 测试退款文件.xlsx 第1行后 IOT_Sales 表总金额为 2530437.19
2025-06-11 11:30:41.304383 数据库验证: 预期金额变化 -25.0, 实际金额变化 0.0
2025-06-11 11:30:41.304507 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-06-11 11:30:41.304679 测试退款文件.xlsx 第1行 更新验证失败: 期望=25.0, 实际=NULL
2025-06-11 11:30:41.304837 测试退款文件.xlsx 第1行 事务已提交，状态: 已退款
2025-06-11 11:30:41.319227 测试退款文件.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-11 11:30:41.319862 测试退款文件.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-11 11:34:01.698248 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:34:01.699162 数据库连接测试成功
2025-06-11 11:34:01.798422 数据库验证: 更新前 - 测试退款文件.xlsx 第1行后 IOT_Sales 表总金额为 2530437.19
2025-06-11 11:34:01.798798 更新操作影响行数: 0
2025-06-11 11:34:01.798949 警告: 警告: 更新操作没有影响任何行，可能更新失败
2025-06-11 11:34:01.845406 数据库验证: 更新后 - 测试退款文件.xlsx 第1行后 IOT_Sales 表总金额为 2530437.19
2025-06-11 11:34:01.845625 数据库验证: 预期金额变化 -25.0, 实际金额变化 0.0
2025-06-11 11:34:01.845753 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-06-11 11:34:01.845926 测试退款文件.xlsx 第1行 更新验证失败: 期望=25.0, 实际=NULL
2025-06-11 11:34:01.846088 测试退款文件.xlsx 第1行 事务已提交，状态: 已退款
2025-06-11 11:34:01.861124 测试退款文件.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-11 11:34:01.862054 测试退款文件.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-11 11:34:45.957470 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:34:45.958453 数据库连接测试成功
2025-06-11 11:34:46.011798 信息: Equipment_ID TEST002 使用 Transaction Date '2025-06-11' (含时间) 进行精确匹配。
2025-06-11 11:34:46.061955 信息: Equipment_ID TEST002 在 Transaction Date '2025-06-11' 无精确时间，尝试匹配当天所有记录。
2025-06-11 11:34:46.122648 警告: 警告: Equipment_ID TEST002 在日期 2025-06-11 未找到金额匹配的记录 (已忽略时间)。退款金额: 25.0
2025-06-11 11:34:46.122943 弹窗请求: 弹窗提示需求: Equipment_ID TEST002 在日期 2025-06-11 未找到可操作记录 (金额不匹配)。
2025-06-11 11:34:46.123298 测试退款文件_无TransactionID.xlsx 第1行 标记为未找到
2025-06-11 11:34:46.123806 测试退款文件_无TransactionID.xlsx 第1行 '未找到'状态已更新到数据库
2025-06-11 11:34:46.132824 测试退款文件_无TransactionID.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-11 11:34:46.133445 测试退款文件_无TransactionID.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-11 11:38:47.262606 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:38:47.263757 数据库连接测试成功
2025-06-11 11:38:47.368579 数据库验证: 更新前 - 测试_TransactionID匹配.xlsx 第1行后 IOT_Sales 表总金额为 2530437.19
2025-06-11 11:38:47.368946 更新操作影响行数: 0
2025-06-11 11:38:47.369085 警告: 警告: 更新操作没有影响任何行，可能更新失败
2025-06-11 11:38:47.416053 数据库验证: 更新后 - 测试_TransactionID匹配.xlsx 第1行后 IOT_Sales 表总金额为 2530437.19
2025-06-11 11:38:47.416261 数据库验证: 预期金额变化 -25.0, 实际金额变化 0.0
2025-06-11 11:38:47.416391 警告: 数据库验证: 警告! 金额变化与预期不符，更新操作可能未正确执行
2025-06-11 11:38:47.416578 测试_TransactionID匹配.xlsx 第1行 更新验证失败: 期望=25.0, 实际=NULL
2025-06-11 11:38:47.416744 测试_TransactionID匹配.xlsx 第1行 事务已提交，状态: 已退款
2025-06-11 11:38:47.422885 测试_TransactionID匹配.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-11 11:38:47.423459 测试_TransactionID匹配.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-11 11:38:48.060167 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:38:48.061021 数据库连接测试成功
2025-06-11 11:38:48.143599 信息: Equipment_ID TEST_TRAD 使用 Transaction Date '2025-06-11' (含时间) 进行精确匹配。
2025-06-11 11:38:48.194892 信息: Equipment_ID TEST_TRAD 在 Transaction Date '2025-06-11' 无精确时间，尝试匹配当天所有记录。
2025-06-11 11:38:48.252929 警告: 警告: Equipment_ID TEST_TRAD 在日期 2025-06-11 未找到金额匹配的记录 (已忽略时间)。退款金额: 25.0
2025-06-11 11:38:48.253114 弹窗请求: 弹窗提示需求: Equipment_ID TEST_TRAD 在日期 2025-06-11 未找到可操作记录 (金额不匹配)。
2025-06-11 11:38:48.253370 测试_传统匹配.xlsx 第1行 标记为未找到
2025-06-11 11:38:48.253682 测试_传统匹配.xlsx 第1行 '未找到'状态已更新到数据库
2025-06-11 11:38:48.267876 测试_传统匹配.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-11 11:38:48.268494 测试_传统匹配.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-11 11:38:48.894763 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-11 11:38:48.895870 数据库连接测试成功
2025-06-11 11:38:48.951598 信息: Equipment_ID TEST_SPEC 使用 Transaction Date '2025-06-11' (含时间) 进行精确匹配。
2025-06-11 11:38:49.003821 信息: Equipment_ID TEST_SPEC 在 Transaction Date '2025-06-11' 无精确时间，尝试匹配当天所有记录。
2025-06-11 11:38:49.062964 信息: Equipment_ID TEST_SPEC 在日期 2025-06-11 找到金额匹配记录 (忽略时间)。退款金额: 25.0, 数据库金额: 25.0
2025-06-11 11:38:49.108688 数据库验证: 删除前后 IOT_Sales 表总金额为 2530512.19
2025-06-11 11:38:49.109449 删除操作影响行数: 0
2025-06-11 11:38:49.109861 警告: 警告: 删除操作没有影响任何行，可能删除失败
2025-06-11 11:38:49.159349 数据库验证: 删除后后 IOT_Sales 表总金额为 2530512.19
2025-06-11 11:38:49.159539 数据库验证: 预期金额变化 25.0, 实际金额变化 0.0
2025-06-11 11:38:49.159650 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-11 11:38:49.204147 测试_特殊处理.xlsx 第1行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-11 11:38:49.255119 测试_特殊处理.xlsx 第1行 尝试通过Order_No=TEST_ORDER_SPEC删除记录
2025-06-11 11:38:49.350816 测试_特殊处理.xlsx 第1行 最终删除验证通过: 记录已成功删除
2025-06-11 11:38:49.351018 测试_特殊处理.xlsx 第1行 事务已提交，状态: 已删除
2025-06-11 11:38:49.351189 测试_特殊处理.xlsx 第1行 验证成功：记录已从数据库中移除
2025-06-11 11:38:49.357068 测试_特殊处理.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-11 11:38:49.357689 测试_特殊处理.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:19:10.780048 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-12 15:19:10.790210 数据库连接测试成功
2025-06-12 15:19:10.792969 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:19:10.813638 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第1行 标记为未找到
2025-06-12 15:19:10.814388 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第1行 '未找到'状态已更新到数据库
2025-06-12 15:19:10.821567 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-12 15:19:10.822560 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:19:10.823537 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:19:10.840643 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第2行 标记为未找到
2025-06-12 15:19:10.841065 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第2行 '未找到'状态已更新到数据库
2025-06-12 15:19:10.847033 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第2行 REFUND_LIST插入记录已提交
2025-06-12 15:19:10.847417 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:19:10.847820 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:19:10.864051 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第3行 标记为未找到
2025-06-12 15:19:10.867985 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第3行 '未找到'状态已更新到数据库
2025-06-12 15:19:10.874010 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第3行 REFUND_LIST插入记录已提交
2025-06-12 15:19:10.874412 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:19:10.874807 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:19:10.885885 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第4行 标记为未找到
2025-06-12 15:19:10.887373 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第4行 '未找到'状态已更新到数据库
2025-06-12 15:19:10.894947 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第4行 REFUND_LIST插入记录已提交
2025-06-12 15:19:10.897138 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第4行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:19:10.898570 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:19:10.911268 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第5行 标记为未找到
2025-06-12 15:19:10.911679 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第5行 '未找到'状态已更新到数据库
2025-06-12 15:19:10.918248 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第5行 REFUND_LIST插入记录已提交
2025-06-12 15:19:10.918758 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第5行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:19:10.920195 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:19:10.939216 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第6行 标记为未找到
2025-06-12 15:19:10.939779 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第6行 '未找到'状态已更新到数据库
2025-06-12 15:19:10.947648 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第6行 REFUND_LIST插入记录已提交
2025-06-12 15:19:10.948383 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第6行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:19:10.949436 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:19:10.970320 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第7行 标记为未找到
2025-06-12 15:19:10.971334 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第7行 '未找到'状态已更新到数据库
2025-06-12 15:19:11.001783 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第7行 REFUND_LIST插入记录已提交
2025-06-12 15:19:11.005228 SETTLEMENT_REPORT_09062025_zeroiot.xlsx 第7行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:26:09.250313 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-12 15:26:09.254665 数据库连接测试成功
2025-06-12 15:26:09.256372 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:26:09.344702 SETTLEMENT_REPORT_09062025_IOT.xlsx 第1行 标记为未找到
2025-06-12 15:26:09.345310 SETTLEMENT_REPORT_09062025_IOT.xlsx 第1行 '未找到'状态已更新到数据库
2025-06-12 15:26:09.363404 SETTLEMENT_REPORT_09062025_IOT.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-12 15:26:09.364125 SETTLEMENT_REPORT_09062025_IOT.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:26:09.364561 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:26:09.430184 SETTLEMENT_REPORT_09062025_IOT.xlsx 第2行 标记为未找到
2025-06-12 15:26:09.430662 SETTLEMENT_REPORT_09062025_IOT.xlsx 第2行 '未找到'状态已更新到数据库
2025-06-12 15:26:09.436470 SETTLEMENT_REPORT_09062025_IOT.xlsx 第2行 REFUND_LIST插入记录已提交
2025-06-12 15:26:09.437056 SETTLEMENT_REPORT_09062025_IOT.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:26:09.437421 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:26:09.499148 SETTLEMENT_REPORT_09062025_IOT.xlsx 第3行 标记为未找到
2025-06-12 15:26:09.499663 SETTLEMENT_REPORT_09062025_IOT.xlsx 第3行 '未找到'状态已更新到数据库
2025-06-12 15:26:09.505587 SETTLEMENT_REPORT_09062025_IOT.xlsx 第3行 REFUND_LIST插入记录已提交
2025-06-12 15:26:09.506100 SETTLEMENT_REPORT_09062025_IOT.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:26:09.506414 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:26:09.567033 SETTLEMENT_REPORT_09062025_IOT.xlsx 第4行 标记为未找到
2025-06-12 15:26:09.567422 SETTLEMENT_REPORT_09062025_IOT.xlsx 第4行 '未找到'状态已更新到数据库
2025-06-12 15:26:09.574145 SETTLEMENT_REPORT_09062025_IOT.xlsx 第4行 REFUND_LIST插入记录已提交
2025-06-12 15:26:09.574685 SETTLEMENT_REPORT_09062025_IOT.xlsx 第4行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:26:09.575042 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:26:09.636694 SETTLEMENT_REPORT_09062025_IOT.xlsx 第5行 标记为未找到
2025-06-12 15:26:09.637075 SETTLEMENT_REPORT_09062025_IOT.xlsx 第5行 '未找到'状态已更新到数据库
2025-06-12 15:26:09.642401 SETTLEMENT_REPORT_09062025_IOT.xlsx 第5行 REFUND_LIST插入记录已提交
2025-06-12 15:26:09.643088 SETTLEMENT_REPORT_09062025_IOT.xlsx 第5行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:26:09.643433 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:26:09.705043 SETTLEMENT_REPORT_09062025_IOT.xlsx 第6行 标记为未找到
2025-06-12 15:26:09.705496 SETTLEMENT_REPORT_09062025_IOT.xlsx 第6行 '未找到'状态已更新到数据库
2025-06-12 15:26:09.712537 SETTLEMENT_REPORT_09062025_IOT.xlsx 第6行 REFUND_LIST插入记录已提交
2025-06-12 15:26:09.713710 SETTLEMENT_REPORT_09062025_IOT.xlsx 第6行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:26:09.714274 信息: Equipment_ID  使用 Transaction Date 'None' (仅日期) 进行当天匹配。
2025-06-12 15:26:09.775961 SETTLEMENT_REPORT_09062025_IOT.xlsx 第7行 标记为未找到
2025-06-12 15:26:09.776415 SETTLEMENT_REPORT_09062025_IOT.xlsx 第7行 '未找到'状态已更新到数据库
2025-06-12 15:26:09.783246 SETTLEMENT_REPORT_09062025_IOT.xlsx 第7行 REFUND_LIST插入记录已提交
2025-06-12 15:26:09.784328 SETTLEMENT_REPORT_09062025_IOT.xlsx 第7行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-12 15:38:29.827013 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-12 15:38:29.829647 数据库连接测试成功
2025-06-12 15:38:29.899217 信息: Equipment_ID 603011106 使用 Transaction Date '2025-06-08' (含时间) 进行精确匹配。
2025-06-12 15:38:29.962815 信息: Equipment_ID 603011106 在 Transaction Date '2025-06-08' 无精确时间，尝试匹配当天所有记录。
2025-06-12 15:38:30.036193 警告: 警告: Equipment_ID 603011106 在日期 2025-06-08 未找到任何记录。
2025-06-12 15:38:30.036411 弹窗请求: 弹窗提示需求: Equipment_ID 603011106 在日期 2025-06-08 未找到任何记录。
2025-06-12 15:38:30.036681 SETTLEMENT_REPORT_09062025_IOT.xlsx 第1行 标记为未找到
2025-06-12 15:38:30.036978 SETTLEMENT_REPORT_09062025_IOT.xlsx 第1行 '未找到'状态已更新到数据库
2025-06-12 15:38:30.051164 SETTLEMENT_REPORT_09062025_IOT.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-12 15:38:30.051942 SETTLEMENT_REPORT_09062025_IOT.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-17 16:14:57.776832 数据库路径验证: C:/Users/<USER>/Desktop/Day Report/database/sales_reports.db
2025-06-17 16:14:57.790428 数据库连接测试成功
2025-06-17 16:14:57.909931 数据库验证: 删除前后 IOT_Sales 表总金额为 2745966.62
2025-06-17 16:14:57.910467 删除操作影响行数: 0
2025-06-17 16:14:57.910795 警告: 警告: 删除操作没有影响任何行，可能删除失败
2025-06-17 16:14:57.968549 数据库验证: 删除后后 IOT_Sales 表总金额为 2745966.62
2025-06-17 16:14:57.968776 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-17 16:14:57.968895 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-17 16:14:58.024850 SETTLEMENT_REPORT_12062025_IOT.xlsx 第1行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-17 16:14:58.096277 SETTLEMENT_REPORT_12062025_IOT.xlsx 第1行 尝试通过Order_No=2025060816181931626916745928704删除记录
2025-06-17 16:14:58.224951 SETTLEMENT_REPORT_12062025_IOT.xlsx 第1行 严重警告: 多次尝试后记录仍未被删除！
2025-06-17 16:14:58.225467 SETTLEMENT_REPORT_12062025_IOT.xlsx 第1行 事务已提交，状态: 已删除
2025-06-17 16:14:58.225888 SETTLEMENT_REPORT_12062025_IOT.xlsx 第1行 验证成功：记录已从数据库中移除
2025-06-17 16:14:58.233245 SETTLEMENT_REPORT_12062025_IOT.xlsx 第1行 REFUND_LIST插入记录已提交
2025-06-17 16:14:58.233833 SETTLEMENT_REPORT_12062025_IOT.xlsx 第1行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-17 16:14:58.352661 数据库验证: 删除前后 IOT_Sales 表总金额为 2745961.62
2025-06-17 16:14:58.353671 删除操作影响行数: 0
2025-06-17 16:14:58.353987 警告: 警告: 删除操作没有影响任何行，可能删除失败
2025-06-17 16:14:58.410732 数据库验证: 删除后后 IOT_Sales 表总金额为 2745961.62
2025-06-17 16:14:58.410991 数据库验证: 预期金额变化 5.0, 实际金额变化 0.0
2025-06-17 16:14:58.411144 警告: 数据库验证: 警告! 金额变化与预期不符，删除操作可能未正确执行
2025-06-17 16:14:58.472394 SETTLEMENT_REPORT_12062025_IOT.xlsx 第2行 删除验证失败: 记录仍存在 (rowid验证:False, 其他键验证:True)
2025-06-17 16:14:58.599913 SETTLEMENT_REPORT_12062025_IOT.xlsx 第2行 尝试通过Order_No=2025060816051931623604747329536删除记录
2025-06-17 16:14:58.715349 SETTLEMENT_REPORT_12062025_IOT.xlsx 第2行 严重警告: 多次尝试后记录仍未被删除！
2025-06-17 16:14:58.715585 SETTLEMENT_REPORT_12062025_IOT.xlsx 第2行 事务已提交，状态: 已删除
2025-06-17 16:14:58.715770 SETTLEMENT_REPORT_12062025_IOT.xlsx 第2行 验证成功：记录已从数据库中移除
2025-06-17 16:14:58.723447 SETTLEMENT_REPORT_12062025_IOT.xlsx 第2行 REFUND_LIST插入记录已提交
2025-06-17 16:14:58.724194 SETTLEMENT_REPORT_12062025_IOT.xlsx 第2行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-17 16:14:58.787400 信息: Order No. 2025061114101932681813364924416 使用 Transaction Date '2025-06-11' (含时间) 进行精确匹配。
2025-06-17 16:14:58.843512 SETTLEMENT_REPORT_12062025_IOT.xlsx 第3行 标记为未找到
2025-06-17 16:14:58.844350 SETTLEMENT_REPORT_12062025_IOT.xlsx 第3行 '未找到'状态已更新到数据库
2025-06-17 16:14:58.852882 SETTLEMENT_REPORT_12062025_IOT.xlsx 第3行 REFUND_LIST插入记录已提交
2025-06-17 16:14:58.854616 SETTLEMENT_REPORT_12062025_IOT.xlsx 第3行 验证成功：REFUND_LIST记录已成功插入数据库
2025-06-17 16:14:58.917005 信息: Order No. 2025061114081932681293904568320 使用 Transaction Date '2025-06-11' (含时间) 进行精确匹配。
2025-06-17 16:14:58.975649 SETTLEMENT_REPORT_12062025_IOT.xlsx 第4行 标记为未找到
2025-06-17 16:14:58.976025 SETTLEMENT_REPORT_12062025_IOT.xlsx 第4行 '未找到'状态已更新到数据库
2025-06-17 16:14:58.983913 SETTLEMENT_REPORT_12062025_IOT.xlsx 第4行 REFUND_LIST插入记录已提交
2025-06-17 16:14:58.985170 SETTLEMENT_REPORT_12062025_IOT.xlsx 第4行 验证成功：REFUND_LIST记录已成功插入数据库
